<template>
  <div class="upload-container">
    <ou-drag-upload
      ref="uploadRef"
      @getData="getUploadData"
      @on-change="fileChange"
      fileType=".xls,.xlsx"
      :limit="1"
      tip="当前只支持后缀为.xls，.xlsx的文件"></ou-drag-upload>
    <!-- <ou-upload
      drag
      v-model:file-list="fileList"
      class="pn-pg-upload"
      :action="uploadUrl"
      ref="uploadRef"
      :on-remove="handleRemove"
      :accept="fileType"
      :limit="limit"
      :on-exceed="handleExceed"
      :auto-upload="false"
      :on-change="handleChange"
      :before-upload="handleBeforeUpload">
      <ou-button  plain :disabled="disabled" class="pn-info-btn">上传</ou-button>
    </ou-upload>
    <div class="pn-upload-placeholder" v-if="!fileList.length">{{ placeholder }}</div> -->
  </div>
</template>

<script setup jsx>
  import { ref, defineProps, defineEmits, defineExpose } from "vue";
  const props = defineProps({
    uploadUrl: {
      default: "plugIn/upload",
      type: String,
    },
    fileType: {
      default: ".xlsx, .xls",
      type: String,
    },
    placeholder: {
      default: "请上传",
      type: String,
    },
    disabled: {
      default: false,
      type: Boolean,
    },
    limit: {
      default: 1,
      type: Number,
    },
  });
  const uploadRef = ref(null);
  const emit = defineEmits(["on-change", "getData", "clear"]);

  const getFileData = () => {
    return uploadRef.value.getFileData();
  };
  const clear = () => {
    uploadRef.value.clearFiles();
  };
  defineExpose({
    clear,
    getFileData,
  });
</script>

<style lang="scss">
  .upload-container {
    position: relative;
    overflow: hidden;
    width: 100%;
  }
  .pn-pg-upload {
    width: 100%;
    display: flex;
  }
  .pn-pg-upload > div {
    height: 32px;
    order: 2;
  }
  .pn-pg-upload > ul {
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    min-width: 200px;
    box-sizing: border-box;
    margin-right: 4px;
    order: 1;
    flex-grow: 1 !important;
  }
  .pn-pg-upload > ul li {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 30px;
    margin-bottom: 0px;
    &:hover {
      background: #ffffff;
    }
  }
  // .pn-upload-placeholder {
  //   height: 32px;
  //   padding: 0px 10px;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   color: #999999;
  //   position: absolute;
  //   top: 0px;
  // }
</style>
