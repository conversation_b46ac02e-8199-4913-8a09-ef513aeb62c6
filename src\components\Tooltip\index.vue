<template>
  <!--  -->
  <div
    class="tooltip"
    style="overflow: hidden; text-overflow: ellipsis; white-space: normal; word-break: break-all; width: 100%">
    <el-tooltip
      popper-class="tooltips"
      v-if="!isSafari"
      :effect="effect"
      :disabled="isShowTooltip"
      :content="content"
      :placement="placement"
      :lineClamp="lineClamp"
      :show-arrow="false">
      <div
        :style="{ width: width }"
        class="line1 textOut"
        :class="{ textwrap: isSafari }"
        @mouseover="onMouseOver(refName)">
        <span class="tooltip_content" :ref="setItemRef">
          <slot>
            {{ content || "" }}
          </slot>
        </span>
      </div>
      <template #content v-if="isSlotContent">
        <slot name="content">
          {{ content || "" }}
        </slot>
      </template>
    </el-tooltip>
    <span style="overflow: hidden; text-overflow: ellipsis; white-space: normal; word-break: break-all" v-else>
      {{ content || "" }}
    </span>
  </div>
</template>

<script setup>
  import { ref, defineProps } from "vue";
  const props = defineProps({
    // tooltip内是否插槽
    isSlotContent: {
      type: Boolean,
      default: false,
    },
    // 显示的文字内容
    content: {
      type: String,
      default: "",
    },
    // 显示位置 同el-tooltip
    placement: {
      type: String,
      default: "bottom-start",
    },
    // tooltip主题 同el-tooltip
    effect: {
      type: String,
      default: "light",
    },
    width: {
      type: String,
      default: "",
    },
    // // 外层框的样式，在传入的这个类名中设置文字显示的宽度
    // className: {
    //   type: String,
    //   default: "w85",
    // },
    // 为页面文字标识（如在同一页面中调用多次组件，此参数不可重复）
    refName: {
      type: String,
      default: "",
    },
    lineClamp: {
      type: Number,
      default: 1,
    },
  });

  const iframeRefs = ref({});
  const setItemRef = (el, key) => {
    if (el) {
      iframeRefs.value[props.refName] = el;
    }
  };
  const isShowTooltip = ref(true);
  let isSafari = ref(false);

  if (!(/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))) {
  } else {
    isSafari.value = true;
  }
  const onMouseOver = str => {
    let parentWidth = iframeRefs.value[str].parentNode.offsetWidth;
    let contentWidth = iframeRefs.value[str].offsetWidth;
    // 判断是否开启tooltip功能
    if (contentWidth > parentWidth) {
      isShowTooltip.value = false;
    } else {
      isShowTooltip.value = true;
    }
  };
</script>

<style lang="scss" scoped>
  .tooltip_content {
    color: #333 !important;
  }
  .textOut {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .textwrap {
    white-space: normal !important;
  }
  // .line1 {
  //   width: calc(100% - 20px);
  // }
</style>
<style lang="scss">
  .tooltips {
    max-width: 350px;
    background: #ffffff;
    box-shadow:
      0px 12px 48px 16px rgba(0, 0, 0, 0.03),
      0px 9px 28px 0px rgba(0, 0, 0, 0.05),
      0px 6px 16px -8px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    border: none !important;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
  }
</style>
