<template>
  <!--  数据访问控制创建配置 -->
  <div class="plugin-dataControl-creat-configuration add_conent_flex1">
    <ou-form ref="formRef" class="-mb-20" :rules="formRules" :model="form" label-width="140px">
      <ou-form-item label="配置规则：" prop="configRule">
        <div class="config-rule-container">
          <div class="btn-row">
            <ou-button @click="addDialogOpen" type="primary" text :disabled="form.configRule.length >= 100">
              添加
            </ou-button>
            <ou-button @click="batchesDelClick" type="primary" text :disabled="!selectionsList.length">
              批量删除
            </ou-button>
          </div>
          <ou-table
            ref="tableRef"
            :data="form.configRule"
            :height="!form.configRule.length ? 200 : form.configRule.length >= 5 ? 240 : 'auto'"
            class="editor"
            style="width: 100%; --table-empty-size: 120px"
            @select="value => handleSelectionChange(value)"
            @select-all="value => handleSelectionChange(value)">
            <ou-table-column type="selection" width="32" align="center" :selectable="row => !row.isDefault" />
            <ou-table-column type="index" label="序号" width="44" align="center" />
            <ou-table-column prop="rule_name" label="规则名称">
              <template #default="scope">
                <ou-text-ellipsis :content="String(scope.row.rule_name)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="match_data" label="匹配数据">
              <template #default="scope">
                <ou-text-ellipsis :content="matchDataTextInfo(scope.row.match_data)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="matchers" label="匹配条件">
              <template #default="scope">
                <ou-text-ellipsis :content="getTextInfo(scope.row.matchers)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="act" label="动作">
              <template #default="scope">
                <ou-text-ellipsis :content="getOptionsLabel(scope.row.act, actOptions) || '-'"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="enable" label="状态" width="106">
              <template #default="scope">
                <ou-switch
                  v-model="scope.row.enable"
                  active-text="启用"
                  inactive-text="禁用"
                  class="switch-offside"
                  size="small"></ou-switch>
              </template>
            </ou-table-column>
            <ou-table-column prop="address" label="操作" width="176">
              <template #default="scope">
                <ou-button @click="updateClick(scope.$index, scope.row)" type="primary" text>编辑</ou-button>
                <ou-button v-if="!scope.row.isDefault" @click="delClick(scope.$index)" type="primary" text>
                  删除
                </ou-button>
              </template>
            </ou-table-column>
          </ou-table>
        </div>
      </ou-form-item>
    </ou-form>
    <ou-opera-dialog
      :title="`${editType}配置规则`"
      v-model="dialogShow"
      @closed="dialogClose"
      @confirm="dialogConfirm"
      :close-on-click-modal="false"
      :width="1139"
      draggable>
      <template #content>
        <ou-form ref="dialogFormRef" class="-mb-20" :rules="dialogFormRules" :model="dialogForm" label-width="120px">
          <ou-form-item label="规则名称：" prop="rule_name">
            <ou-input
              v-model.trim="dialogForm.rule_name"
              placeholder="请输入"
              maxlength="63"
              show-word-limit
              :disabled="dialogForm.isDefault"
              style="width: 300px"></ou-input>
          </ou-form-item>
          <ou-form-item label="匹配数据：" prop="match_data">
            <ou-collapse-panel class="matchedConditionEditorCollapse" v-model:isCollapse="matchDataCollapse">
              <template #headerLeft>
                <span class="header-hint">多个条件同时满足才执行对应动作</span>
              </template>
              <template #content>
                <ou-cascade-editor
                  v-if="dialogShow"
                  ref="matchingDataFormRef"
                  v-model="dialogForm.match_data"
                  :form="matchingDataFormConfig"
                  @cellFocus="index => (activeMatchDataIndex = index)"
                  :addBtnDisabled="() => dialogForm.match_data.length >= 2"
                  :beforeAdd="matchDataBeforeAdd"></ou-cascade-editor>
              </template>
            </ou-collapse-panel>
          </ou-form-item>
          <ou-form-item label="匹配条件：" prop="matchers">
            <ou-collapse-panel class="matchedConditionEditorCollapse" v-model:isCollapse="matchConditionCollapse">
              <template #headerLeft>
                <span class="header-hint">多个条件同时满足才执行对应动作</span>
              </template>
              <template #content>
                <matchedConditionEditor
                  v-if="dialogShow"
                  ref="matchedConditionEditorRef"
                  v-model="dialogForm.matchers"
                  :fieldOptions="['srcIp', 'path', 'queryParameter', 'userName', 'region', 'time']"
                  style="width: 100%"></matchedConditionEditor>
              </template>
            </ou-collapse-panel>
          </ou-form-item>
          <ou-form-item label="动作：" prop="act" class="line-height-22">
            <ou-radio-group v-model="dialogForm.act">
              <ou-radio v-for="item in actOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </ou-radio>
            </ou-radio-group>
          </ou-form-item>

          <template v-if="dialogForm.act === 1">
            <ou-form-item label="类型：" prop="deny_type">
              <ou-select v-model="dialogForm.deny_type" placeholder="请选择" style="width: 300px" :clearable="false">
                <ou-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value" />
              </ou-select>
            </ou-form-item>
            <ou-form-item label="响应内容：" prop="deny_context">
              <div class="return-page-area-content">
                <div v-if="[2, 3].includes(dialogForm.deny_type)" class="content-header">
                  <div @click="denyContextPreviewOpen" class="preview-btn">
                    <ou-icon size="16"><YanjingKai /></ou-icon>
                    <span>预览</span>
                  </div>
                </div>
                <ou-input
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 3, maxRows: 9 }"
                  placeholder="请输入返回页面内容，不填写则显示默认内容"
                  v-model="dialogForm.deny_context"
                  style="width: 100%"></ou-input>
              </div>
            </ou-form-item>
          </template>
        </ou-form>
      </template>
    </ou-opera-dialog>
    <ou-opera-dialog
      title="预览"
      v-model="denyContextPreviewShow"
      width="1200px"
      :close-on-click-modal="false"
      @close="denyContextPreviewClose"
      @cancel="denyContextPreviewClose"
      draggable
      hideFooter
      class="aac--denyContext-preview-dialog">
      <template #content>
        <div class="denyContext-preview-content">
          <ou-scrollbar>
            <div v-if="dialogForm.deny_type === 2" v-html="dialogForm.deny_context" class="html-content"></div>
            <pre v-if="dialogForm.deny_type === 3" v-html="denyContextJSON" class="json-content"></pre>
          </ou-scrollbar>
        </div>
      </template>
      <template #footer>
        <ou-button @click="denyContextPreviewClose">关闭</ou-button>
      </template>
    </ou-opera-dialog>
  </div>
</template>
<script setup>
  import { defineProps, defineExpose, computed } from "vue";
  import matchedConditionEditor from "@/components/NewMatchedConditionEditor/index.vue";
  import { OuModal } from "@ouryun/ouryun-plus";
  import { getTextInfo, getMatchParameter, compatibleMatchingList } from "@/utils/MatchedCondition.js";
  import { getMatchDataParameter, getMatchDataTextInfo, getPatternParameter } from "../utils/matchingDataTools.js";
  import hookStates from "../hook/index.js";
  import { getAllDataLabelsApi } from "@/api/dataRecognition.js";
  import { defaultConfigHooks } from "../hook/config-default.js";

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
  });

  const {
    form,
    formRef,
    formRules,
    selectionsList,
    dialogShow,
    editType,
    dialogFormRef,
    matchedConditionEditorRef,
    actOptions,
    typeOptions,
    dataRecognitionList,
    dialogFormRules,
    DialogForm,
    dialogForm,
    tableRef,
    denyContextPreviewShow,
    activeMatchDataIndex,
    matchingDataFormRef,
    matchingDataFormConfig,

    labelOptions,
    logicOptions,
    symbolOptions,
    matchDataCollapse,
    matchConditionCollapse,
  } = hookStates();

  const { switchAble, getDefaultConfig } = defaultConfigHooks();

  const onCreated = async () => {
    await getAllDataLabels();
    if (props.content || props.global) {
      initForm();
    } else {
      // 使用默认配置
      const defaultConfig = getDefaultConfig(dataRecognitionList.value);
      form.configRule = JSON.parse(JSON.stringify(defaultConfig));
    }
  };

  const matchDataTextInfo = match_data => {
    return getMatchDataTextInfo(match_data, {
      labelOptions,
      logicOptions,
      dataRecognitionList: dataRecognitionList.value,
      symbolOptions,
    });
  };

  const getAllDataLabels = async () => {
    const res = await getAllDataLabelsApi();
    dataRecognitionList.value = (res.data.data_label_rules || []).map(item => {
      return {
        label: item.name,
        value: item.id,
        rule: item.rule,
        builtin_sign: item.builtin_sign, // 是否内置数据标签
        enabled_sign: item.enabled_sign, // 是否启用数据标签
      };
    });
  };

  const initForm = () => {
    const content = JSON.parse(props.content || props.global);
    form.configRule = content.plugin_config.rules_config.map((item, index) => {
      const rules_config = {
        rule_name: item.rule_name,
        match_data: content.meta_data[index].match_data.map(item => {
          return {
            label: item.label,
            logic: item.logic,
            matchContent: item.matchContent,
            symbol: item.symbol,
            quantity: item.quantity,
          };
        }),
        matchers: compatibleMatchingList(content.meta_data[index].matchers),
        act: item.act,
        enable: item.enable,
        // deny_type: item.deny_type,
        deny_context: item.deny_context,
        isDefault: item.isDefault,
      };
      if (item.act === 1) {
        rules_config.deny_type = item.deny_type;
      } else {
        rules_config.deny_type = 1;
      }

      return rules_config;
    });
  };
  const handleSelectionChange = value => {
    selectionsList.value = value;
  };

  let activeTableIndex = null;
  const addDialogOpen = () => {
    editType.value = "添加";
    dialogShow.value = true;
  };

  const matchDataBeforeAdd = callback => {
    const valid = matchingDataFormRef.value.validator();
    if (!valid) {
      return callback(new Error());
    } else {
      callback();
    }
  };

  const dialogClose = () => {
    dialogFormRef.value.resetFields();
    Object.assign(dialogForm, new DialogForm());
  };

  const dialogConfirm = () => {
    dialogFormRef.value.validate(valid => {
      const matchedConditionValid = matchedConditionEditorRef.value.validator();
      const matchingDataFormValid = matchingDataFormRef.value.validator();
      if ([valid, matchingDataFormValid, matchedConditionValid].every(item => item)) {
        const dialog_form = JSON.parse(JSON.stringify(dialogForm));

        if (editType.value === "添加") {
          dialog_form.enable = true;
        } else {
          dialog_form.enable = form.configRule[activeTableIndex].enable || false;
        }

        if (editType.value === "添加") {
          if (form.configRule.some(item => item.rule_name === dialog_form.rule_name)) {
            return OuModal.warning(`规则名称${dialog_form.rule_name}已存在`);
          }
          form.configRule.unshift(dialog_form);
        } else {
          if (
            form.configRule.some(item => item.rule_name === dialog_form.rule_name) &&
            form.configRule[activeTableIndex].rule_name !== dialog_form.rule_name
          ) {
            return OuModal.warning(`规则名称${dialog_form.rule_name}已存在`);
          }
          form.configRule[activeTableIndex] = dialog_form;
        }
        dialogShow.value = false;

        formRef.value.validate();
      }
    });
  };

  const updateClick = (tableIndex, row) => {
    editType.value = "编辑";
    activeTableIndex = tableIndex;
    for (let k in dialogForm) {
      if (dialogForm.hasOwnProperty(k)) {
        dialogForm[k] = JSON.parse(JSON.stringify(row[k]));
      }
    }
    dialogShow.value = true;
  };
  const delClick = tableIndex => {
    // 若seconds中存在删除项，则删除记录值
    const include = selectionsList.value.findIndex(item => item === form.configRule[tableIndex]);
    if (include !== -1) selectionsList.value.splice(include, 1);

    // 删除列表项
    form.configRule.splice(tableIndex, 1);

    formRef.value.validate();
  };

  const batchesDelClick = () => {
    if (selectionsList.value.length) {
      const content = {
        content: `确定要删除选中的${selectionsList.value.length}条配置规则吗？`,
        moreContent: `删除后将清理掉对应的配置信息。`,
      };
      OuModal.confirm(content)
        .then(() => {
          // 删除列表项
          form.configRule = form.configRule.filter(item => !selectionsList.value.includes(item));
          // 清空selections记录值
          selectionsList.value.splice(0, selectionsList.value.length);

          formRef.value.validate();
        })
        .catch(() => {});
    }
  };

  const denyContextPreviewOpen = () => {
    denyContextPreviewShow.value = true;
  };

  const denyContextPreviewClose = () => {
    denyContextPreviewShow.value = false;
  };

  const denyContextJSON = computed(() => {
    function isJSON(str) {
      try {
        JSON.parse(str);
        return true;
      } catch (e) {
        return false;
      }
    }
    if (isJSON(dialogForm.deny_context)) {
      return JSON.stringify(JSON.parse(dialogForm.deny_context), null, 2);
    } else {
      return "JSON格式错误";
    }
  });

  const getOptionsLabel = (value, options) => {
    return options.find(item => item.value === value)?.label || "";
  };

  const submit = async () => {
    return await Promise.all([formRef.value.validate(valid => valid)]).then(res => {
      if (res.every(item => item)) {
        const params = {
          plugin_config: {
            rules_config: [],
          },
          meta_data: [],
        };

        form.configRule.forEach(item => {
          const rules_config = {
            rule_name: item.rule_name,
            match_data: getMatchDataParameter(item.match_data, dataRecognitionList.value),
            matchers: getMatchParameter(item.matchers).match,
            act: item.act,
            enable: item.enable,
            isDefault: item.isDefault,
          };
          if (item.act === 1) {
            // 动作：拒绝
            rules_config.deny_type = item.deny_type;
            rules_config.deny_context = item.deny_context;
          } else {
            // 动作：仅记录
            rules_config.deny_type = 0;
            rules_config.deny_context = "";
          }
          params.plugin_config.rules_config.push(rules_config);
          params.meta_data.push({
            match_data: item.match_data.map(matchDataItem => {
              let matchDataItemObj = {
                ...matchDataItem,
              };

              let matchRuleArr = [];

              if (matchDataItem.label === "label") {
                matchRuleArr = matchDataItem.matchContent.map(item => {
                  return dataRecognitionList.value.filter(dataRecognitionItem => {
                    return dataRecognitionItem["value"] === item;
                  });
                });
                matchDataItemObj.matchContentRegArr = matchRuleArr.map((item, index) => {
                  const matchContentRegArrItem = {
                    pattern: getPatternParameter(item || []),
                  };

                  matchContentRegArrItem["id"] = matchDataItemObj.matchContent[index];

                  return matchContentRegArrItem;
                });
              } else if (matchDataItem.label === "custom") {
                matchDataItemObj.matchContent = matchDataItem.matchContent
                  .split(" ")
                  .filter(item => item.trim())
                  .join(" ");
              }

              return matchDataItemObj;
            }),
            matchers: item.matchers,
          });
        });

        const extension = {
          refs: {
            // 匹配条件
            ip_address_database: [], // 网段
            time_range_database: [], // 时间端

            // 匹配数据
            date_label_rule: [], // 数据识别
          },
        };
        const ipAddressArr = [];
        const timeRangeArr = [];
        const dataRecognitionArr = [];

        params.meta_data.forEach(item => {
          // 匹配条件
          item.matchers.forEach(matchItem => {
            if (matchItem.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic)) {
              ipAddressArr.push(...matchItem.content);
            } else if (
              matchItem.field === "time" &&
              ["belongToTimeRange", "notBelongToTimeRange"].includes(matchItem.logic)
            ) {
              timeRangeArr.push(matchItem.content);
            }
          });

          // 匹配数据
          item.match_data.forEach(matchItem => {
            if (matchItem.label === "label") {
              dataRecognitionArr.push(...matchItem.matchContent);
            }
          });
        });

        const configs = [
          { key: "ip_address_database", arr: ipAddressArr },
          { key: "time_range_database", arr: timeRangeArr },
          { key: "date_label_rule", arr: dataRecognitionArr },
        ];

        configs.forEach(({ key, arr }) => {
          extension.refs[key] = [...new Set(arr)].map(item => ({ item_id: item }));
        });

        return {
          contentParams: params,
          extension,
        };
      } else {
        throw new Error("表单校验失败");
      }
    });
  };

  onCreated();

  defineExpose({
    submit,
  });
</script>

<style lang="scss" scoped>
  .plugin-dataControl-creat-configuration {
    .config-rule-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      .btn-row {
        height: 32px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
      }
    }

    .return-page-area-content {
      width: 100%;
      .content-header {
        width: 100%;
        height: 32px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        border: 1px solid var(--ouryun-input-border-color, var(--ouryun-border-color));
        border-bottom: none;
        border-radius: var(--ouryun-border-radius-base) var(--ouryun-border-radius-base) 0 0;
      }
      .preview-btn {
        font-size: 14px;
        color: #999;
        display: flex;
        column-gap: 4px;
        align-items: center;
        line-height: 1;
        cursor: pointer;
        &:hover {
          color: var(--ouryun-color-brand-base);
        }
      }
      ::v-deep {
        .ouryun-textarea__inner {
          margin-left: 1px;
          width: calc(100% - 2px);
          box-sizing: border-box;
          border-radius: var(--ouryun-border-radius-base) !important;
          box-shadow: initial;
          box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color));
          &:hover {
            box-shadow: 0 0 0 1px var(--ouryun-input-hover-border-color);
          }
          &:focus {
            box-shadow: 0 0 0 1px var(--ouryun-input-focus-border-color);
          }
        }
        .content-header + .ouryun-textarea {
          .ouryun-textarea__inner {
            border-radius: 0px 0px var(--ouryun-border-radius-base) var(--ouryun-border-radius-base) !important;
          }
        }
      }
    }

    .denyContext-preview-content {
      width: 100%;
      height: calc(60vh - 90px);
      background: #f6f7fc;
      border-radius: var(--ouryun-border-radius-base);
      box-sizing: border-box;

      .html-content,
      .json-content {
        padding: 12px;
        width: 100%;
        margin: 0;
        font-family: "思源黑体";
        color: #333333;
        line-height: 1.5;
      }
    }
  }
</style>
