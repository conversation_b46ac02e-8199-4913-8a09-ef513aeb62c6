import { getWafType } from "@/api/pgModule";
import ruleList from "@/assets/data/rule.json";
import { regex as REGEX } from "@ouryun/ouryun-plus";
import { ref, reactive } from "vue";

const checkLevel = {
  L1: ["1"],
  L2: ["1", "2"],
  L3: ["1", "2", "3"],
  L4: ["1", "2", "3", "4"],
};

const mapLevel = {
  1: {
    label: "低危",
    color: "#FFCA64",
  },
  2: {
    label: "中危",
    color: "#FF9100",
  },
  3: {
    label: "高危",
    color: "#FF0B0B",
  },
};

const levelList = [
  {
    label: "基础",
    value: "L1",
    normal: true,
    desc: "适用于初次使用该插件以及测试该功能是否正常运转",
  },
  {
    label: "宽松",
    value: "L2",
    normal: true,
    desc: "只拦截攻击特征比较明显的请求，当误报情况较多的场景下，建议选择此模式",
  },
  {
    label: "中等",
    value: "L3",
    normal: true,
    desc: "满足大多数场景下的web防护需求",
  },
  {
    label: "严格",
    value: "L4",
    normal: true,
    desc: "可以拦截具有复杂的绕过特征的攻击请求，高精度防护要求可选择此模式",
  },
];
const flowMap = {
  kb: "KB",
  mb: "MB",
};
const dateMap = {
  H: "小时",
  M: "分钟",
  S: "秒",
};

const typeListMap = ref([]);

//兼容1.6=>2.0白名单
export const configWhite = whiteList => {
  const list = (whiteList.list || []).reduce((acc, cur) => {
    if (!cur.type) {
      const ipList = cur.ipList.split("\n").reduce((result, item) => {
        const isExist = acc.find(val => val.source_ip === item);
        if (!isExist) {
          result.push({
            source_ip: item,
            rule_type: [],
            rule_id: "",
            path: "",
            status: true,
            type: "rule_type",
          });
        }
        return result;
      }, []);
      acc.push(...ipList);
    } else {
      acc.push(cur);
    }
    return acc;
  }, []);
  return {
    visitId: whiteList.visitId,
    list,
  };
};

export const wafTypeData = () => {
  try {
    return getWafType().then(res => {
      return res.code === 200 ? res.data : ruleList;
    });
  } catch (error) {
    typeListMap.value = ruleList;
  }
};

export const configTypeMap = data => {
  return data.map(item => {
    const { TypeId, TypeName } = item;
    return {
      typeId: TypeId,
      typeName: TypeName,
      RuleType: parseInt(TypeId, 16),
    };
  });
};

//虚拟服务级别配置
export const virtualServiceConfig = form => {
  const requestData = {
    enable: form.astrict,
  };
  const routeData = [
    {
      action: {
        target: "IP",
        quotas: configFrequency(form.visitFrequency),
      },
      matchers: [
        {
          prefix: "/",
          case_sensitive: true,
        },
      ],
    },
  ];
  requestData.rules = form.astrict ? [...routeData] : [];
  return requestData;
};

//配置访问频率
export const configFrequency = e => {
  const count = {
    S: e.dateCount,
    M: e.dateCount * 60,
    H: e.dateCount * 60 * 60,
  };
  const data = [
    {
      max_count: e.count,
      duration: count[e.dateUnit],
    },
  ];
  return data;
};

//配置回显规则
export const configNoteRule = data => {
  return data.map(e => ({
    ClassId: e.ClassId,
    typeList: e.typeList,
    ClassName: e.ClassName,
  }));
};

//配置禁用规则
export const configDisableRules = data => {
  const disableRules = [];
  data.forEach(e => {
    const isDisabled = disableRules.findIndex(i => i.id === e.rule_id);
    const item = {
      id: e.rule_id,
      level: e.rule_level,
    };
    if (e.status && isDisabled >= 0) {
      disableRules.splice(isDisabled, 1);
    }
    if (!e.status && isDisabled < 0) {
      disableRules.push(item);
    }
  });
  return disableRules;
};

//配置放行规则
export const configPassRules = (data, form, passRuleList) => {
  const pass = passRuleList;
  const isOnly = !Boolean(Number(form.defendMode));
  data.forEach(e => {
    const isPass = pass.findIndex(v => v.id === e.rule_id);
    const item = {
      id: e.rule_id,
      level: e.rule_level,
    };
    if (!isOnly && !e.action && isPass >= 0) {
      pass.splice(isPass, 1);
    }
    if (!isOnly && e.action && isPass < 0) {
      pass.push(item);
    }
  });
  return pass;
};

export const configRulesData = (data, form) => {
  return (data || []).map(e => {
    return {
      ...e,
      is_default: configDefault(e.action, e.status, e.rule_level, form),
      status: e.status ? 1 : 0,
      action: e.action ? 2 : 1,
    };
  });
};
//判断是否是根据防护等级的默认规则
const configDefault = (action, status, rule_level, form) => {
  const ruleLevelList = checkLevel[form.defendLevel];
  const isExistLevel = ruleLevelList.includes(rule_level);
  const itemStatus = Boolean(status);
  const isOnly = !Boolean(Number(form.defendMode));
  return itemStatus === isExistLevel && isOnly === action;
};
export const isShowEdit = item => {
  //是否存在已修改的规则
  if (item.editedList) {
    //是否有显示类型字段并且显示
    return item.ShowCheckbox && item.ShowCheckbox === "0" ? false : true;
  } else {
    return false;
  }
};

export function useState() {
  const isLoading = ref(false);
  const isShowRuleConfig = ref(false);
  const ruleListItem = ref([]);
  const ruleData = ref([]);
  const leftListIndex = ref(0);
  const checkList = ref([]);
  const editedList = ref([]);
  const level1Id = ref("");
  const rightListIndex = ref(null);
  const selectTypeItem = ref([]);
  const flowList = flowMap;
  const selectDateList = dateMap;
  const formRef = ref(null);
  let baseForm = reactive({
    defendMode: "1",
    performanceMode: "1",
    defendLevel: "L3",
    detection_only: true,
    isHidden: true,
    grammar: true,
    semantics_enable: true,
    astrict: false,
    astrictSize: {
      count: "",
      unit: "kb",
    },
    visitFrequency: {
      dateCount: 60,
      dateUnit: "S",
      count: 120,
    },
    whiteData: {
      list: [],
      visitId: "",
    },
  });
  const whiteRef = ref(null);
  const passRuleId = ref([]);
  const rule = ref({
    level1Data: [],
    level2Data: [],
  });
  const selects = {
    //防护等级
    level: levelList,
  };

  const verificationRule = msg => [
    { required: true, message: msg, trigger: "change" },
    {
      required: true,
      pattern: /(^[1-9]$)|(^[1-9]\d{1,3}$)/,
      message: "请输入1-9999整数",
      trigger: "change",
    },
  ];
  const frequencyCount = msg => [
    { required: true, message: msg, trigger: "change" },
    {
      required: true,
      pattern: /^(0*(?:[1-9]\d{0,7}|100000000))$/,
      message: "请输入1-1000000000整数",
      trigger: "change",
    },
  ];
  const verification = () => {
    return {
      "visitFrequency.dateCount": verificationRule("请输入访问频率"),
      "visitFrequency.count": frequencyCount("请输入访问频率次数"),
    };
  };
  const validateSize = (rule, value, callback) => {
    const unit = baseForm.astrictSize.unit;
    const MSG = "范围限制256KB到100MB";
    const MAX = 100 * 1024;
    const MIN = 256;
    if (value) {
      const buffer = unit === "kb" ? value : value * 1024;
      if (buffer > MAX || buffer < MIN) {
        return callback(new Error(MSG));
      }
    }
    callback();
  };
  const rules = {
    paranoiaLevel: [{ required: true, message: "请选择防护等级" }],
    fullName: [
      { required: true, message: "请输入实例名称", trigger: "change" },
      {
        required: true,
        pattern: REGEX.PG_NAME,
        message: "请输入中文、英文、数字、-和_",
        trigger: "change",
      },
    ],
    "astrictSize.count": [{ validator: validateSize, trigger: "change" }],
    ...verification(),
  };
  return {
    isLoading,
    isShowRuleConfig,
    ruleListItem,
    ruleData,
    leftListIndex,
    checkList,
    editedList,
    level1Id,
    rightListIndex,
    selectTypeItem,
    flowList,
    selectDateList,
    formRef,
    rules,
    whiteRef,
    passRuleId,
    rule,
    levelList,
    selects,
    flowMap,
    dateMap,
    baseForm,
    typeListMap,
    checkLevel,
    mapLevel,
  };
}
