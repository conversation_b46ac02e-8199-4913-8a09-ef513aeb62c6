<template>
  <ou-opera-dialog
    :close-on-click-modal="false"
    width="552px"
    draggable
    :title="`${props.type}`"
    @close="closeDialog"
    @cancel="cancel"
    @confirm="submit"
    v-model="isShowDialog">
    <template #content>
      <ou-form
        :model="form"
        ref="ConfigRuleRef"
        :rules="rules"
        labelPosition="right"
        label-width="140px"
        class="-mb-20">
        <ou-form-item label="规则名称：" prop="name">
          <ou-input
            maxlength="63"
            show-word-limit
            placeholder="请输入"
            v-model="form.name"
            style="width: 300px"></ou-input>
        </ou-form-item>
        <ou-form-item label="协议类型：" prop="proto">
          <ou-select v-model="form.proto" placeholder="请选择" class="w-300">
            <ou-option v-for="(v, k) in protoOptions" :key="k" :label="v.label" :value="v.value"></ou-option>
          </ou-select>
        </ou-form-item>
        <ou-form-item label="源IP：" prop="saddr">
          <IpTip style="position: absolute; left: 308px; font-size: 16px" />
          <ou-select
            ref="selectRef"
            @change="selectChange"
            v-model="form.saddr"
            multiple
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
            style="width: 300px"
            placeholder="输入后按回车键确定">
            <ou-option v-for="item in saddr_group_ids" :key="item.value" :label="item.label" :value="item.value" />
          </ou-select>
        </ou-form-item>
        <ou-form-item label="源端口：" prop="sport">
          <portTip style="position: absolute; left: 308px; font-size: 16px" />
          <ou-input
            type="text"
            :disabled="form.proto == 3"
            :placeholder="placeholder"
            v-model="form.sport"
            style="width: 300px"></ou-input>
        </ou-form-item>
        <ou-form-item label="目的IP：" prop="daddr">
          <IpTip style="position: absolute; left: 308px; font-size: 16px" />
          <ou-select
            ref="selectRef"
            @change="selectChange2"
            v-model="form.daddr"
            multiple
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
            style="width: 300px"
            placeholder="输入后按回车键确定">
            <ou-option v-for="item in daddr_group_ids" :key="item.value" :label="item.label" :value="item.value" />
          </ou-select>
        </ou-form-item>
        <ou-form-item label="目的端口：" prop="dport">
          <portTip style="position: absolute; left: 308px; font-size: 16px" />
          <ou-input
            type="text"
            :disabled="form.proto == 3"
            :placeholder="placeholder"
            v-model="form.dport"
            style="width: 300px"></ou-input>
        </ou-form-item>
        <ou-form-item label="动作：" prop="action">
          <ou-radio-group v-model="form.action">
            <ou-radio :label="1">放行</ou-radio>
            <ou-radio :label="2">拒绝</ou-radio>
          </ou-radio-group>
        </ou-form-item>
        <ou-form-item label="描述：" prop="remark">
          <ou-input placeholder="请输入" v-model="form.remark" style="width: 300px"></ou-input>
        </ou-form-item>
      </ou-form>
    </template>
  </ou-opera-dialog>
</template>

<script setup>
  import { computed, defineProps, defineEmits, nextTick, ref, watch } from "vue";
  import IpTip from "@/components/IpTip";
  import portTip from "./portTip.vue";
  import { regex as REGEX } from "@ouryun/ouryun-plus";
  import { deepClone } from "@/utils/tool.js";
  import { validateIpAll } from "@/utils/validate.js";
  // import { getIPGroups } from '@/api/plugin'
  import {
    initFormParams,
    protoOptions,
    actionOptions,
    validateIp,
    validatePort,
    validateRemark,
  } from "../config/utils.js";
  const saddr_group_ids = ref([]);
  const daddr_group_ids = ref([]);
  const groupIds = ref([]);

  const emit = defineEmits(["comfirm", "cancel"]);
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const ConfigRuleRef = ref(null);
  const form = ref({});
  const rules = ref({
    name: [
      { required: true, message: "请输入规则名称", trigger: "change" },
      {
        required: true,
        pattern: REGEX.PG_NAME,
        message: "请输入中文、英文、数字、-和_",
        trigger: "change",
      },
    ],
    proto: [{ required: true, message: "请选择", trigger: "change" }],
    saddr: [{ required: false, trigger: "change", validator: validateIp }],
    daddr: [{ required: false, trigger: "change", validator: validateIp }],
    sport: [{ required: false, trigger: "change", validator: validatePort }],
    dport: [{ required: false, trigger: "change", validator: validatePort }],
    remark: [{ required: false, trigger: "change", validator: validateRemark }],
  });

  const placeholder = ref("请输入1-65535");

  watch(
    () => form.value?.proto,
    val => {
      if (val == 3) {
        form.value.sport = "";
        form.value.dport = "";
        placeholder.value = "-";
      } else {
        placeholder.value = "请输入1-65535";
      }
    },
    { deep: true, immediate: true },
  );

  const selectChange = async val => {
    form.value.saddr = val.map(item => item.trim()).filter(item => item !== "");
    let index = form.value.saddr.findIndex(i => i == "" || !i || i === undefined || !validateIpAll(i));
    if (index > -1) {
      form.value.saddr.splice(index, 1);
    }
    let list = form.value.saddr
      .filter(item => {
        let i = saddr_group_ids.value.findIndex(i => i.value == item);
        if (i == -1) {
          return item;
        }
      })
      .map(item => {
        return {
          label: item,
          value: item,
        };
      });
    await nextTick();
    saddr_group_ids.value.push(...list);
  };

  const selectChange2 = async val => {
    form.value.daddr = val.map(item => item.trim()).filter(item => item !== "");
    let index = form.value.daddr.findIndex(i => i == "" || !i || i === undefined || !validateIpAll(i));
    if (index > -1) {
      form.value.daddr.splice(index, 1);
    }
    let list = form.value.daddr
      .filter(item => {
        let i = daddr_group_ids.value.findIndex(i => i.value == item);
        if (i == -1) {
          return item;
        }
      })
      .map(item => {
        return {
          label: item,
          value: item,
        };
      });
    await nextTick();
    daddr_group_ids.value.push(...list);
  };
  const isShowDialog = computed({
    get: () => {
      let saddrArray = [],
        daddrArray = [];
      if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
        form.value = deepClone(initFormParams);
      } else {
        let saddrs = props.modelValue?.saddr?.split(",")?.filter(i => i);
        let daddrs = props.modelValue?.daddr?.split(",")?.filter(i => i);
        saddrArray = saddrs?.map(item => ({ label: item, value: item }));
        daddrArray = daddrs?.map(item => ({ label: item, value: item }));
        form.value = {
          ...deepClone(props.modelValue),
          saddr: saddrs,
          daddr: daddrs,
        };
      }
      if (props.visible) {
        saddr_group_ids.value = [...saddrArray];
        daddr_group_ids.value = [...daddrArray];
      }
      return props.visible;
    },
    set: val => {
      emit("cancel");
    },
  });
  const submit = () => {
    ConfigRuleRef.value.validate(valid => {
      if (!valid) return;
      emit("comfirm", form.value, props.type);
    });
  };
  const closeDialog = () => {
    ConfigRuleRef.value?.resetFields();
    emit("cancel");
  };

  const cancel = () => {
    emit("cancel");
  };
</script>

<style scoped lang="scss">
  .configRule {
    width: 100%;
  }

  .w-300 {
    width: 300px !important;
  }

  .ouryun-form.ouryun-form--default > .ouryun-form-item-custom {
    margin-bottom: 0px !important;
  }
  .list-handle {
    display: flex;
  }
</style>
