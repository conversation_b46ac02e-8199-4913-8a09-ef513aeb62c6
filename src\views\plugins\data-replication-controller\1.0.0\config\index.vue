<template>
  <!--  数据控制创建配置 -->
  <div class="plugin-dataControl-creat-configuration add_conent_flex1">
    <ou-form label-width="140px" label-position="right">
      <ou-form-item label="配置规则：" prop="plugin" style="margin: 0px">
        <div class="config-rule-container">
          <div class="btn-row">
            <ou-button @click="addDialogOpen" type="primary" text :disabled="configRule.length >= 100">添加</ou-button>
            <ou-button @click="batchesDelClick" type="primary" text :disabled="!selectionsList.length">
              批量删除
            </ou-button>
          </div>
          <ou-table
            ref="tableRef"
            :data="configRule"
            :height="!configRule.length ? 200 : configRule.length >= 5 ? 240 : 'auto'"
            class="editor"
            style="width: 100%; --table-empty-size: 120px"
            @select="value => handleSelectionChange(value)"
            @select-all="value => handleSelectionChange(value)">
            <ou-table-column type="selection" width="32" align="center" :selectable="row => !row.isDefault" />
            <ou-table-column type="index" label="序号" width="44" align="center" />
            <ou-table-column prop="ruleName" label="规则名称">
              <template #default="scope">
                <ou-text-ellipsis :content="String(scope.row.ruleName)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="matchedConditionInfo" label="匹配条件">
              <template #default="scope">
                <ou-text-ellipsis :content="getTextInfo(scope.row.matchingList)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="protectionMode" label="动作">
              <template #default="scope">
                <ou-text-ellipsis
                  :content="getOptionsLabel(scope.row.protectionMode, protectionModeOptions) || '-'"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="protectionMode" label="状态">
              <template #default="scope">
                <ou-switch
                  v-model="scope.row.enable"
                  active-text="启用"
                  size="small"
                  inactive-text="禁用"
                  class="switch-offside"></ou-switch>
              </template>
            </ou-table-column>
            <ou-table-column prop="address" label="操作" width="100">
              <template #default="scope">
                <ou-button @click="updateClick(scope.$index, scope.row)" type="primary" text>编辑</ou-button>
                <ou-button v-if="!scope.row.isDefault" @click="delClick(scope.$index)" type="primary" text>
                  删除
                </ou-button>
              </template>
            </ou-table-column>
          </ou-table>
        </div>
      </ou-form-item>
    </ou-form>
    <ou-opera-dialog
      :title="`${editType}配置规则`"
      v-model="dialogShow"
      @close="dialogClose"
      @cancel="dialogClose"
      @confirm="dialogConfirm"
      :close-on-click-modal="false"
      :width="1139"
      draggable>
      <template #content>
        <ou-form ref="dialogFormRef" :rules="dialogFormRules" :model="dialogForm" label-width="120px">
          <ou-form-item label="规则名称：" prop="ruleName">
            <ou-input
              v-model.trim="dialogForm.ruleName"
              placeholder="请输入"
              maxlength="63"
              show-word-limit
              :disabled="dialogForm.isDefault"
              style="width: 300px"></ou-input>
          </ou-form-item>
          <ou-form-item label="匹配条件：" prop="matchingList">
            <ou-collapse-panel class="matchedConditionEditorCollapse" v-model:isCollapse="dialogForm.isCollapse">
              <template #headerLeft>
                <span class="header-hint">多个条件同时满足才执行对应动作</span>
              </template>
              <template #content>
                <matchedConditionEditor
                  v-if="dialogShow"
                  ref="matchedConditionEditorRef"
                  v-model="dialogForm.matchingList"
                  :disabled="dialogForm.matchingList.length > 4"
                  :maxRows="5"
                  generalPurposeLibrary
                  :fieldOptions="['srcIp', 'path', 'userName', 'region', 'time']"
                  style="width: 100%"></matchedConditionEditor>
              </template>
            </ou-collapse-panel>
          </ou-form-item>
          <ou-form-item label="防护模式：" prop="protectionMode" class="line-height-22">
            <ou-radio-group v-model="dialogForm.protectionMode">
              <ou-radio v-for="item in protectionModeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </ou-radio>
            </ou-radio-group>
          </ou-form-item>
          <ou-form-item label="复制水印：" prop="copyWatermark" v-if="dialogForm.protectionMode === 1">
            <ou-input
              v-model.trim="dialogForm.copyWatermark"
              placeholder="请输入"
              maxlength="50"
              show-word-limit
              style="width: 300px"></ou-input>
            <ou-button
              @click="copyWatermarkPreviewShow = true"
              class="ml-8"
              type="primary"
              text
              :disabled="!dialogForm.copyWatermark">
              预览效果
            </ou-button>
          </ou-form-item>
        </ou-form>
      </template>
    </ou-opera-dialog>
    <ou-opera-dialog
      title="预览效果"
      v-model="copyWatermarkPreviewShow"
      width="528px"
      :close-on-click-modal="false"
      @close="copyWatermarkPreviewClose"
      draggable
      hideFooter>
      <template #content>
        <div class="copyWatermark-preview-content">
          <div class="clipboard-example">SRhino</div>
          <div class="copyWatermark-text">{{ dialogForm.copyWatermark }}</div>
        </div>
      </template>
    </ou-opera-dialog>
  </div>
</template>
<script setup>
  import { defineProps, defineExpose, ref, nextTick, computed, watch } from "vue";
  import matchedConditionEditor from "@/components/NewMatchedConditionEditor/index.vue";
  import { OuModal } from "@ouryun/ouryun-plus";
  import { getTextInfo, getMatchParameter, compatibleMatchingList } from "@/utils/MatchedCondition.js";
  import hookStates from "../hook/index.js";
  import { defaultConfig } from "../hook/config-default.js";

  import usePlugin from "basePluginStore";
  const store = usePlugin();

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
  });

  const { protectionModeOptions, dialogFormRules, DialogForm, dialogForm, copyWatermarkPreviewShow } = hookStates();

  const onCreated = () => {
    if (props.content || props.global) {
      initForm();
    } else {
      //使用【内置策略】
      configRule.value = JSON.parse(JSON.stringify(defaultConfig));
    }
  };

  const initForm = () => {
    const content = JSON.parse(props.content || props.global);
    const rules = content.meta_data.form.rules.map(rule => {
      return {
        ...rule,
        matchingList: compatibleMatchingList(rule.matchingList),
      };
    });

    configRule.value = rules;
  };
  const configRule = ref([]);

  let selectionsList = ref([]);
  const handleSelectionChange = value => {
    selectionsList.value = value;
  };

  const dialogShow = ref(false);

  let activeTableIndex = null;
  const editType = ref("添加");
  const addDialogOpen = () => {
    editType.value = "添加";
    dialogShow.value = true;
  };

  const dialogClose = () => {
    dialogShow.value = false;

    setTimeout(() => {
      dialogFormRef.value.resetFields();
      Object.assign(dialogForm, new DialogForm());
    }, 500);
  };

  const dialogFormRef = ref(null);
  const matchedConditionEditorRef = ref(null);

  const dialogConfirm = () => {
    const matchedConditionValid = matchedConditionEditorRef.value.validator();
    dialogFormRef.value.validate(valid => {
      if (valid && matchedConditionValid) {
        const form = JSON.parse(JSON.stringify(dialogForm));

        if (["添加"].includes(editType.value)) {
          form.enable = true;
        } else {
          form.enable = configRule.value[activeTableIndex].enable || false;
        }

        if (["添加", "复制"].includes(editType.value)) {
          if (configRule.value.some(item => item.ruleName === form.ruleName)) {
            return OuModal.warning(`规则名称${form.ruleName}已存在`);
          }
          configRule.value.unshift(form);
        } else {
          if (
            configRule.value.some(item => item.ruleName === form.ruleName) &&
            configRule.value[activeTableIndex].ruleName !== form.ruleName
          ) {
            return OuModal.warning(`规则名称${form.ruleName}已存在`);
          }
          configRule.value[activeTableIndex] = form;
        }
        dialogClose();
      }
    });
  };

  const updateClick = (tableIndex, row) => {
    editType.value = "编辑";
    activeTableIndex = tableIndex;
    for (let k in dialogForm) {
      if (dialogForm.hasOwnProperty(k)) {
        dialogForm[k] = JSON.parse(JSON.stringify(row[k]));
      }
    }
    dialogShow.value = true;
  };
  const delClick = tableIndex => {
    // 若seconds中存在删除项，则删除记录值
    const include = selectionsList.value.findIndex(item => item === configRule.value[tableIndex]);
    if (include !== -1) selectionsList.value.splice(include, 1);

    // 删除列表项
    configRule.value.splice(tableIndex, 1);
  };

  const batchesDelClick = () => {
    if (selectionsList.value.length) {
      const content = {
        content: `确定要删除选中的${selectionsList.value.length}条配置规则吗？`,
        moreContent: `删除后将清理掉对应的配置信息。`,
      };
      OuModal.confirm(content)
        .then(() => {
          // 删除列表项
          configRule.value = configRule.value.filter(item => !selectionsList.value.includes(item));
          // 清空selections记录值
          selectionsList.value.splice(0, selectionsList.value.length);
        })
        .catch(() => {});
    }
  };

  const tableRef = ref(null);

  const getOptionsLabel = (value, options) => {
    return options.find(item => item.value === value)?.label || "";
  };

  const copyWatermarkPreviewClose = () => {
    copyWatermarkPreviewShow.value = false;
  };
  const configSubmitData = () => {
    const rules = configRule.value.map((e, i) => {
      const { copyWatermark, ruleName, protectionMode, matchingList, enable, isDefault } = e;
      return {
        water_mark_context: copyWatermark,
        action: protectionMode === 1 ? "ALLOW" : "DENY",
        rule_name: ruleName,
        enable,
        ...getMatchParameter(matchingList),
        id: i + 1,
        isDefault,
      };
    });
    return {
      rules,
    };
  };
  const submit = async () => {
    const params = {
      plugin_config: configSubmitData(),
      meta_data: {
        form: {
          rules: configRule.value,
        },
      },
    };
    const extension = { refs: { ip_address_database: [], time_range_database: [] } };
    const ipAddressArr = [];
    const timeRangeArr = [];
    configRule.value.forEach(item => {
      // 匹配条件
      item.matchingList.forEach(matchItem => {
        if (matchItem.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic)) {
          ipAddressArr.push(...matchItem.content);
        } else if (
          matchItem.field === "time" &&
          ["belongToTimeRange", "notBelongToTimeRange"].includes(matchItem.logic)
        ) {
          timeRangeArr.push(matchItem.content);
        }
      });
    });

    const configs = [
      { key: "ip_address_database", arr: ipAddressArr },
      { key: "time_range_database", arr: timeRangeArr },
    ];
    configs.forEach(({ key, arr }) => {
      extension.refs[key] = [...new Set(arr)].map(item => ({ item_id: item }));
    });
    return {
      contentParams: params,
      extension,
    };
  };

  onCreated();

  defineExpose({
    submit,
  });
</script>

<style lang="scss" scoped>
  .plugin-dataControl-creat-configuration {
    .config-rule-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      .btn-row {
        height: 32px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
      }
    }
  }

  .copyWatermark-preview-content {
    font-size: 14px;
    color: #333333;
    .clipboard-example {
      line-height: 22px;
      border-bottom: 2px dashed #e7e7e7;
      padding-bottom: 20px;
    }
    .copyWatermark-text {
      margin: 20px 0px;
    }
  }
  :deep(.ouryun-opera-custom) {
    .ou-opear-content {
      padding: 32px 40px 12px 40px !important;
    }
  }
</style>
