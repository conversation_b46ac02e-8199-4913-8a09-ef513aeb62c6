import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";

// 获取当前模块的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const basePath = "./src/views/plugins";

/**
 * 获取指定目录下的一二级文件
 * @param {string} dirPath - 目录路径
 * @returns {Array} - 文件路径数组
 */
function getAllFiles(dirPath) {
  let arrayOfFiles = [];
  const files = fs.readdirSync(dirPath);
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    if (fs.statSync(filePath).isDirectory()) {
      const subFiles = fs.readdirSync(filePath).map(subFile => path.join(filePath, subFile));
      arrayOfFiles = arrayOfFiles.concat(subFiles);
    } else {
      arrayOfFiles.push(filePath);
    }
  });

  return arrayOfFiles;
}

/**
 * 提取 plugins 二级三级目录的名字，并存放相对路径到数组中
 * @param {Array} files - 文件路径数组
 * @returns {Array} - 相对路径数组
 */
function extractPluginNames(files) {
  const pluginPaths = files
    .map(file => {
      const relativePath = path.relative(__dirname, file);
      const parts = relativePath.split(path.sep);
      const versionPattern = /^\d+\.\d+\.\d+$/; // 匹配版本号的正则表达式
      // console.log(parts, parts[4], versionPattern.test(parts[4]));
      if (parts[4] && versionPattern.test(parts[4])) {
        const pluginName = parts.slice(3, 7).join("/"); // 提取二级三级四级目录的名字
        return pluginName;
      }
      return null;
    })
    .filter(Boolean); // 去除 null 值
  return [...new Set(pluginPaths)]; // 去重
}

/**
 * 获取所有插件路径
 * @returns {Array} - 插件路径数组
 */
export const pluginPaths = () => {
  const directoryPath = path.join(__dirname, basePath);
  const files = getAllFiles(directoryPath);
  return extractPluginNames(files);
};

/**
 * 合并配置文件路径
 * @param {string} src - 源文件路径
 * @param {string} target - 目标文件路径
 * @returns {Object} - 包含源文件路径和目标文件路径的对象
 */
export const cfg_merge = (src, target) => {
  // const srcFilePath = path.resolve(__dirname, `./src/views/plugins/${src}/cfg_merge.js`);
  const srcFilePath = path.resolve(__dirname, `./public/cfg_merge.js`);
  const distFilePath = path.resolve(__dirname, `./dist/${target}/cfg_merge.js`);
  return {
    srcFilePath,
    distFilePath,
  };
};

/**
 * 插件源码及版本号，暴露地址
 * @param {string} srcPath - 插件源码路径 eg: weak-password-detection/1.0.1'
 * @returns {Object} - 包含插件入口文件路径的对象 eg: { './index': './src/views/plugins/weak-password-detection/1.0.1/config/index.vue' }
 */
export function pluginExposeEntry(srcPath) {
  let parts = [basePath, srcPath, "config", "index.vue"]; // extends
  const filePath = parts.join("/");
  let temp = {};
  if (fs.existsSync(filePath)) {
    temp[`./index`] = filePath;
  } else {
    throw new Error(`源码路径：${filePath}未找到`);
  }
  return temp;
}

/**
 * 生成插件的构建目录
 * @param {string} srcPath - 插件源码路径 eg: weak-password-detection/v1.0.1
 * @param {string} [version] - 插件版本号
 * @returns {string} - 构建目录路径 eg: www.srhino.com.weak-password-detection.1.0.1
 */
export const pluginBuildDir = (srcPath, version) => {
  let parts = srcPath.split("/");
  if (version) {
    return `www.srhino.com.${parts[0]}.${version}`;
  }
  return `www.srhino.com.${parts[0]}.${parts[1]}`;
};
