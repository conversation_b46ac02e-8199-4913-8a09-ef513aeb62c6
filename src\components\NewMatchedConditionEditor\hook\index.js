import { ref } from "vue";

export default function () {
  const anyMatchForm = [
    {
      prop: "field",
      type: () => "input",
      visible: () => true,
      span: () => 1,
      attrs: () => {
        return {
          disabled: true,
        };
      },
    },
    {
      prop: "params",
      type: () => "input",
      visible: () => true,
      span: () => 1,
      attrs: () => {
        return {
          disabled: true,
        };
      },
    },
    {
      prop: "logic",
      type: () => "input",
      visible: () => true,
      span: () => 1,
      attrs: () => {
        return {
          disabled: true,
        };
      },
    },
    {
      prop: "content",
      type: () => "input",
      visible: () => true,
      span: () => 1,
      attrs: () => {
        return {
          disabled: true,
        };
      },
    },
  ];

  const CascadeEditorRef = ref(null);
  const rules = ref([]);
  const activeIndex = ref(0);

  return {
    anyMatchForm,
    CascadeEditorRef,
    rules,
    activeIndex,
  };
}
