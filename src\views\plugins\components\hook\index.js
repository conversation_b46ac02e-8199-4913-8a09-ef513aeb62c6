import { validateIpAll } from "@/utils/validate.js";
const hasDuplicateKey = (objArray, key) => {
  const valueCount = new Map();
  for (const obj of objArray) {
    const value = obj[key];
    if (valueCount.has(value)) {
      return obj[key];
    } else {
      valueCount.set(value, obj);
    }
  }
  return false;
};

const configUploadIPData = data => {
  const { list } = data;
  let result = {
    data: [],
    fail: [],
    status: "",
  };
  const listData = list.reduce((acc, cur) => {
    if (typeof cur === "object") {
      const ipItem = Object.values(cur);
      acc.push(...ipItem);
    } else {
      acc.push(cur);
    }
    return acc;
  }, []);
  const resData = listData.filter(v => validateIpAll(v));
  const failData = listData.filter(v => !validateIpAll(v));
  result.data = resData;
  result.fail = failData;
  if (failData.length && resData.length) {
    result.status = "warning";
  }
  if (resData.length && !failData.length) {
    result.status = "success";
  }
  if (!resData.length && failData.length) {
    result.status = "warning";
  }
  if (!resData.length && !failData.length) {
    result.status = "warning";
  }
  return result;
};
export const useState = () => {
  return {
    hasDuplicateKey,
    configUploadIPData,
  };
};
