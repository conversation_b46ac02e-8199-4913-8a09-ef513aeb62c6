import usePlugin from "basePluginStore";

export const defaultConfig = [
  {
    ruleName: "内置规则-非工作时间访问",
    matchingList: [{ field: "time", logic: "notBelongToTimeRange", params: "", content: "" }],
    act: 2,
    type: "text",
    denyContext: "",
    enable: false,
    isDefault: true,
  },
  { ruleName: "默认规则", matchingList: [], act: 0, type: "text", denyContext: "", enable: true, isDefault: true },
];

export function defaultConfigHooks() {
  const store = usePlugin();
  const getDefaultConfig = () => {
    // 获取时间段中【工作时间】这一条数据，通过label和disabled属性来查找
    const workTime = store.timePeriodsList?.find(item => item.label === "工作时间" && !item.disabled);
    if (!workTime) {
      return []; // 如果没有工作时间，则返回空数组
    }
    defaultConfig.forEach(config => {
      config.matchingList.forEach(matcher => {
        // 如果匹配内容是时间,且 属于时间段/不属于时间段，则赋值为工作时间id
        if (matcher.field === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(matcher.logic)) {
          matcher.content = workTime.value;
        }
      });
    });
    return defaultConfig;
  };

  return {
    getDefaultConfig,
  };
}
