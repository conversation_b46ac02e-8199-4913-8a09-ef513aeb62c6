import { reactive, ref, nextTick } from "vue";
import { regex as REGEX } from "@ouryun/ouryun-plus";
import { OuModal } from "@ouryun/ouryun-plus";
const validateMatch = (rule, value, callback) => {
  if (value.length > 1000) {
    const message = "规则条数已超过上限1000条，请进行清理后再提交";
    this.$message.warning(message);
    return callback(new Error(message));
  }
  if (value.some(item => item.status === "编辑")) {
    return callback(new Error("当前正处于编辑状态"));
  } else {
    callback();
  }
};

export default function () {
  const baseForm = reactive({
    configRules: [],
  });
  const rowForm = ref();
  const submitType = ref("");
  const tablesRef = ref(null);
  const ConfigRuleRef = ref(null);
  const isShowDialog = ref(false);
  const selectionsList = ref([]);
  const isCheckData = ref(true);
  const selectedConfigRuleIndex = ref(0);

  const rules = ref({
    rule_name: [
      { required: true, message: "请输入规则名称", trigger: "change" },
      {
        required: true,
        pattern: REGEX.PG_NAME,
        message: "请输入中文、英文、数字、-和_",
        trigger: "change",
      },
    ],
    matchingList: [{ required: false, trigger: [], validator: validateMatch }],
  });

  const showDialog = (type, row, index) => {
    submitType.value = type;
    selectedConfigRuleIndex.value = index;
    rowForm.value = row || {};
    isShowDialog.value = true;
  };
  const routeMoved = (type, list) => {
    baseForm.configRules = list;
    nextTick(() => {
      selectionsList.value.forEach(e => {
        tablesRef.value.toggleRowSelection(e);
      });
    });
  };
  const handleSelectionChange = val => {
    isCheckData.value = !val.length;
    if (val.length) {
      selectionsList.value = val;
    }
  };

  //置顶
  const top = (row, index) => {
    if (index >= 0 && index < baseForm.configRules.length) {
      const element = baseForm.configRules.splice(index, 1)[0];
      baseForm.configRules.unshift(element);
    }
  };
  const remove = (row, index) => {
    baseForm.configRules.splice(index, 1);
  };
  //批量删除
  const batchRemove = () => {
    const batchTitle = `确定要批量删除${selectionsList.value.length}条配置规则吗？`;

    const content = {
      content: batchTitle,
      moreContent: `删除后将清理掉配置规则。`,
    };
    OuModal.confirm(content).then(() => {
      // 删除列表项
      baseForm.configRules = baseForm.configRules.filter(item => !selectionsList.value.includes(item));
      // 清空selections记录值
      selectionsList.value = [];
    });
  };

  const submitDialog = form => {
    const outForm = baseForm.configRules[selectedConfigRuleIndex.value];

    if (["添加"].includes(submitType.value)) {
      form.enable = true;
    } else {
      form.enable = outForm.enable || false;
    }
    if (["添加", "复制"].includes(submitType.value)) {
      if (baseForm.configRules.some(item => item.rule_name === form.rule_name)) {
        return OuModal.warning(`规则名称${form.rule_name}已存在`);
      }
      baseForm.configRules.unshift(form);
    } else {
      if (
        baseForm.configRules.some(item => item.rule_name === form.rule_name) &&
        outForm.rule_name !== form.rule_name
      ) {
        return OuModal.warning(`规则名称${form.rule_name}已存在`);
      }
      baseForm.configRules[selectedConfigRuleIndex.value] = form;
    }
    isShowDialog.value = false;
  };
  const cancel = () => {
    isShowDialog.value = false;
  };

  return {
    baseForm,
    rowForm,
    rules,
    isCheckData,
    submitType,
    selectionsList,
    tablesRef,
    isShowDialog,
    selectedConfigRuleIndex,
    showDialog,
    routeMoved,
    handleSelectionChange,
    top,
    remove,
    batchRemove,
    cancel,
    submitDialog,
  };
}
