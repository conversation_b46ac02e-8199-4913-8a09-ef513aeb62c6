<template>
  <!-- 匹配条件编辑组件 -->
  <div class="plugin-matched-condition-editor">
    <ou-table class="matchedConditionEditor showAny" v-if="props.showAny" height="160px" :data="anyMatch" empty-default>
      <ou-table-column label="匹配字段" prop="field"></ou-table-column>
      <ou-table-column label="匹配参数" prop="params"></ou-table-column>
      <ou-table-column label="逻辑" prop="logic"></ou-table-column>
      <ou-table-column label="匹配内容" prop="content" width="240px"></ou-table-column>
      <ou-table-column label="操作" class-name="small-padding fixed-width" width="100">
        <template #default>
          <ou-button class="slot-btn" type="primary" text disabled>编辑</ou-button>
          <ou-button class="slot-btn" type="primary" text disabled>删除</ou-button>
        </template>
      </ou-table-column>
    </ou-table>
    <ou-table
      v-else
      ref="tableRef"
      :data="matchList"
      class="matchedConditionEditor"
      style="width: 100%; --table-empty-size: 120px"
      :height="matchList.length >= 5 ? 240 : matchList.length <= 3 ? 160 : 'auto'"
      empty-default>
      <ou-table-column prop="field" label="匹配字段">
        <template #default="scope">
          <ou-select
            v-if="scope.row.status === 'edit'"
            @change="rowFieldChange(scope.row)"
            @visible-change="fieldVisibleChange"
            v-model="scope.row.field"
            placeholder="请选择字段"
            :clearable="false"
            :ref="`field-select-${scope.$index}`"
            popper-class="field-options-popover">
            <ou-option
              v-for="item in fieldOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
              class="field-option"></ou-option>
          </ou-select>
          <ou-text-ellipsis v-else :content="getFieldText(scope.row.field) || '-'"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column prop="params" label="匹配参数">
        <template #default="scope">
          <div v-if="scope.row.status === 'edit'" :class="{ 'cell-verify-content': verifyParams(scope.row) }">
            <ou-input
              v-model="scope.row.params"
              :placeholder="getParamsPlaceholder(scope.row)"
              :disabled="!scope.row.field || !['requestHeader', 'queryParameter'].includes(scope.row.field)"></ou-input>
            <div class="cell-verify">{{ verifyParams(scope.row) }}</div>
          </div>
          <ou-text-ellipsis v-else :content="scope.row.params || '-'"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column prop="logic" label="逻辑">
        <template #default="scope">
          <ou-select
            v-if="scope.row.status === 'edit'"
            v-model="scope.row.logic"
            placeholder="请选择"
            :disabled="!scope.row.field"
            :clearable="false"
            @change="rowLogicChange(scope.row)"
            :ref="`logic-select-${scope.$index}`">
            <ou-option
              v-for="item in getLogicOptions(scope.row, generalPurposeLibrary)"
              :key="item.value"
              :label="item.label"
              :value="item.value"></ou-option>
          </ou-select>
          <ou-text-ellipsis v-else :content="getLogicText(scope.row.logic) || '-'"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column prop="content" label="匹配内容" width="240px">
        <template #default="scope">
          <div v-if="scope.row.status === 'edit'" :class="{ 'cell-verify-content': verifyContent(scope.row) }">
            <span
              v-if="
                ['requestHeader', 'queryParameter'].includes(scope.row.field) &&
                ['null', 'presentMatchTrue', 'presentMatchFalse'].includes(scope.row.logic)
              ">
              -
            </span>
            <template
              v-else-if="
                scope.row.field === 'srcIp' &&
                ['belongToIpGroup', 'notBelongToIpGroup'].includes(scope.row.logic) &&
                generalPurposeLibrary
              ">
              <ou-selectV2
                style="width: 100%"
                v-model="scope.row.content"
                @visible-change="visible => requestMethodVisibleChange(visible, scope.row)"
                multiple
                filterable
                collapse-tags
                clearable
                :ref="`content-select-${scope.$index}`"
                :options="store.ipGroupList"></ou-selectV2>
              <div class="cell-verify">{{ verifyContent(scope.row) }}</div>
            </template>
            <template v-else-if="scope.row.field === 'requestMethod'">
              <ou-select
                style="width: 100%"
                v-model="scope.row.content"
                @visible-change="visible => requestMethodVisibleChange(visible, scope.row)"
                multiple
                filterable
                collapse-tags
                clearable
                popper-class="requestMethod-select-popper"
                :ref="`content-select-${scope.$index}`">
                <div class="select-tag">
                  <div class="select-tag-header">
                    <div>匹配方法</div>
                    <div>{{ scope.row.content.length }}/9</div>
                  </div>
                  <div class="check-all-container">
                    <ou-checkbox
                      label="全部"
                      v-model="requestMethodCheckAll"
                      :indeterminate="requestMethodCheckAllIsIndeterminate"></ou-checkbox>
                  </div>
                </div>
                <ou-option v-for="(item, index) in methodOptions" :key="index" :value="item.value" :label="item.label">
                  <span class="check"></span>
                  <span style="margin-left: 8px">{{ item.label }}</span>
                </ou-option>
              </ou-select>
              <div class="cell-verify">{{ verifyContent(scope.row) }}</div>
            </template>
            <template v-else-if="scope.row.field === 'region'">
              <ou-tree-select
                v-model="scope.row.content"
                :data="locationTreeList"
                multiple
                filterable
                node-key="value"
                :render-after-expand="false"
                :show-checkbox="true"
                style="width: 100%" />
              <div class="cell-verify">{{ verifyContent(scope.row) }}</div>
            </template>
            <template v-else>
              <ou-input
                v-model="scope.row.content"
                @blur="rowContentBlur(scope.row)"
                :placeholder="getContentPlaceholder(scope.row)"
                :disabled="!scope.row.field"></ou-input>
              <div class="cell-verify">{{ verifyContent(scope.row) }}</div>
            </template>
          </div>
          <ou-text-ellipsis v-else :content="getContentText(scope.row) || '-'"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="操作" width="100">
        <template #default="scope">
          <ou-button
            v-if="scope.row.status === 'edit'"
            @click="confirmRow(scope.row)"
            type="primary"
            text
            :disabled="confirmBtnDisabled(scope.row)">
            确定
          </ou-button>
          <ou-button v-if="scope.row.status === 'edit'" @click="cancelRow(scope)" type="primary" text>取消</ou-button>
          <ou-button
            v-if="scope.row.status === 'view'"
            @click="editRow(scope.row)"
            type="primary"
            text
            :disabled="props.disabled || editBtnDisabled">
            编辑
          </ou-button>
          <ou-button
            v-if="scope.row.status === 'view'"
            @click="delRow(scope.$index)"
            type="primary"
            text
            :disabled="props.disabled">
            删除
          </ou-button>
        </template>
      </ou-table-column>
    </ou-table>
    <div v-if="!props.showAny" class="matched-condition-editor-footer">
      <ou-button type="primary" style="display: flex" text @click="addRow" :disabled="props.disabled || addBtnDisabled">
        <ou-icon><Plus /></ou-icon>
        <span>添加</span>
      </ou-button>
      <div style="margin-left: 16px">
        <span>已配置</span>
        <span>{{ matchList.length }}</span>
        <span>条，还可添加</span>
        <span>{{ matchList.length > 10 ? 0 : 10 - matchList.length }}</span>
        <span>条（多个条件同时满足才执行对应动作）</span>
      </div>
    </div>
  </div>
</template>
<script setup name="matchedConditionEditor">
  import { ref, nextTick, defineEmits, onMounted, computed, watch } from "vue";
  import { fieldOptionsAll, methodOptions, locationTreeList } from "@/utils/MatchedCondition.js";
  import hookStates from "./hook/index.js";
  import { addSetFieldOptionsTooltip, removeSetFieldOptionsTooltip } from "./hook/tools.js";
  import usePlugin from "basePluginStore";

  const store = usePlugin();
  const {
    anyMatch,
    matchList,
    matchListBackup,
    verifyParams,
    verifyContent,
    getLogicOptions,
    getParamsPlaceholder,
    getContentPlaceholder,
    getFieldText,
    getContentText,
    getLogicText,
    confirmBtnDisabled,
    addBtnDisabled,
    editBtnDisabled,
  } = hookStates();

  const props = defineProps({
    modelValue: {
      type: Array,
      required: true,
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    showAny: {
      type: Boolean,
      default: false,
    },
    // 是否必填
    required: {
      type: Boolean,
      default: true,
    },
    // 匹配字段选项, 默认选项["源IP","路径","请求头部","请求方法","查询参数","账号"]
    fieldOptions: {
      type: Array,
      default: () => [
        "srcIp",
        "path",
        "requestHeader",
        "requestMethod",
        "queryParameter",
        "userName",
        // "sourceLocation",
        // "region",
      ],
    },
    // 是否使用通用库
    generalPurposeLibrary: {
      type: Boolean,
      default: false,
    },
  });

  const fieldOptions = computed(() => {
    return props.fieldOptions
      .map(item => {
        return fieldOptionsAll.value.find(item2 => item2.value === item);
      })
      .filter(item => item);
  });

  const emits = defineEmits(["update:modelValue"]);

  const tableRef = ref(null);

  const activeRequestMethodRow = ref({
    content: [],
  });

  const requestMethodVisibleChange = (visible, row) => {
    if (visible) {
      activeRequestMethodRow.value = row;
    } else {
      activeRequestMethodRow.value = { content: [] };
    }
  };

  const requestMethodCheckAllIsIndeterminate = computed(() => {
    return (
      activeRequestMethodRow.value.content.length > 0 &&
      activeRequestMethodRow.value.content.length < methodOptions.length
    );
  });

  const rowContentBlur = row => {
    if (row.field === "userName") {
      nextTick(() => {
        row.content = row.content
          .split(",")
          .filter(item => item)
          .map(item => {
            return item.trim();
          })
          .join(",");
      });
    }
  };

  const requestMethodCheckAll = computed({
    get: () => activeRequestMethodRow.value.content.length === methodOptions.length,
    set: newValue =>
      newValue
        ? (activeRequestMethodRow.value.content = methodOptions.map(item => item.value))
        : (activeRequestMethodRow.value.content = []),
  });

  const onCreated = () => {
    matchList.value = JSON.parse(JSON.stringify(props.modelValue));
    if (props.required && !matchList.value.length && !props.showAny) addRow();
  };

  onMounted(() => {
    matchListBackup.value = JSON.stringify(matchList.value);
  });

  const emitChange = () => {
    emits("update:modelValue", JSON.parse(JSON.stringify(matchList.value)));
  };

  // 更新匹配字段选项禁用状态
  const changeFieldOptionsAbled = () => {
    // 重置选项状态(除了“请求头部”、“查询参数”，其他匹配字段不可重复添加)
    fieldOptions.value.forEach(option => {
      if (
        matchList.value.some(
          item => item.field === option.value && !["requestHeader", "queryParameter"].includes(item.field),
        )
      ) {
        option.disabled = true;
      } else {
        option.disabled = false;
      }
    });
    // 当前编辑项已设置的匹配字段选项不禁用
    const editRow = matchList.value.find(item => item.status === "edit");
    if (editRow) {
      const activeOption = fieldOptions.value.find(option => option.value === editRow.field);
      activeOption && (activeOption.disabled = false);
    }
  };

  watch(
    () => matchList.value,
    () => {
      nextTick(() => {
        changeFieldOptionsAbled();
      });
      emitChange();
    },
    { deep: true, immediate: true },
  );

  const toScrollBottom = () => {
    nextTick(() => {
      tableRef.value?.setScrollTop(matchListBackup.value.length * 40);
    });
  };

  // 删除一行 / 删除按钮操作
  const delRow = index => {
    matchList.value.splice(index, 1);
    // 删除一行后，需要重新保存备份值
    const valueBackupVal = JSON.parse(matchListBackup.value);
    valueBackupVal.splice(index, 1);
    matchListBackup.value = JSON.stringify(valueBackupVal);
  };

  // 确定按钮操作
  const confirmRow = row => {
    row.status = "view";
    matchListBackup.value = JSON.stringify(matchList.value);
  };

  // 编辑按钮操作
  const editRow = row => {
    row.status = "edit";
  };

  // 取消按钮操作
  const cancelRow = scope => {
    const { $index: index, row } = scope;
    const rowBackup = JSON.parse(matchListBackup.value)[index];
    if (rowBackup) {
      row.status = "view";
      matchList.value[index] = rowBackup;
    } else {
      delRow(index, 1);
    }
  };

  // 添加按钮操作
  const addRow = () => {
    matchListBackup.value = JSON.stringify(matchList.value);
    matchList.value.push({
      field: "",
      params: "",
      logic: "",
      content: "",
      status: "edit",
    });
    toScrollBottom();
  };

  // 修改了"匹配字段"选项
  const rowFieldChange = row => {
    row.params = "";
    if (["requestMethod", "region"].includes(row.field)) {
      row.content = [];
    } else {
      row.content = "";
    }

    if (["requestMethod", "userName"].includes(row.field)) {
      row.logic = "exactTrue";
    } else if (["path"].includes(row.field)) {
      row.logic = "prefix";
    } else if (["srcIp", "requestHeader", "queryParameter", "sourceLocation", "region"].includes(row.field)) {
      row.logic = "containsTrue";
    }
  };

  // 展开/关闭了"匹配字段"下拉框
  const fieldVisibleChange = visible => {
    if (visible) {
      addSetFieldOptionsTooltip();
    } else {
      removeSetFieldOptionsTooltip();
    }
  };

  // 修改了"逻辑"选项
  const rowLogicChange = row => {
    if (
      ["requestHeader", "queryParameter"].includes(row.field) &&
      ["null", "presentMatchTrue", "presentMatchFalse"].includes(row.logic)
    ) {
      row.content = "";
    }

    if (row.field === "srcIp") {
      if (["belongToIpGroup", "notBelongToIpGroup"].includes(row.logic)) {
        row.content = [];
      } else if (["containsTrue", "containsFalse"].includes(row.logic)) {
        row.content = "";
      }
    }
  };

  onCreated();
</script>
<style lang="scss">
  .requestMethod-select-popper {
    .ouryun-select-dropdown__wrap {
      max-height: none;
    }
    .select-tag {
      margin: 0 -6px;
      position: sticky;
      top: 0;
      margin-top: -6px;
      margin-bottom: 4px;
      z-index: 999;
      display: flex;
      flex-direction: column;
      row-gap: 6px;
      .select-tag-header {
        width: 100%;
        height: 32px;
        padding: 0 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #f6f7fb;
        & > div:first-child {
          color: #3a3a3a;
          font-weight: 600;
        }
        & > div:last-child {
          color: #999999;
        }
      }
      .check-all-container {
        padding: 0 6px;
        .ouryun-checkbox {
          padding: 0 9px;
          height: 28px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          &:hover {
            background-color: #f3f3f3;
          }
          .ouryun-checkbox {
            width: 100%;
          }
        }
      }
    }

    .ouryun-select-dropdown__list .ouryun-select-dropdown__item {
      background-color: #fff !important;
      &:hover {
        background-color: #f3f3f3 !important;
      }
    }
  }
</style>
<style lang="scss" scoped>
  ::v-deep .ouryun-table.matchedConditionEditor {
    $table-border-color: var(--ouryun-table-border-color);
    border-radius: 2px;
    box-sizing: content-box;
    .ouryun-table__inner-wrapper::before {
      background-color: transparent;
    }

    th.ouryun-table__cell.is-leaf {
      border: none;
    }
    .ouryun-table__header-wrapper {
      height: 40px;
      border-bottom: 1px solid $table-border-color;
      box-sizing: border-box;
    }

    .ouryun-table__cell {
      padding: 0;
      height: 40px;
      box-sizing: border-box;
    }

    .ouryun-table__body-wrapper .ouryun-table__cell {
      &:not(.is-center) {
        .cell {
          height: 38px;
          display: flex;
          align-items: center;
        }
      }
      &.ouryun-table-fixed-column--right {
        .cell {
          background-color: #fff;
        }
      }
    }

    td.ouryun-table__cell {
      border-bottom: none !important;
    }
    .ouryun-table__row {
      background-color: transparent;
      box-shadow: 0 0 0 0.5px $table-border-color;
      &:last-child {
        box-shadow:
          0 1px 0 0 $table-border-color,
          0 0 0 0.5px $table-border-color;
      }
      &:hover {
        background-color: #f5f7fa;
        box-shadow: 0 0 0 1px $table-border-color;
      }
    }
    tr:hover > td.ouryun-table__cell {
      background-color: transparent;
    }
  }

  .field-option {
    position: relative;
    ::v-deep .popup {
      position: relative;
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      background-color: #fff;
      font-size: 12px;
      line-height: 1;
      color: #333;
      padding: 10px;
      border-radius: 7px;
      z-index: 999;
      box-shadow:
        0px 12px 48px 16px rgba(0, 0, 0, 0.03),
        0px 9px 28px 0px rgba(0, 0, 0, 0.05),
        0px 6px 16px -8px rgba(0, 0, 0, 0.08);
    }
  }

  .plugin-matched-condition-editor {
    $table-border-color: #dedede;
    box-shadow: 0 0 0 1px $table-border-color;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1px;
    .ouryun-table {
      border-radius: 2px 2px 0 0;
    }

    ::v-deep {
      .ouryun-table .cell {
        overflow: visible;
        padding-right: 0 !important;
        .ou-text-ellipsis {
          padding-left: 8px;
        }
      }

      .ouryun-table .ouryun-table__header tr th.ouryun-table__cell:not(:last-child) .cell {
        padding-left: 16px !important;
      }

      .ouryun-table .ouryun-table__cell {
        z-index: auto;
        .cell {
          width: 100%;
          display: flex;
          align-items: center;
          height: 32px;
          line-height: 32px;
          padding-right: 0;
          padding-left: 10px;
          & > div:not(.ouryun-select):not(.ouryun-input):not(.cell-verify-content) {
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .matched-condition-editor-footer {
      width: calc(100% + 4px);
      height: 42px;
      display: flex;
      align-items: center;
      padding: 0 10px;
      color: #666666;
      box-shadow: 0 0 0 1px #dedede inset;
      margin: 0 -2px -2px;
    }

    ::v-deep {
      .ouryun-input__wrapper,
      .ouryun-select__wrapper {
        box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color)) inset !important;
      }

      .ouryun-select__tags .ouryun-select__tags-text {
        max-width: none !important;
      }

      .ouryun-table__body-wrapper tbody .ouryun-table__row {
        @mixin errorMessage {
          white-space: nowrap;
          height: 36px;
          width: auto;
          font-size: 14px;
          line-height: 36px;
          padding: 0 10px;
          background-color: #ff6d6d;
          color: rgba(255, 255, 255, 0.85);
          position: absolute;
          border-radius: 3px;
          z-index: 999;
          &::before {
            content: "";
            position: absolute;
            left: 20px;
            top: calc(100% - 47px);
            width: 0px;
            height: 0px;
            border: 6px solid transparent;
          }
        }
        .cell-verify-content {
          position: relative;
          width: 100%;
          .ouryun-input__wrapper,
          .ouryun-select__wrapper {
            box-shadow: 0 0 0 1px var(--ouryun-color-danger) inset !important;
          }
        }

        &:last-child .cell-verify {
          @include errorMessage;
          top: -44px;
          &::before {
            top: 100%;
            border-top-color: #ff6d6d;
          }
        }
        .cell-verify,
        &:nth-child(-n + 2) .cell-verify {
          @include errorMessage;
          top: 40px;
          &::before {
            top: calc(100% - 47px);
            border-bottom-color: #ff6d6d;
          }
        }

        .cell-verify:empty {
          display: none;
        }
      }

      .ouryun-table.matchedConditionEditor.showAny .ouryun-table__cell .cell {
        padding-left: 16px !important;
      }
    }
  }
</style>
