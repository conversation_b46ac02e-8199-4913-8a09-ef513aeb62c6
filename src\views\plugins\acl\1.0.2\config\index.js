import { ref, reactive } from "vue";
import { OuModal } from "@ouryun/ouryun-plus";
import { deepClone } from "@/utils/tool.js";
import { findIp } from "./utils";
const useTable = () => {
  const tabHeids = ref([
    {
      prop: "prior",
      label: "优先级",
      minWidth: 75,
      Tooltip: false,
    },
    {
      prop: "name",
      label: "规则名称",
      minWidth: 130,
    },
    { prop: "proto", label: "协议类型", minWidth: 100, Tooltip: false },
    {
      prop: "saddr",
      label: "源IP",
      minWidth: 140,
      Tooltip: false,
    },
    {
      prop: "sport",
      label: "源端口",
      minWidth: 140,
      Tooltip: false,
    },
    {
      prop: "daddr",
      label: "目的IP",
      minWidth: 140,
      Tooltip: false,
    },
    {
      prop: "dport",
      label: "目的端口",
      minWidth: 140,
      Tooltip: false,
    },
    {
      prop: "action",
      label: "动作",
      minWidth: 140,
      Tooltip: false,
    },
    {
      prop: "remark",
      label: "描述",
      minWidth: 140,
    },
    {
      prop: "enable",
      label: "状态",
      minWidth: 140,
      Tooltip: false,
    },
  ]);
  const tableConfig = {
    isColumnDisplay: true,
    isSelect: true,
    treeProps: { children: "children", hasChildren: "hasChildren" },
    sequence: false,
    rowClass: "kkkkk",
    sequenceFixed: true,
  };

  const selectConfig = {
    oneList: [
      { prop: "name", type: "input", placeholder: "请输入", propName: "规则名称" },
      { prop: "saddr", type: "input", placeholder: "请输入", propName: "源IP" },
      { prop: "daddr", type: "input", placeholder: "请输入", propName: "目的IP" },
      {
        prop: "proto",
        type: "select",
        placeholder: "请选择",
        propName: "协议",
        options: [
          { name: "TCP", value: 1 },
          { name: "UDP", value: 2 },
          { name: "ICMP", value: 3 },
        ],
        selectList: {
          label: "name",
          value: "value",
          clearable: true,
        },
      },
      {
        prop: "action",
        type: "select",
        placeholder: "请选择",
        propName: "动作",
        options: [
          { name: "拒绝", value: "2" },
          { name: "放行", value: "1" },
        ],
        selectList: {
          label: "name",
          value: "value",
          clearable: true,
        },
      },
    ],
  };

  const searchForm = reactive({
    page: 1,
    pageSize: 10,
    name: "",
    action: "", // 动作
    proto: "", // 协议
    saddr: "", // 源IP
    daddr: "", // 目的IP
  });

  class TableData {
    allTables = []; // 所有数据
    tempTables = [];
    constructor(resource) {
      const temp = resource.map(item => {
        return {
          ...item,
        };
      });
      this.allTables = temp;
      this.tempTables = temp;
    }
    total() {
      return this.tempTables.length;
    }

    filteredConfigs(form) {
      const { name = "", action = "", proto = "", saddr = "", daddr = "" } = form;
      console.log(form);
      this.tempTables = this.allTables.filter(c => {
        let hasName = c.name.toUpperCase().indexOf(name.toUpperCase()) !== -1;
        // 动作为空，值为""即不参与筛选;
        let hasAction = action === "" ? true : c.action == action;
        // 协议为空，值为""即不参与筛选;
        let hasProto = c.proto === 0 || proto === "" ? true : c.proto == proto;
        // 源IP
        let hasSaddr = saddr === "" ? true : findIp(c.saddr, saddr);
        // 目的IP
        let hasDaddr = daddr === "" ? true : findIp(c.daddr, daddr);
        return hasName && hasAction && hasProto && hasSaddr && hasDaddr;
      });
      return this;
    }
    currentPage(form) {
      const { page, pageSize } = form;
      return this.tempTables.slice((page - 1) * pageSize, page * pageSize);
    }
    create(form) {
      return {
        table: this.currentPage(form),
        sum: this.total(),
      };
    }

    search(form) {
      return {
        table: this.filteredConfigs(form).currentPage(form),
        sum: this.total(),
      };
    }
  }

  const updateTableRows = (table, form, type, status) => {
    let rows = deepClone(form);
    if (!Array.isArray(rows)) {
      rows = [rows];
    }
    let hasSameName = rows.some((item, index, arr) => {
      return table.some(t => item?.id !== t.id && item.name === t.name);
    });
    if (hasSameName) {
      OuModal.warning(`规则名称${rows[0].name}已存在`);
      return false;
    }
    if (type == "新增") {
      rows = rows.map((i, index) => ({
        ...i,
        enable: true,
        id: new Date().getTime() + index,
        saddr: Array.isArray(i.saddr) ? i.saddr.join(",") : i.saddr || "",
        daddr: Array.isArray(i.daddr) ? i.daddr.join(",") : i.daddr || "",
      }));
      return [...rows, ...table].map((i, index) => ({ ...i, prior: index + 1 }));
    }

    if (type == "编辑") {
      // 编辑表单 字段更新
      return table.map((i, index) => {
        let currentRow = rows.find(row => row.id === i.id);
        return currentRow?.id
          ? {
              ...currentRow,
              saddr: currentRow.saddr ? currentRow.saddr.join(",") : "",
              daddr: currentRow.daddr ? currentRow.daddr.join(",") : "",
            }
          : i;
      });
    }

    // 启用禁用
    return table.map(i => ({ ...i, enable: rows.some(r => r.id === i.id) ? status : i.enable }));
  };

  return {
    searchForm,
    tabHeids,
    tableConfig,
    selectConfig,
    TableData,
    updateTableRows,
  };
};

export default useTable;
