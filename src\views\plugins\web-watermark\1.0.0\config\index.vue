<template>
  <!--  数据水印 -->
  <div class="plugin-dwc-creat-configuration add_conent_flex1">
    <ou-form label-width="140px" label-position="right" ref="formRef" :rules="formRules" :model="form">
      <ou-form-item label="匹配条件：" prop="matchingList">
        <matchedConditionEditor
          ref="matchedConditionEditorRef"
          v-model="form.matchers"
          :fieldOptions="['srcIp', 'path', 'requestHeader', 'queryParameter', 'userName']"
          style="width: 100%"></matchedConditionEditor>
      </ou-form-item>
      <div class="form-second">
        <div class="form-second-left">
          <ou-form-item label="水印类型：" prop="type">
            <ou-select v-model="form.type" :clearable="false">
              <ou-option
                v-for="(item, index) in typeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"></ou-option>
            </ou-select>
            <ou-tooltip-icon
              class="form-item-tooltip-icon"
              content="当前支持对HTML网页添加水印（即 屏幕水印） "></ou-tooltip-icon>
          </ou-form-item>
          <ou-form-item label="水印内容：" prop="water_mark_context">
            <ou-select
              v-model="form.water_mark_context"
              multiple
              :filterable="true"
              :allow-create="allowCreateAbled"
              :reserve-keyword="false"
              default-first-option
              placeholder="请选择或输入内容后按回车确认"
              no-match-text="最多支持添加10项"
              @keydown="handleKeyDown"
              @change="handleTagChange">
              <ou-option
                v-for="(item, index) in watermarkContextOptions"
                :key="index"
                :label="item.label"
                :value="item.value"></ou-option>
            </ou-select>
            <ou-tooltip-icon
              class="form-item-tooltip-icon"
              raw-content
              content="<span>（1）除可选择的水印内容外，支持自定义添加最多10项水印内容；自定义时，在输入框输入内容后按回车键确定；</span><br /><span>（2）因未识别到用户和账号信息，致使水印内容为空时，默认使用“源IP”-“应用名称”-“当前时间”为水印内容</span>"></ou-tooltip-icon>
          </ou-form-item>
          <template v-if="form.type === 'BRIGHT'">
            <ou-form-item label="字体颜色：" prop="font_color">
              <ou-input v-model="form.font_color" @blur="fontColorInputBlur" :clearable="false">
                <template #suffix>
                  <el-color-picker v-model="fontColorComputed" />
                </template>
              </ou-input>
            </ou-form-item>
            <ou-form-item label="透明度：" prop="transparency">
              <ou-input v-model.number="form.transparency" :clearable="false">
                <template #append>
                  <span>%</span>
                </template>
              </ou-input>
            </ou-form-item>
          </template>
          <ou-form-item label="字体大小：" prop="font_size">
            <ou-select v-model="form.font_size" :clearable="false">
              <ou-option
                v-for="(item, index) in fontSizeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"></ou-option>
            </ou-select>
          </ou-form-item>
          <ou-form-item label="倾斜方式：" prop="tilt_method">
            <ou-select v-model="form.tilt_method" :clearable="false">
              <ou-option
                v-for="(item, index) in tiltMethodOptions"
                :key="index"
                :label="item.label"
                :value="item.value"></ou-option>
            </ou-select>
          </ou-form-item>
          <ou-form-item label="密度：" prop="density">
            <ou-select v-model="form.density" :clearable="false">
              <ou-option
                v-for="(item, index) in densityOptions"
                :key="index"
                :label="item.label"
                :value="item.value"></ou-option>
            </ou-select>
          </ou-form-item>
        </div>
        <div class="form-second-right">
          <div class="preview-box">
            <div class="preview-box-header">
              <span>水印预览</span>
              <template v-if="form.type === 'DARK'">
                <ou-switch v-model="previewSwitch" class="on-off" />
                <ou-tooltip-icon
                  content="<span>除可选择的水印内容外，支持自定义水印内容；</span><br /><span>自定义时，在输入框输入内容后按回车键确定</span>">
                  <template #content>
                    <div style="display: flex; flex-direction: column">
                      <div>暗水印默认透明度为98%，以肉眼不可见的形式存在</div>
                      <div>
                        <span>暗水印解码在</span>
                        <span>「</span>
                        <span
                          @click="toDarkWatermarkDecode"
                          style="color: var(--ouryun-color-brand-base); cursor: pointer">
                          实验室-工具箱-暗水印解码
                        </span>
                        <span>」</span>
                        <span>处操作</span>
                      </div>
                    </div>
                  </template>
                </ou-tooltip-icon>
              </template>
            </div>
            <div class="preview-box-content">
              <el-watermark
                :font="font"
                :rotate="rotate"
                :gap="gap"
                :content="watermarkContextComputed"
                style="height: 100%">
                <el-skeleton :rows="10" />
              </el-watermark>
            </div>
          </div>
        </div>
      </div>
    </ou-form>
  </div>
</template>
<script setup>
  import { defineProps, defineExpose, computed } from "vue";
  import { OuModal } from "@ouryun/ouryun-plus";

  import matchedConditionEditor from "@/components/NewMatchedConditionEditor/index.vue";
  import { getMatchParameter, compatibleMatchingList } from "@/utils/MatchedCondition.js";
  import hookStates from "../hook/index.js";
  import usePlugin from "basePluginStore";
  const store = usePlugin();

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
  });

  const {
    regHex,
    matchedConditionEditorRef,
    defaultConfig,
    form,
    formRef,
    formRules,
    typeOptions,
    newWatermarkContextOptions,
    watermarkContextOptions,
    fontSizeOptions,
    tiltMethodOptions,
    densityOptions,
    fontColorComputed,
    watermarkContextComputed,
    font,
    rotate,
    gap,
    previewSwitch,
  } = hookStates();

  const fontColorInputBlur = () => {
    const oldFontColor = form.font_color.trim().toUpperCase();
    let newFontColor = oldFontColor.includes("#") ? oldFontColor : `#${oldFontColor}`;
    if (!regHex.test(newFontColor) && newFontColor !== "#") {
      newFontColor = defaultConfig.color;
      OuModal.warning("无效的颜色代码");
    }

    if (newFontColor.length === 4) {
      // 将3位十六进制色值转为6位十六进制色值
      newFontColor = newFontColor.replace(
        /#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/,
        (_, r, g, b) => `#${r}${r}${g}${g}${b}${b}`,
      );
    }

    form.font_color = newFontColor;
  };

  const onCreated = () => {
    if (props.content || props.global) {
      initForm();
    }
  };

  const initForm = () => {
    const content = JSON.parse(props.content || props.global);
    const rule = content.plugin_config.rule;

    ["type", "font_size", "tilt_method", "density"].forEach(field => {
      form[field] = rule[field];
    });

    if (rule.type === "BRIGHT") {
      // 明水印
      ["font_color", "transparency"].forEach(field => {
        form[field] = rule[field];
      });
    }

    const creatOptions = [];

    form.water_mark_context = rule.water_mark_context.map(item => {
      if (item.water_mark_context_type === "CUSTOM") {
        const custom_water_mark_context = item.custom_water_mark_context;
        creatOptions.push({
          label: custom_water_mark_context,
          value: custom_water_mark_context,
          content: `{${custom_water_mark_context}}`,
        });
        return custom_water_mark_context;
      } else {
        return item.water_mark_context_type;
      }
    });

    watermarkContextOptions.value = [...newWatermarkContextOptions, ...creatOptions];

    form.matchers = compatibleMatchingList(content.meta_data.matchers);
  };

  const allowCreateAbled = computed(() => {
    const customOptions = watermarkContextOptions.value.filter(
      option => !["SRCIP", "ACCOUNT", "APPNAME", "CURTIME"].includes(option.value),
    );

    return customOptions.length < 10;
  });

  // 输入回车的数据处理
  const handleTagChange = value => {
    const newValue = value.filter(
      item => item.length <= 63 || ["SRCIP", "ACCOUNT", "APPNAME", "CURTIME"].includes(item),
    );

    if (newValue.length !== value.length) {
      OuModal.warning("单个选项最多输入63个字符");
    }

    const creatOptions = [];

    newValue.forEach(valueItem => {
      if (!["SRCIP", "ACCOUNT", "APPNAME", "CURTIME"].includes(valueItem)) {
        creatOptions.push({
          label: valueItem,
          value: valueItem,
          content: `{${valueItem}}`,
        });
      }
    });

    form.water_mark_context = newValue;
    watermarkContextOptions.value = [...newWatermarkContextOptions, ...creatOptions];
  };

  const handleKeyDown = event => {
    if (event.key === " ") {
      event.preventDefault();
    }
  };

  const toDarkWatermarkDecode = () => {
    const origin = window.location.origin;
    const darkWatermarkDecodeUrl = origin + "/#/laboratory/workBox/darkWatermarkDecode";
    window.open(darkWatermarkDecodeUrl);
  };

  const submit = async () => {
    const matchedConditionValid = matchedConditionEditorRef.value.validator();
    return await Promise.all([formRef.value.validate(valid => valid)]).then(res => {
      if (res.every(item => item) && matchedConditionValid) {
        const rule = {
          water_mark_context: [],
        };
        ["type", "font_size", "tilt_method", "density"].forEach(field => {
          rule[field] = form[field];
        });

        if (form.type === "BRIGHT") {
          // 明水印
          ["font_color", "transparency"].forEach(field => {
            rule[field] = String(form[field]);
          });
        }

        rule.water_mark_context = form.water_mark_context.map(item => {
          if (["SRCIP", "ACCOUNT", "APPNAME", "CURTIME"].includes(item)) {
            return {
              custom_water_mark_context: "",
              water_mark_context_type: item,
            };
          } else {
            return {
              water_mark_context_type: "CUSTOM",
              custom_water_mark_context: item,
            };
          }
        });

        rule.matchers = getMatchParameter(form.matchers).match;

        const params = {
          plugin_config: {
            rule,
          },
          meta_data: {},
        };

        params.meta_data.matchers = form.matchers;

        const extension = {
          refs: {
            // 匹配条件
            ip_address_database: [], // 网段
          },
        };
        const ipAddressArr = [];

        // 匹配条件
        params.meta_data.matchers.forEach(matchItem => {
          if (matchItem.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic)) {
            ipAddressArr.push(...matchItem.content);
          }
        });

        const configs = [{ key: "ip_address_database", arr: ipAddressArr }];

        configs.forEach(({ key, arr }) => {
          extension.refs[key] = [...new Set(arr)].map(item => ({ item_id: item }));
        });

        return {
          contentParams: params,
          extension,
        };
      } else {
        throw new Error("表单校验失败");
      }
    });
  };

  onCreated();

  defineExpose({
    submit,
  });
</script>

<style lang="scss" scoped>
  .plugin-dwc-creat-configuration {
    .form-second {
      display: flex;
      justify-content: space-between;
      column-gap: 60px;

      .form-second-left {
        width: 440px;
      }

      .form-second-right {
        flex: 1;
        .preview-box {
          height: calc(100% - 20px);
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          display: flex;
          flex-direction: column;
          .preview-box-header {
            width: 100%;
            height: 36px;
            border-bottom: 1px solid #d9d9d9;
            display: flex;
            justify-content: center;
            align-items: center;
            column-gap: 8px;
          }
          .preview-box-content {
            flex: 1;
            width: 100%;
            overflow: hidden;
            position: relative;
            .ouryun-skeleton {
              width: auto;
              overflow: hidden;
              position: absolute;
              top: 20px;
              bottom: 20px;
              left: 20px;
              right: 20px;
            }
          }
        }
      }
    }

    .ouryun-form-item-custom {
      position: relative;
      .form-item-tooltip-icon {
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    :deep(.ouryun-input) {
      .ouryun-input__suffix .ouryun-color-picker__trigger {
        border: none;
      }
    }
  }
</style>
