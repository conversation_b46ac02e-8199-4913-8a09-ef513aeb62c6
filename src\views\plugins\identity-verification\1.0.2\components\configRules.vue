<template>
  <!-- 1166px -->
  <ou-opera-dialog
    :close-on-click-modal="false"
    width="1172px"
    draggable
    :title="`${props.type}规则  `"
    @close="closeDialog"
    @cancel="cancel"
    @confirm="submit"
    v-model="isShowDialog">
    <template #content>
      <ou-form
        class="config-rule -mb-20"
        :model="form"
        ref="ConfigRuleRef"
        :rules="rules"
        labelPosition="right"
        label-width="140px">
        <ou-form-item label="规则名称：" prop="rule_name">
          <ou-input
            maxlength="63"
            show-word-limit
            placeholder="请输入"
            v-model="form.rule_name"
            style="width: 300px"></ou-input>
        </ou-form-item>
        <ou-form-item label="匹配条件：" prop="matchingList">
          <MatchedConditionEditor
            ref="matchRef"
            v-if="isShowDialog"
            style="width: 100%"
            :modelValue="form.matchingList"
            @update:modelValue="updateMatch"
            :fieldOptions="fieldOptions"
            generalPurposeLibrary
            :showAny="false"></MatchedConditionEditor>
        </ou-form-item>
        <ou-form-item label="认证信息：" prop="authInfo" class="auth-info-form-item">
          <AuthInfo
            ref="authInfoRef"
            v-if="isShowDialog"
            :modelValue="form.authInfo"
            @update:modelValue="updateAuthInfo" />
        </ou-form-item>
        <ou-form-item label="token有效期：" prop="token_exp">
          <!-- <ou-tooltip
            class="box-item"
            style="position: absolute; top: 1px; left: 309px"
            effect="light"
            popper-class="no-arrow"
            placement="bottom-start"
            width="400px"
            content="输入范围 1-720 小时，token有效期内无需重复登录">
            <ou-icon class="tip_icon_hover"><Notice /></ou-icon>
          </ou-tooltip> -->
          <ou-tooltip-icon
            style="position: absolute; left: 308px; font-size: 16px"
            trigger="hover"
            content="输入范围 1-720 小时，token有效期内无需重复登录"></ou-tooltip-icon>
          <ou-input
            type="text"
            placeholder="请输入token有效期，单位小时"
            v-model="form.token_exp"
            oninput="value=value.replace(/^\.+|[^\d.]/g,'')"
            style="width: 300px"></ou-input>
        </ou-form-item>
        <ou-form-item label="账号绑定IP：" prop="is_signal_IP">
          <ou-tooltip-icon
            style="position: absolute; left: 308px; font-size: 16px"
            trigger="hover"
            content="限制可使用该账号的IP，绑定后账号仅限首次登陆IP使用;
账号变更后，将重新绑定；请注意，该功能仅适用于静态IP场景，不适用于动态IP场景"></ou-tooltip-icon>

          <ou-select v-model="form.is_signal_IP" placeholder="请选择" class="w-300">
            <ou-option v-for="(v, k) in bindoptions" :key="k" :label="v.label" :value="v.value"></ou-option>
          </ou-select>
        </ou-form-item>
        <ou-form-item label="认证页面：" prop="has_verify_page" class="pn_required_label">
          <ou-select v-model="form.has_verify_page" placeholder="请选择" class="w-300">
            <ou-option v-for="(v, k) in authpageOptions" :key="k" :label="v.label" :value="v.value"></ou-option>
          </ou-select>
          <ou-button
            v-if="form.has_verify_page === true"
            style="margin-left: 10px"
            type="primary"
            text
            @click="preview('添加')">
            预览
          </ou-button>
        </ou-form-item>
        <ou-form-item label="账号传递：" prop="account_transfer" class="pn_required_label">
          <ou-tooltip
            class="box-item"
            style="position: absolute; left: 308px; font-size: 16px"
            effect="light"
            popper-class="no-arrow"
            placement="bottom-start">
            <ou-icon class="tip_icon_hover"><Notice /></ou-icon>
            <template #content>
              <div style="font-size: 14px; margin-bottom: 8px">账号传递位置</div>
              <ou-table :data="gridData">
                <ou-table-column width="180" property="address" label="传递位置" />
                <ou-table-column width="280" property="params" label="传递参数" />
              </ou-table>
            </template>
          </ou-tooltip>
          <ou-select v-model="form.account_transfer" placeholder="请选择" class="w-300">
            <ou-option v-for="(v, k) in accountTransferOptions" :key="k" :label="v.label" :value="v.value"></ou-option>
          </ou-select>
        </ou-form-item>
      </ou-form>
      <!-- <template #footer>
        <div class="dialog-footer">
          <ou-button @click="cancel">取消</ou-button>
          <ou-button type="primary" @click="submit">确定</ou-button>
        </div>
      </template> -->
    </template>
  </ou-opera-dialog>
</template>

<script setup>
  import { ref, defineProps, defineEmits, computed } from "vue";
  import { Notice } from "@ouryun-plus/icons-vue";
  import AuthInfo from "./authInfo.vue";
  import { regex as REGEX, OuModal } from "@ouryun/ouryun-plus";
  import { deepClone } from "@/utils/tool.js";
  import MatchedConditionEditor from "@/components/MatchedConditionEditor";
  import {
    initFormParams,
    bindoptions,
    authpageOptions,
    accountTransferOptions,
    validateMatch,
    validateInfoMatch,
    validateTokenExp,
  } from "../hook/utils.js";

  const gridData = [
    {
      address: "Request-header",
      params: "Stone-Rhino-Login-UserName",
    },
    {
      address: "Request-Query Params",
      params: "Stone-Rhino-Login-UserName",
    },
  ];

  const fieldOptions = ["srcIp", "path", "requestHeader", "requestMethod", "queryParameter"];

  const emit = defineEmits(["comfirm", "cancel"]);
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const matchRef = ref(null);
  const ConfigRuleRef = ref(null);
  const authInfoRef = ref(null);
  const form = ref({});
  const rules = ref({
    rule_name: [
      { required: true, message: "请输入规则名称", trigger: "change" },
      {
        required: true,
        pattern: REGEX.PG_NAME,
        message: "请输入中文、英文、数字、-和_",
        trigger: "change",
      },
    ],
    matchingList: [{ required: true, trigger: [], validator: validateMatch }],
    authInfo: [{ required: true, trigger: "change", validator: validateInfoMatch }],
    token_exp: [{ required: true, trigger: "change", validator: validateTokenExp }],
  });
  const preview = () => {
    let url = `${window.location.origin}/#/identityVerificationPage`;
    window.open(url, "_blank");
  };

  const isShowDialog = computed({
    get: () => {
      if (!props.modelValue || Object.keys(props.modelValue).length === 0) {
        form.value = deepClone(initFormParams);
      } else {
        form.value = deepClone(props.modelValue);
      }
      if (props.type === "复制") form.value.rule_name = `${props.modelValue.rule_name}_复制`;
      return props.visible;
    },
    set: val => {
      emit("close");
    },
  });

  const submit = () => {
    ConfigRuleRef.value.validate(valid => {
      if (!valid) return;
      let res = authInfoRef.value?.multilineEditorRef.beforeCommit();
      console.log("res", res);
      if (res instanceof Error) {
        return res.message && OuModal.warning(res.message);
      } else if (res) {
        form.value.authInfo = res;
      }
      emit("comfirm", form.value);
    });
  };
  const closeDialog = () => {
    ConfigRuleRef.value?.resetFields();
    emit("cancel");
  };

  const updateAuthInfo = value => {
    form.value.authInfo = value;
    ConfigRuleRef.value?.validateField("authInfo");
  };
  const updateMatch = value => {
    form.value.matchingList = value;
    ConfigRuleRef.value?.validateField("matchingList");
  };
  const cancel = () => {
    emit("cancel");
  };
</script>

<style scoped lang="scss">
  .configRule {
    width: 100%;
  }

  .w-300 {
    width: 300px !important;
  }

  .ouryun-form.ouryun-form--default > .ouryun-form-item-custom {
    margin-bottom: 0px !important;
  }
  .list-handle {
    display: flex;
  }
</style>
