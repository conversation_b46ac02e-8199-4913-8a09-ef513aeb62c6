{"name": "plugin-ui", "version": "3.8.4", "description": "插件配置", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "scripts": {"dev": "npm run preBuild && npm run serve", "preBuild": "node dynamic-build.mjs", "serve": "vite preview --port 8030 --strictPort --mode development", "build:prod": "node dynamic-build.mjs", "build:watch": "npx cross-env RESOURCE='data-masking/1.0.3' TARGET='data-masking/1.0.3' vite build  --emptyOutDir=false --watch", "build:watch2": "npx cross-env RESOURCE='identity-verification/1.0.2' TARGET='identity-verification/1.0.2' vite build  --emptyOutDir=false --watch", "build:watch1": "npx cross-env RESOURCE='weak-password-detection/1.0.2' TARGET='weak-password-detection/1.0.2' vite build  --emptyOutDir=false --watch", "build:watch3": "npx cross-env RESOURCE='ddc/1.0.0' TARGET='ddc/1.0.0' vite build  --emptyOutDir=false --watch", "build:watch4": "npx cross-env RESOURCE='application-acl/1.0.7' TARGET='application-acl/1.0.7' vite build  --emptyOutDir=false --watch", "build:watch5": "npx cross-env RESOURCE='ratelimit/1.0.9' TARGET='ratelimit/1.0.9' vite build  --emptyOutDir=false --watch", "build:watch6": "npx cross-env RESOURCE='data-replication-controller/1.0.0' TARGET='data-replication-controller/1.0.0' vite build  --emptyOutDir=false --watch", "build:watch7": "npx cross-env RESOURCE='waf/1.0.14' TARGET='waf/1.0.14' vite build  --emptyOutDir=false --watch", "build:watch8": "npx cross-env RESOURCE='cc-protection/1.0.4' TARGET='cc-protection/1.0.4' vite build  --emptyOutDir=false --watch", "build:watch9": "npx cross-env RESOURCE='dac/1.0.0' TARGET='dac/1.0.0' vite build  --emptyOutDir=false --watch", "build:watch10": "npx cross-env RESOURCE='file-watermark/1.0.0' TARGET='file-watermark/1.0.0' vite build  --emptyOutDir=false --watch", "build:watch11": "npx cross-env RESOURCE='acl/1.0.2' TARGET='acl/1.0.2' vite build  --emptyOutDir=false --watch", "clean": "<PERSON><PERSON><PERSON> dist", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://git.ouryun.cn/base/plugin-ui.git"}, "dependencies": {"@ouryun-plus/icons-vue": "rc", "@ouryun/ouryun-plus": "1.0.2-alpha.89", "@vueuse/core": "9.5.0", "axios": "0.27.2", "decimal.js": "^10.4.3", "file-saver": "2.0.5", "js-md5": "^0.8.3", "pinia": "2.0.22", "sortablejs": "^1.15.2", "ulid": "^2.3.0", "vue": "3.2.45", "vue-router": "4.1.4", "xlsx": "^0.18.5"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.9", "@vitejs/plugin-vue": "^5.1.4", "@vue/compiler-sfc": "3.2.45", "cross-env": "^7.0.3", "rimraf": "^6.0.1", "sass": "^1.79.5", "prettier": "3.5.2", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.27.2", "vite": "^5.4.2", "vite-plugin-vue-setup-extend": "0.4.0"}}