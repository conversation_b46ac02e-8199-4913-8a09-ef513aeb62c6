export const defaultConfig = [
  {
    ruleName: "内置规则-API接口防翻页遍历",
    quotaGroup: 1,
    nextStep: 0,
    matchingList: [{ field: "queryParameter", params: "page", logic: "presentMatchTrue", content: "", status: "view" }],
    action: false,
    requestQuota: [{ duration: 1, durationOption: 60, limit: 20 }],
    headName: "",
    enable: false,
    isDefault: true,
  },
  {
    ruleName: "内置规则-防账号撞库",
    quotaGroup: 1,
    nextStep: 0,
    matchingList: [{ field: "path", params: "", logic: "regex", content: "\^/(.*/)?(?:login|signin)", status: "view" }],
    action: false,
    requestQuota: [{ duration: 1, durationOption: 1, limit: 20 }],
    headName: "",
    enable: true,
    isDefault: true,
  },
  {
    ruleName: "内置规则-防高频异常攻击",
    quotaGroup: 1,
    nextStep: 0,
    matchingList: [],
    action: false,
    requestQuota: [
      { duration: 1, durationOption: 60, limit: 1500 },
      { duration: 1, durationOption: 1, limit: 30 },
    ],
    headName: "",
    enable: true,
    isDefault: true,
  },
  {
    ruleName: "内置规则-防验证码爆破",
    quotaGroup: 1,
    nextStep: 0,
    matchingList: [
      {
        field: "path",
        params: "",
        logic: "regex",
        content: "^/(.*/)?(?:captcha/validate|(?:verify|validate|check)Code)",
        status: "view",
      },
    ],
    action: false,
    requestQuota: [{ duration: 1, durationOption: 60, limit: 10 }],
    headName: "",
    enable: false,
    isDefault: true,
  },
  {
    ruleName: "内置规则-防短信炸弹",
    quotaGroup: 1,
    nextStep: 0,
    matchingList: [
      {
        field: "path",
        params: "",
        logic: "regex",
        content: "^/(.*/)?(?:sendSMS|sms/send|message/send|notification/send)",
        status: "view",
      },
    ],
    action: false,
    requestQuota: [{ duration: 1, durationOption: 60, limit: 10 }],
    headName: "",
    enable: false,
    isDefault: true,
  },
  {
    ruleName: "内置规则-防恶意注册",
    quotaGroup: 1,
    nextStep: 0,
    matchingList: [
      {
        field: "path",
        params: "",
        logic: "regex",
        content: "^/(.*/)?(?:register|signup|create-account)",
        status: "view",
      },
    ],
    action: false,
    requestQuota: [{ duration: 1, durationOption: 60, limit: 10 }],
    headName: "",
    enable: false,
    isDefault: true,
  },
  {
    ruleName: "内置规则-防频繁下载",
    quotaGroup: 1,
    nextStep: 0,
    matchingList: [
      {
        field: "path",
        params: "",
        logic: "regex",
        content: "^/(.*/)?(?:download|file|export|attachment)",
        status: "view",
      },
    ],
    action: false,
    requestQuota: [{ duration: 1, durationOption: 60, limit: 10 }],
    headName: "",
    enable: false,
    isDefault: true,
  },
  {
    ruleName: "内置规则-账号异常",
    quotaGroup: 3,
    nextStep: 0,
    matchingList: [],
    action: false,
    requestQuota: [
      { duration: 1, durationOption: 60, limit: 1500 },
      { duration: 1, durationOption: 1, limit: 30 },
    ],
    headName: "",
    enable: true,
    isDefault: true,
  },
];
