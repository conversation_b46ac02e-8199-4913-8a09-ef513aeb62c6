<template>
  <!-- <div v-bind="containerProps" style="height: 200px; border: 1px solid red">
    <div v-bind="wrapperProps">
      {{ list }}
      <div
        v-for="{ index, data } in list"
        :key="index"
        class="border mb-2"
        :style="{
          height: `40px`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }">
        {{ data.password }}
        <span opacity="70" m="l-1">ddddd</span>
      </div>
    </div>
  </div> -->
  <ou-collapse-panel v-model:isCollapse="isCollapse">
    <template #headerLeft>
      <div class="search-left">
        <transition name="ani" appear style="width: 240px">
          <div v-show="inputVisible" class="animated fadeInLeft">
            <ou-input
              ref="searchInputRef"
              @clear="inputBlur"
              @blur="inputBlur"
              @focus="inputFocus"
              clearable
              placeholder="快速查找"
              v-model="searchName"
              @keyup.enter.native="emitSearch"
              style="width: 100%">
              <template #suffix>
                <ou-icon @click="emitSearch" style="cursor: pointer"><Search /></ou-icon>
              </template>
            </ou-input>
          </div>
        </transition>
        <transition name="ani" appear>
          <ou-icon class="animated fadeIn" @click="inputIconClick" v-show="!inputVisible"><Search /></ou-icon>
        </transition>
      </div>
    </template>
    <template #headerRight>
      <div class="header-right">
        <ou-icon-group>
          <ou-icon :size="20" @click="emit('importAdd')"><Shangchuan1 /></ou-icon>
          <ou-icon :size="20" @click="emit('exportAdd')"><Xiazai1 /></ou-icon>
        </ou-icon-group>
      </div>
    </template>
    <template #content>
      <div class="content" ref="rows">
        <!-- {{ form }} -->
        <!-- {{ validatorStatus }}
        {{  props.modelValue }} -->
        <template v-if="searchResult">
          <template v-for="(valueItem, valueIndex) in props.modelValue" :key="valueIndex">
            <div class="custome-multiline-editor-row" v-if="!(searchName && valueItem.show === false)">
              <div class="input-group">
                <template v-for="(configItem, configIndex) in form" :key="configItem.prop">
                  <div
                    :data-error-text="validatorStatus?.[valueIndex]?.[configIndex]"
                    :class="{ 'validator-error': validatorStatus?.[valueIndex]?.[configIndex] }">
                    <ou-input
                      v-if="configItem.type === 'input'"
                      @input="triggerHandler(valueItem, configItem.prop, valueIndex, configIndex)"
                      @clear="triggerHandler(valueItem, configItem.prop, valueIndex, configIndex)"
                      v-model.lazy="valueItem[configItem.prop]"
                      v-bind="configItem.attrs"
                      style="width: 100%"></ou-input>
                    <ou-date-picker
                      v-else-if="configItem.type === 'date'"
                      style="width: 100%"
                      v-model="valueItem[configItem.prop]"
                      type="date"
                      placeholder="长期有效"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      v-bind="configItem.attrs"></ou-date-picker>
                  </div>
                </template>
              </div>
              <div class="button-group">
                <ou-button @click="delHander(valueIndex)" v-if="props.modelValue.length > 1">
                  <ou-icon color="#999"><Minus /></ou-icon>
                </ou-button>
                <template v-if="props.modelValue.length <= valueIndex + 1 && props.modelValue.length < props.maxLength">
                  <ou-button
                    @click="addHander(valueIndex)"
                    :disabled="validatorStatus?.[valueIndex]?.some(item => item)">
                    <ou-icon color="#999"><Plus /></ou-icon>
                  </ou-button>
                </template>
              </div>
            </div>
          </template>
        </template>
        <div v-else class="empty-info">
          <OuIcon :size="120">
            <NoData />
          </OuIcon>
          <div class="empty-text">暂无数据</div>
        </div>
      </div>
    </template>
  </ou-collapse-panel>
</template>
<script setup name="OuMultilineEditor">
  import { ref, watch, defineEmits, defineProps, defineExpose, computed, transition, nextTick } from "vue";
  import { Plus, Minus } from "@ouryun-plus/icons-vue";
  import { OuButton, OuModal, OuIcon } from "@ouryun/ouryun-plus";
  import { NoData } from "@ouryun-plus/icons-vue";
  import { useScrollbar } from "../hook/sc.js";
  // import { useVirtualList } from "@vueuse/core";
  const { setScrollTop } = useScrollbar();
  const isCollapse = ref(true);
  const props = defineProps({
    modelValue: {
      required: true,
      type: Array,
    },
    maxLength: {
      type: Number,
      default: 1000,
    },
  });

  const emit = defineEmits(["update:modelValue", "search", "headClick", "exportAdd", "importAdd"]);

  const searchName = ref("");
  const isInputFocus = ref(false);
  const inputVisible = computed(() => {
    return isInputFocus.value || searchName.value;
  });
  watch(inputVisible, newVal => {
    if (!newVal) {
      nextTick(() => {
        if (!searchName.value) emitSearch();
      });
    }
  });

  const searchInputRef = ref(null);
  const inputIconClick = () => {
    isInputFocus.value = true;
    // nextTick(() => searchInputRef.value?.focus()); //todo
  };

  const inputBlur = () => {
    isInputFocus.value = false;
  };
  const inputFocus = () => {
    isInputFocus.value = true;
  };
  // 记录校验是否已被触发
  const triggerList = ref([]);
  // 校验每行没咧是否存在错误提示
  const validatorStatus = ref([]);

  // const { list, containerProps, wrapperProps } = useVirtualList(props.modelValue, {
  //   itemHeight: 22,
  //   overscan: 10,
  // });
  // console.log("list", list);
  // list.value = a.list;
  // containerProps.value = a.containerProps;
  // wrapperProps.value = a.wrapperProps;

  const form = [
    {
      type: "input",
      prop: "user_name",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "请输入账号名称",
      },
    },
    {
      type: "input",
      prop: "password",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "请输入密码",
      },
    },
    {
      type: "date",
      prop: "exp",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "长期有效",
      },
    },
    {
      name: "备注",
      prop: "remark",
      type: "input",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "请输入备注信息",
      },
    },
  ];
  let formObj = {};

  const onCreated = () => {
    form.forEach(item => {
      formObj[item.prop] = item.defaultValue;
    });
    if (!props.modelValue.length) {
      props.modelValue.push(JSON.parse(JSON.stringify(formObj)));
    }
    setTimeout(() => {
      initState();
    }, 300);
  };

  // 初始化校验状态
  const initState = () => {
    validatorStatus.value = props.modelValue?.map(column => form.map(row => validatorCellMessage(row.prop, column)));
  };

  const validatorCellMessage = (property, row) => {
    if (!property || !row) {
      return;
    }
    let verifyText = "";
    const user_nameCheck = /^[0-9a-zA-Z_-]{1,63}$/;
    const passwordCheck = /^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*?_-]).{8,63}$/;
    const remarkCheck = /^[\u4e00-\u9fa5_a-zA-Z0-9-]+$/;
    const content = row[property];

    switch (property) {
      case "user_name":
        if (!content.length) {
          verifyText = "请输入账号名称";
        } else if (content.length > 63) {
          verifyText = "长度不多于63个字符   ";
        } else if (!user_nameCheck.test(content)) {
          verifyText = "请输入英文、数字、-和_";
        } else {
          if (props.modelValue.length > 1) {
            const newArr = props.modelValue.filter(item => item.user_name === content);
            if (newArr.length > 1) {
              verifyText = `账号名称不能重复`;
            }
          }
        }
        break;
      case "password":
        if (!content.length) {
          verifyText = "请输入密码";
        } else if (content.length < 8) {
          verifyText = "长度不少于8个字符";
        } else if (content.length > 63) {
          verifyText = "长度不多于63个字符";
        } else if (!passwordCheck.test(content)) {
          verifyText = "同时含大小写字母、数字、特殊符号";
        }
        break;
      case "remark":
        if (content.length > 63) {
          verifyText = "长度限制为63个字符";
        } else if (content.length && !remarkCheck.test(content)) {
          verifyText = "请输入中文、英文、数字、-和_";
        }
        break;
    }
    return verifyText;
    // };
  };
  // 删除一行
  const delHander = async index => {
    function delRow() {
      props.modelValue.splice(index, 1);
      validatorStatus.value.splice(index, 1);
    }
    delRow();
  };
  const addRow = index => {
    props.modelValue.splice(index + 1, 0, JSON.parse(JSON.stringify(formObj)));
    validatorStatus.value.splice(
      index + 1,
      0,
      form.map(item => validatorCellMessage(item.prop, formObj)),
    );
  };
  // 添加一行
  const addHander = async index => {
    if (props.maxLength !== null && props.modelValue.length >= props.maxLength) {
      return OuModal.warning("最多添加" + props.maxLength + "条");
    }
    addRow(index);
    toScrollBottom();
  };
  // 添加多行
  const addmultilineHander = async sussessArray => {
    props.modelValue.push(...sussessArray);
    toScrollBottom();
    let temp = [];
    sussessArray?.forEach(e => {
      temp.push(form.map(item => validatorCellMessage(item.prop, e)));
    });
    validatorStatus.value.push(...temp);
  };

  const toScrollBottom = async () => {
    await nextTick();
    setScrollTop(rows.value, 0);
    rows.value.scrollTop = props.modelValue.length * 40;
  };
  // 触发当前单元格校验
  const triggerHandler = (valueItem, prop, valueIndex, configIndex) => {
    let message = validatorCellMessage(prop, valueItem);
    validatorStatus.value[valueIndex][configIndex] = message;
  };
  const clearValidator = () => {
    triggerList.value = props.modelValue.map(() => props.form.map(() => false));
  };

  const rows = ref();

  const getValidatorStatus = () => {
    let isErrorIndex = 0;
    let isError = false;
    for (let i = 0; i < validatorStatus.value.length; i++) {
      let row = validatorStatus.value[i];
      if (row.some(item => item)) {
        isErrorIndex = i;
        isError = true;
        break;
      }
    }
    console.log("isErrorIndex", isErrorIndex);
    if (isError) {
      const target = rows.value?.children[isErrorIndex];
      target?.scrollIntoView();
    }
    return isError;
  };

  defineExpose({
    clearValidator,
    addmultilineHander,
    beforeCommit: () => {
      searchName.value = "";
      isCollapse.value = true;
      if (getValidatorStatus()) {
        return new Error("请检查认证信息是否通过校验");
      }
      return props.modelValue;
    },
  });
  onCreated();

  const emitSearch = () => {
    isCollapse.value = true;
    console.log("emitSearch", searchName.value);
    searchAuthInfo(searchName.value);
  };
  const searchResult = ref(true);
  const searchAuthInfo = searchname => {
    if (searchname) {
      let c = 0;
      props.modelValue?.forEach(item => {
        const user_name = item.user_name?.toUpperCase();
        const remark = item.remark ? item.remark.toUpperCase() : "";
        let show =
          user_name.indexOf(searchname.toUpperCase()) !== -1 || remark.indexOf(searchname.toUpperCase()) !== -1;
        c += show ? 1 : 0;
        item.show = show;
      });
      searchResult.value = c > 0;
    } else {
      props.modelValue?.forEach(item => (item.show = true));
      searchResult.value = true;
    }
  };
</script>
<style lang="scss" scoped>
  @keyframes fadeInLeft {
    0% {
      opacity: 0;
      -webkit-transform: translate3d(100%, 0, 0);
      -ms-transform: translate3d(100%, 0, 0);
      transform: translate3d(100%, 0, 0);
    }

    100% {
      opacity: 1;
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  $animationTimeline: 0.3s;

  .ani-enter-active {
    &.fadeIn {
      -webkit-animation: fadeIn $animationTimeline;
      animation: fadeIn $animationTimeline;
    }
    &.fadeInLeft {
      -webkit-animation: fadeInLeft $animationTimeline;
      animation: fadeInLeft $animationTimeline;
    }
  }

  .ani-leave-active {
    &.fadeIn {
      -webkit-animation: fadeIn $animationTimeline reverse;
      animation: fadeIn $animationTimeline reverse;
    }
    &.fadeInLeft {
      -webkit-animation: fadeInLeft $animationTimeline reverse;
      animation: fadeInLeft $animationTimeline reverse;
    }
  }

  .search-left {
    position: relative;
    min-width: 24px;
    :deep(.animated) {
      display: flex;
      align-items: center;
    }

    :deep(.ouryun-input-group__append) {
      padding: 0;
      .ouryun-icon {
        height: 100%;
        padding: 0 12px;
        box-sizing: content-box;
        cursor: pointer;
      }
    }
    & > .ouryun-icon {
      position: absolute;
      color: #666666;
      cursor: pointer;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .content {
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  :deep(.ouryun-collapse-item__content) {
    padding: 20px 16px 8px !important;
    // max-height: 200px;
    // overflow-y: auto;
  }

  .custome-multiline-editor-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;

    .input-group {
      --width: auto;
      --grid-template-columns: 200px 200px 200px 200px;
      width: var(--width);
      display: grid;
      grid-template-columns: var(--grid-template-columns);
      column-gap: 8px;
      .validator-error {
        position: relative;
        margin-bottom: 2px;
        &::after {
          content: attr(data-error-text);
          font-size: 12px;
          line-height: 1;
          color: var(--ouryun-color-danger);
          width: 100%;
          position: absolute;
          left: 1px;
          bottom: 0px;
          transform: translateY(calc(100% + 2px));
        }
        :deep(.ouryun-input .ouryun-input__wrapper) {
          box-shadow: 0 0 0 1px var(--ouryun-color-danger) inset !important;
          .ouryun-input__inner {
            // color: var(--ouryun-color-danger);
            caret-color: #000;
          }
        }
        .ouryun-select .ouryun-select__wrapper {
          box-shadow: 0 0 0 1px var(--ouryun-color-danger) inset !important;
          .ouryun-select__selected-item:not(.is-transparent) {
            color: var(--ouryun-color-danger);
          }
        }
      }
    }

    .button-group {
      display: flex;
      flex-wrap: nowrap;
      align-items: flex-start;
      column-gap: 8px;
      .ouryun-button-custom {
        padding: 0;
      }
      .ouryun-button {
        margin: 0;
        border-style: dashed;
        width: 32px;
        height: 32px;
      }
    }
  }

  .empty-info {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -20px;
    .empty-text {
      line-height: normal;
      color: #999999;
      font-size: 14px;
      position: absolute;
      bottom: 0px;
    }
  }

  .h-300px {
    height: 300px;
    border: 1px solid red;
  }
  .overflow-auto {
    overflow: auto;
  }

  .rounded {
    border-radius: 0.375rem;
  }
  .border {
    border: 1px solid #d1d5db;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }
</style>
<style lang="sass"></style>
