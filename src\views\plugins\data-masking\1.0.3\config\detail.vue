<template>
  <div class="desensitization-pg-details">
    <BasicInfoContainer title="配置信息">
      <template #container>
        <OuBasicInfo class="tableBasicInfo" :keyLabels="keyLabels" :basicinfo="configData">
          <template #content="{ msg }">
            <ou-table
              v-if="msg.key === 'tacticsList'"
              :data="msg.value"
              :height="msg.value.length >= 5 ? 240 : msg.value.length <= 4 ? 200 : 'auto'"
              style="width: 100%; --table-empty-size: 120px">
              <ou-table-column type="index" label="优先级" width="58" align="center"></ou-table-column>
              <ou-table-column
                :prop="item.prop"
                :key="item"
                :width="item.width"
                :label="item.label"
                v-for="item in tacticsTableHead">
                <template #default="scope">
                  <span v-if="item.prop === 'status'" :style="{ color: scope.row.status ? '#62BF78' : '#FF4F45' }">
                    {{ scope.row.status ? "启用" : "禁用" }}
                  </span>
                  <ou-text-ellipsis v-else :content="scope.row[item.prop]"></ou-text-ellipsis>
                </template>
              </ou-table-column>
            </ou-table>
            <ou-table
              v-else-if="msg.key === 'whiteList'"
              :data="configData.whiteList"
              :height="configData.whiteList.length >= 5 ? 240 : configData.whiteList.length <= 4 ? 200 : 'auto'"
              style="width: 100%; --table-empty-size: 120px; margin-bottom: 20px">
              <ou-table-column type="index" label="序号" width="44" align="center"></ou-table-column>
              <ou-table-column
                :prop="item.prop"
                :key="item"
                :label="item.label"
                :width="item.width"
                v-for="item in whiteTableHead">
                <template #default="scope">
                  <span v-if="item.prop === 'status'" :style="{ color: scope.row.status ? '#62BF78' : '#FF4F45' }">
                    {{ scope.row.status ? "启用" : "禁用" }}
                  </span>
                  <ou-text-ellipsis v-else :content="scope.row[item.prop]"></ou-text-ellipsis>
                </template>
              </ou-table-column>
            </ou-table>
          </template>
        </OuBasicInfo>
        <OuBasicInfo :keyLabels="keyLabels1" :basicinfo="configData"></OuBasicInfo>
      </template>
    </BasicInfoContainer>
    <BasicInfoContainer title="已绑定对象">
      <template #container>
        <ou-table :data="objectList">
          <ou-table-column type="index" label="序号" width="44" align="center"></ou-table-column>
          <ou-table-column
            :prop="item.prop"
            :label="item.label"
            v-for="item in objectTableHead"
            :key="item"></ou-table-column>
        </ou-table>
      </template>
    </BasicInfoContainer>
  </div>
</template>
<script setup>
  import { useDetail } from "../hook/detail";
  import Tooltip from "@/components/Tooltip";
  import useTmData from "@/views/plugins/response-rewrite/v1.0.0/data";
  import BasicInfoContainer from "@/components/BasicInfoContainer";
  const { boundList, configInfo, tacticsHead, whiteHead, serviceHead } = useDetail();
  const configData = reactive({
    whiteList: [],
    tacticsList: [],
    status: "",
    compress: "",
  });
  const objectList = boundList.value;
  const tmData = ref(null);
  const sensitiveList = ref(null);
  const ruleEnum = ref(null);
  const objectTableHead = serviceHead;
  const tacticsTableHead = tacticsHead;
  const whiteTableHead = whiteHead;
  const initData = async () => {
    await getTmList();
    sensitiveList.value = tmData.value.identityRule.reduce((acc, cur) => {
      acc[cur.name] = cur;
      return acc;
    }, {});
    ruleEnum.value = tmData.value.desensitization.reduce((acc, cur) => {
      acc[cur.name] = cur;
      return acc;
    }, {});
    configList();
  };
  const configList = () => {
    const { whiteList, tacticsList, status, compress } = configInfo.value;
    configData.whiteList = whiteList.map(e => {
      return {
        ...e,
        sensitiveTypeName: mapTm(e.sensitiveTypeName),
      };
    });
    configData.tacticsList = tacticsList.map(e => {
      return {
        ...e,
        rule: ruleEnum.value[e.rule].ruleName,
        sensitiveTypeName: mapTm(e.sensitiveTypeName),
      };
    });
    configData.status = status;
    configData.compress = compress;
  };
  const mapTm = data => {
    if (data.includes("all")) return "全部";
    return data
      .split(",")
      .map(item => sensitiveList.value[item].sensitiveTypeName)
      .join("，");
  };
  const getTmList = () => {
    const { getTmData } = useTmData();
    return getTmData().then(res => {
      const dataList = res && res.length ? res : [];
      tmData.value = dataList.reduce((acc, cur) => {
        const { type, data } = cur;
        acc[type] = data.list ? data.list : [];
        return acc;
      }, {});
    });
  };
  initData();

  const keyLabels = [
    {
      label: "脱敏策略",
      key: "tacticsList",
      columnspan: 3,
    },
    {
      label: "白名单",
      key: "whiteList",
      columnspan: 3,
    },
  ];

  const keyLabels1 = [
    {
      label: "状态码",
      key: "status",
    },
    {
      label: "脱敏压缩数据",
      key: "compress",
    },
  ];
</script>

<style lang="scss" scoped>
  :deep(.ouryun-basicinfo) {
    :has(.ouryun-table) {
      .item-left {
        position: relative;
        top: 10px;
      }
    }
  }
</style>
