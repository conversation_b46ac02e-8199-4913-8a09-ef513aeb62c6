import * as xlsx from "xlsx";

export const readFile = (file, opt = {}) => {
  return new Promise(resolve => {
    let reader = new FileReader();
    if (/text+/.test(file.type)) {
      // text
      reader.readAsText(file, opt.code || "utf-8");
    } else {
      // 默认excel
      reader.readAsArrayBuffer(file.raw || file);
    }
    reader.onload = ev => {
      resolve(ev.target.result);
    };
  });
};

// 拿到上传的excel数据数组
export const doReadExcelFile = async (file, opt) => {
  let dataBinary = await readFile(file, opt);
  if (/text+/.test(file.type)) {
    // text
    return dataBinary;
  } else {
    // 默认excel
    let workBook = xlsx.read(dataBinary, { type: "binary", cellDates: true });
    let workSheet = workBook.Sheets[workBook.SheetNames[0]];
    const data = xlsx.utils.sheet_to_json(workSheet);
    console.log(data);

    // console.log(data)
    return data;
  }
};

/**
 * 下载excel
 * @param {Object} config
 */
export const exportExcelFile = config => {
  const { fileName, data, header } = config;
  if (!fileName) throw new Error("文件名不能为空");
  const ws = xlsx.utils.json_to_sheet(data || [], { header: header || [] });
  const wb = xlsx.utils.book_new();
  xlsx.utils.book_append_sheet(wb, ws, "Sheet1");
  xlsx.writeFile(wb, `${fileName}.xlsx`);
};
