import { regex as REGEX } from "@ouryun/ouryun-plus";
import { ref, computed } from "vue";
import { validateIpAll } from "@/utils/validate.js";

//配置账号&ip
export const configUserIp = row => {
  const { userWhitelist, ipWhitelist, conditionType, accountDisplay = [] } = row;

  if (conditionType === 1) {
    return userWhitelist || (accountDisplay.length > 0 ? accountDisplay.map(e => e.label).join(",") : "-");
  }

  return Array.isArray(ipWhitelist) ? ipWhitelist.join(",") : ipWhitelist || "-";
};

export function useTacticsForm() {
  class DialogForm {
    ruleName = "";
    matchRule = [];
    Match = "";
    maskingData = [
      {
        sensitiveTypeName: "",
        rule: "",
        edit: true,
        sample: "",
        result: "",
      },
    ];
  }
  const dialogForm = ref(new DialogForm());
  const validateMatch = (rule, value, callback) => {
    if (value.length > 10) {
      const message = "规则条数已超过上限10条，请进行清理后再提交";
      OuModal.warning(message);

      return callback(new Error(message));
    }
    if (value.some(item => item.status === "edit")) {
      return callback(new Error("当前正处于编辑状态"));
    } else {
      callback();
    }
  };
  const validateMaskingData = (rule, value, callback) => {
    const MAX_LENGTH = 100;
    if (value.length >= MAX_LENGTH) {
      const message = `规则条数已超过上限${MAX_LENGTH}条，请进行清理后再提交`;
      OuModal.warning(message);
      return callback(new Error(message));
    }
    if (!value.length) {
      return callback(new Error("脱敏数据不能为空"));
    }
    if (value.some(item => item.edit)) {
      return callback(new Error("当前正处于编辑状态"));
    }
    callback();
  };
  const rules = ref({
    ruleName: [
      { required: true, message: "请输入规则名称", trigger: "change" },
      {
        required: true,
        pattern: REGEX.PG_NAME,
        message: "请输入中文、英文、数字、-和_",
        trigger: "change",
      },
    ],
    matchRule: [{ required: true, trigger: [], validator: validateMatch }],
    maskingData: [{ required: true, trigger: [], validator: validateMaskingData }],
  });
  return {
    rules,
    DialogForm,
    dialogForm,
  };
}

//校验IP多行文本框方法
export const changIp = value => {
  if (!value) return { isValid: true, message: "" };
  if (validateIpAll(value)) {
    return { isValid: true, message: "" };
  } else {
    return { isValid: false, message: `IP不合法` };
  }
};

export function useWhiteListState() {
  class DialogForm {
    listName = "";
    conditionType = 0;
    account = [];
    accountDisplay = [];
    enable = true;
    ipWhitelist = [];
    sensitiveType = ["all-check"];
  }
  const conditionTypeList = {
    1: "账号",
    0: "IP",
  };
  const dialog = ref({
    isShow: false,
    title: "",
  });
  const tmData = ref(null);
  const urlTextAreaRef = ref(null);
  const whiteListRef = ref(null);
  const sensitiveTypeList = ref({});
  const labelDataList = ref({});

  const userAccountList = ref([]);

  const formType = ref("");
  const table = ref({
    list: [],
  });
  const handelIndex = ref(null);
  //输入框提示文案
  const placeholderText = computed(() => {
    const useText = `每行一条数据，单个用户名长度限制为128个字符，可输入多条数据，最多支持3000条`;
    const ipText = `每行一条数据，可输入多条数据，最多支持3000条，可输入单个IP、IP范围、IP/掩码。\n示例如下:\n单个IP：***********\nIP范围：***********-***********00或***********-100 (前三段必须相同，且结束IP需大于起始IP)\nIP/掩码: ***********/24`;
    return form.value.conditionType === 1 ? useText : ipText;
  });
  const rules = {
    listName: [{ required: true, message: "请输入名单名称", trigger: "change" }],
    ipWhitelist: [{ required: true, message: "", trigger: "change" }],
    sensitiveType: [{ required: true, message: "请选择数据标签", trigger: "change" }],
    account: [{ required: true, message: "请选择账号", trigger: "change" }],
  };
  const form = ref(new DialogForm());
  return {
    tmData,
    dialog,
    urlTextAreaRef,
    whiteListRef,
    sensitiveTypeList,
    labelDataList,
    userAccountList,
    formType,
    table,
    handelIndex,
    rules,
    form,
    placeholderText,
    conditionTypeList,
    DialogForm,
  };
}
