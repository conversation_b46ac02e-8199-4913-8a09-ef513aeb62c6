import { regex as REGEX } from "@ouryun/ouryun-plus";

const verificationRule = msg => [
  { required: true, message: msg, trigger: "change" },
  {
    required: true,
    pattern: /(^[1-9]$)|(^[1-9]\d{1,3}$)/,
    message: "请输入1-9999整数",
    trigger: "change",
  },
];
const verification = () => {
  return {
    "trigger.dateCount": verificationRule("请输入触发条件"),
    "trigger.count": verificationRule("请输入触发次数"),
    "verifyFail.dateCount": verificationRule("请输入频率"),
    "verifyFail.count": verificationRule("请输入次数"),
    "visitFrequency.dateCount": verificationRule("请输入访问频率"),
    "visitFrequency.count": verificationRule("请输入访问次数"),
    "astrictTime.dateCount": verificationRule("请输入封锁时长"),
  };
};

const formRules = {
  pgName: [
    { required: true, message: "请输入实例名称", trigger: "change" },
    {
      required: true,
      pattern: REGEX.PG_NAME,
      message: "请输入中文、英文、数字、-和_",
      trigger: "change",
    },
  ],
  ...verification(),
};

const timeMap = {
  H: "小时",
  M: "分钟",
  S: "秒",
};

const verifyMap = {
  auto: "自动开启",
  sustain: "一直开启",
};

const levelData = [
  {
    label: "宽松",
    value: "L1",
    normal: true,
    desc: "只拦截攻击特征比较明显的请求，当误报情况较多的场景下，建议选择此模式",
  },
  {
    label: "中等",
    value: "L2",
    normal: true,
    desc: "满足大多数场景下的web防护需求",
  },
  {
    label: "严格",
    value: "L3",
    normal: true,
    desc: "可以拦截具有复杂的绕过特征的攻击请求，高精度防护要求可选择此模式",
  },
];

const mapDateUnit = d => {
  const count = {
    S: d.dateCount,
    M: d.dateCount * 60,
    H: d.dateCount * 60 * 60,
  };
  return count[d.dateUnit];
};

const levelMap = {
  L1: 0,
  L2: 1,
  L3: 2,
};

//CC插件提交数据
const submitData = (form, whiteData) => {
  const { defendMode, defendLevel, astrictTime, verifyFail, trigger, verify, action, visitFrequency } = form;
  const data = {
    mode: Number(defendMode),
    auto_level: levelMap[defendLevel],
    action: {
      dryrun: action === "1" ? true : false,
      man_machine_verification: action === "2" ? true : false,
      all_run_verification: verify === "auto" ? false : true,
      rate_limit_quota: {
        duration: mapDateUnit(defendMode === "1" ? trigger : visitFrequency),
        max_count: defendMode === "1" ? trigger.count : visitFrequency.count,
      },
      check_quota: {
        duration: mapDateUnit(verifyFail),
        max_count: verifyFail.count,
      },
      block_time: mapDateUnit(astrictTime),
    },
    ip_whitelist: whiteData,
  };
  return data;
};

export const useState = () => {
  return {
    timeMap,
    levelData,
    verifyMap,
    formRules,
    mapDateUnit,
    submitData,
  };
};
