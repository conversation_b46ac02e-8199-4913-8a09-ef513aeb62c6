import { reactive, ref, computed } from "vue";
import { regex } from "@ouryun/ouryun-plus";

export default function () {
  const form = reactive({
    configRule: [],
  });
  const formRef = ref(null);

  const validateConfigRule = (rule, value, callback) => {
    if (!value.length) {
      callback(new Error("请添加配置规则"));
    } else {
      callback();
    }
  };

  const formRules = {
    configRule: [{ required: true, trigger: [], validator: validateConfigRule }],
  };

  const selectionsList = ref([]);

  const dialogShow = ref(false);

  const editType = ref("添加");

  const dialogFormRef = ref(null);
  const matchedConditionEditorRef = ref(null);

  const actOptions = [
    {
      label: "拒绝",
      value: 1,
    },
    {
      label: "仅记录",
      value: 2,
    },
  ];

  const typeOptions = [
    {
      label: "文本",
      value: 1,
    },
    {
      label: "HTML",
      value: 2,
    },
    {
      label: "JSON",
      value: 3,
    },
  ];

  const labelOptions = [
    {
      label: "数据标签",
      value: "label",
    },
    {
      label: "自定义参数",
      value: "custom",
    },
  ];

  const logicOptions = [
    {
      label: "属于",
      value: "isBelongTrue",
    },
    {
      label: "不属于",
      value: "isBelongFalse",
    },
  ];

  const symbolOptions = [
    {
      label: ">",
      value: "GT",
    },
    {
      label: "<",
      value: "LT",
    },
    {
      label: "≥",
      value: "GE",
    },
    {
      label: "≤",
      value: "LE",
    },
    {
      label: "=",
      value: "EQ",
    },
  ];

  const dataRecognitionList = ref([]);

  const validateMatch = (rule, value, callback) => {
    if (value.length > 10) {
      const message = "规则条数已超过上限10条，请进行清理后再提交";
      callback(new Error(message));
    } else {
      callback();
    }
  };

  const dialogFormRules = {
    rule_name: [{ required: true, trigger: ["change", "blur"], message: "请输入规则名称" }],
    match_data: [{ required: true, trigger: [] }],
    matchers: [{ required: false, trigger: [], validator: validateMatch }],
    type: [{ required: true, trigger: ["change", "blur"], message: "请选择类型" }],
  };

  class DialogForm {
    rule_name = "";
    match_data = [{ label: "", logic: "", matchContent: "", symbol: "", quantity: "" }];
    matchers = [];
    act = 1;
    deny_type = 1;
    deny_context = "";
    isDefault = false;
  }

  const dialogForm = reactive(new DialogForm());

  const tableRef = ref(null);

  const denyContextPreviewShow = ref(false);

  const activeMatchDataIndex = ref(0);

  const matchingDataFormRef = ref(null);

  const matchingDataFormConfig = computed(() => {
    return [
      {
        prop: "label",
        type: () => "select",
        change: item => {
          item["logic"] = null;
        },
        visible: () => true,
        span: () => 1,
        change: item => {
          ["logic"].forEach(prop => {
            item[prop] = "";
          });
        },
        validator: (item, index, callback) => {
          if (!item["label"]) callback(new Error("请选择"));
        },
        selectOptions: () => {
          labelOptions.forEach(option => {
            if (
              dialogForm.match_data.some(
                (ruleItem, ruleIndex) => ruleItem.label === option.value && ruleIndex !== activeMatchDataIndex.value,
              )
            ) {
              option.disabled = true;
            } else {
              option.disabled = false;
            }
          });

          return labelOptions;
        },
      },
      {
        prop: "logic",
        type: () => "select",
        change: item => {
          if (item["label"] === "custom") {
            item["matchContent"] = "";
          } else {
            item["matchContent"] = [];
          }
          item["symbol"] = null;
          item["quantity"] = "";
        },
        visible: item => item["label"],
        span: () => 1,
        validator: (item, index, callback) => {
          if (!item["logic"]) callback(new Error("请选择"));
        },
        selectOptions: () => logicOptions,
      },
      {
        prop: "matchContent",
        type: item => {
          if (item["label"] === "custom") {
            return "input";
          } else if (item["label"] === "label") {
            return "selectV2";
          }
        },
        visible: item => item.logic,
        span: item => {
          if (item["logic"] === "isBelongFalse") {
            return 3;
          } else {
            return 1;
          }
        },
        validator: (item, index, callback) => {
          if (item["label"] === "label") {
            if (!item["matchContent"].length) {
              callback(new Error("请选择"));
            }
          } else {
            if (!item["matchContent"]) callback(new Error("请输入"));
          }
        },
        selectOptions: item => {
          if (item["label"] === "label") {
            return dataRecognitionList.value;
          }
        },
        attrs: item => {
          if (item["label"] === "label") {
            return {
              checkAll: true,
            };
          } else if (item["label"] === "custom") {
            return {
              placeholder: "输入多个以空格隔开",
            };
          }
        },
      },
      {
        prop: "symbol",
        type: () => "select",
        visible: item => item["logic"] === "isBelongTrue",
        span: item => (item["logic"] === "isBelongFalse" ? 0 : 1),
        validator: (item, index, callback) => {
          if (!item["symbol"]) callback(new Error("请选择"));
        },
        selectOptions: () => symbolOptions,
      },
      {
        prop: "quantity",
        type: () => "input",
        unit: () => "个",
        visible: item => item["logic"] === "isBelongTrue",
        span: item => (item["logic"] === "isBelongFalse" ? 0 : 1),
        validator: (item, index, callback) => {
          if (!item["quantity"]) {
            callback(new Error("请输入"));
          } else if (!regex.NU_ZERO.test(item["quantity"])) {
            callback(new Error("请输入正整数"));
          }
        },
      },
    ];
  });

  const matchDataCollapse = ref(true);
  const matchConditionCollapse = ref(true);

  return {
    form,
    formRef,
    formRules,
    selectionsList,
    dialogShow,
    editType,
    dialogFormRef,
    matchedConditionEditorRef,
    actOptions,
    typeOptions,
    dataRecognitionList,
    dialogFormRules,
    DialogForm,
    dialogForm,
    tableRef,
    denyContextPreviewShow,
    activeMatchDataIndex,
    matchingDataFormRef,
    matchingDataFormConfig,

    labelOptions,
    logicOptions,
    symbolOptions,
    matchDataCollapse,
    matchConditionCollapse,
  };
}
