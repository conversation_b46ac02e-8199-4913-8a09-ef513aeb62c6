import request from "baseRequest";

// 插件列表选项
export const getUsedPlugins = data => {
  return request({
    url: "/plugiverse/v1/engine/plugins",
    method: "get",
  });
};

// 配置详情
export const getPluginConfig = data => {
  return request({
    url: "/plugiverse/v1/engine/pluginConfig",
    method: "get",
    params: data,
  });
};

// 保存插件配置实例- new
export const saveLayer4PluginInstance = data => {
  return request({
    url: "/plugiverse/v1/engine/layer4/pluginInstance",
    method: "put",
    data,
  });
};

//  配置保存
export const savePluginConfig = (data, method) => {
  return request({
    url: "/plugiverse/v1/engine/pluginConfig",
    method,
    data,
  });
};

// 路由配置保存
export const saveRouteConfig = data => {
  return request({
    url: "/plugiverse/v1/vs/saveRouteConfigs",
    method: "post",
    data,
  });
};

//获取镜像信息
export const getMirrorInfo = () => {
  return request({
    url: "/v1/pluginInstance/ntlayer/netdevs/ENGINEID",
    method: "get",
  });
};

//提交流量镜像配置
export const saveTrafficMirrorConfig = data => {
  return request({
    url: "/v1/pluginInstance/ntlayer/traffic-mirror/ENGINEID",
    method: "post",
    data,
  });
};

// // 认证鉴权插件下载导入模板
// export const authDownloadTemplate = () => {
//   return request({
//     url: "/plugiverse/v1/sc/plugin/iv/download_tpl",
//     method: "get",
//     noTransmit: true,
//   });
// };
// 认证鉴权插件读取导入文件
export const authUploadFile = data => {
  return request({
    url: "/plugiverse/v1/sc/plugin/iv/rule_list/parse",
    method: "post",
    noTransmit: true,
    upload: true,
    data,
  });
};

// acl 读取导入文件
export const aclUploadFile = data => {
  return request({
    url: "/plugiverse/v1/sc/plugin/acl/rule_list/parse",
    method: "post",
    noTransmit: true,
    upload: true,
    data,
  });
};
