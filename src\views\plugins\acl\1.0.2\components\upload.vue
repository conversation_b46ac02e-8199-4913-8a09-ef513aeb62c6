<template>
  <ou-opera-dialog
    title="导入"
    @close="close"
    v-model="isShowUploadDialog"
    :z-index="996"
    :close-on-click-modal="false"
    draggable
    hideFooter
    :width="step === 'importing' ? '550px' : '528px'">
    <template #content>
      <ou-form
        v-if="step === 'importing'"
        :model="fileForm"
        labelPosition="left"
        ref="whiteItemFormRef"
        :rules="whiteItemRules"
        :label-width="labelWidth"
        class="-mb-20">
        <ou-form-item prop="file" class="pn_required_label" label="">
          <span class="content-tip">请先下载导入模板，并严格按模板要求填写信息后导入，否则信息可能导入失败；</span>
          <div class="export-tips upload-text">
            <div class="export-btn">
              <div style="cursor: pointer" @click="downTemplate">点击下载模板</div>
            </div>
          </div>
          <ou-drag-upload
            ref="uploadRef"
            @getData="getFileData"
            @on-change="fileChange"
            fileType=".xls,.xlsx"
            :limit="1"
            tip="当前只支持后缀为.xls，.xlsx的文件"></ou-drag-upload>
        </ou-form-item>
      </ou-form>
      <div class="import_result" v-else>
        <div v-if="result.state === 1">
          <ou-icon class="icon" color="#62BF78"><SuccessFilled /></ou-icon>
          <div class="tip">导入成功</div>
          <div class="desc">{{ result.msg }}</div>
        </div>
        <div v-else-if="result.state === 2">
          <ou-icon class="icon" color="#ffc53d"><WarningFilled /></ou-icon>
          <div class="tip">部分导入失败</div>
          <div class="desc">{{ result.msg }}</div>
        </div>
        <div v-else>
          <ou-icon class="icon" color="#ff0b0b"><CircleCloseFilled /></ou-icon>
          <div class="tip">导入失败</div>
          <div class="desc">{{ result.msg }}</div>
        </div>
      </div>
    </template>
    <template #footer>
      <div v-if="step === 'importing'">
        <ou-button @click="isShowUploadDialog = false">取消</ou-button>
        <ou-button type="primary" @click="uploadAclInfo" style="margin-left: 10px">确定</ou-button>
      </div>
      <div v-else>
        <ou-button v-if="result.state === 3" @click="isShowUploadDialog = false">结束</ou-button>
        <ou-button v-else @click="isShowUploadDialog = false">完成</ou-button>
        <ou-button type="primary" v-if="result.state === 2" @click="downFail">下载失败数据</ou-button>
        <ou-button type="primary" v-else @click="step = 'importing'">继续导入</ou-button>
      </div>
    </template>
  </ou-opera-dialog>
</template>

<script setup>
  import { ref, computed, defineProps, defineEmits } from "vue";
  import { exportExcelFile } from "@/utils/readfile.js";
  import { aclUploadFile } from "@/api/plugin.js";
  import { OuModal, dayjs } from "@ouryun/ouryun-plus";
  import {
    ExcelHeader,
    getNameById_proto,
    getNameById_action,
    getIdByName_proto,
    getIdByName_action,
  } from "../config/utils";

  const step = ref("importing");
  const result = ref({});
  const emit = defineEmits(["update:modelValue", "comfirm", "cancel"]);
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  // watch(
  //   () => props.modelValue,
  //   () => {},
  //   { deep: true, immediate: true },
  // );

  const isShowUploadDialog = computed({
    get: () => {
      if (props.visible) {
        step.value = "importing";
      }
      return props.visible;
    },
    set: val => {
      emit("close");
    },
  });

  let FailItems = [],
    SuccessItems = [];
  const fileForm = ref({ file: null });

  const returnResult = (sussess, fail) => {
    SuccessItems = sussess?.map(e => {
      const row = {
        name: e[0],
        proto: getIdByName_proto(e[1]),
        saddr: e[2],
        sport: e[3],
        daddr: e[4],
        dport: e[5],
        action: getIdByName_action(e[6]),
        remark: e[7],
      };
      return {
        ...row,
      };
    });
    FailItems = fail || [];
  };

  const renderImportResult = data => {
    const { state, success_total = 0, success_items, fail_total = 0, fail_items, fail_reason } = data;
    if (state === 1) {
      // 1：全部成功 2：部分成功 3：全部失败
      result.value = {
        state,
        msg: `全部导入成功，共计${success_total}条`,
      };
      returnResult(success_items);
      SuccessItems?.length && emit("comfirm", SuccessItems, "新增");
    } else if (state === 2) {
      result.value = {
        state,
        msg: `导入成功${success_total}条，导入失败${fail_total}条，点击下载查看失败数据`,
      };
      returnResult(success_items, fail_items);
      SuccessItems?.length && emit("comfirm", SuccessItems, "新增");
    } else {
      result.value = {
        state,
        msg: `失败原因：“${fail_reason}”`,
      };
    }
  };

  const uploadRef = ref(null);
  const uploadAclInfo = async files => {
    const file = uploadRef.value?.getFileData();
    const data =
      props.modelValue?.map(e => [
        e.name,
        getNameById_proto(e.proto),
        e.saddr,
        e.sport,
        e.daddr,
        e.dport,
        getNameById_action(e.action),
        e.remark,
      ]) || [];
    const fd = new FormData();
    fd.append("file", file[0]);
    fd.append("rules", JSON.stringify(data));
    await aclUploadFile(fd).then(res => {
      const { data, message, code } = res;
      if (code === 200) {
        renderImportResult(data);
      } else {
        OuModal.success("上传失败");
      }
      step.value = "import_result";
    });
  };

  const downFail = () => {
    let FailHeader = [...ExcelHeader, "失败原因"];
    const data = FailItems.map(e => {
      let temp = {};
      FailHeader?.forEach((header, index) => {
        temp[header] = e[index];
      });
      return temp;
    });
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const config = {
      header: FailHeader,
      fileName: `导入失败ACL_${formatTime}`,
      data,
    };
    exportExcelFile(config);
  };

  const downTemplate = () => {
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const config = {
      header: ExcelHeader,
      fileName: `ACL导入模板${formatTime}`,
      data: [
        ["ACL名称1", "全部", "*************", "1-65535", "*********-100", "1-65535", "拒绝", "拒绝该IP的所有流量"],
        // ["123132_CN", "Ouryun_10.zq", "长期有效", "测试账号"],
      ].map(e => {
        let temp = {};
        ExcelHeader?.forEach((header, index) => {
          temp[header] = e[index];
        });
        return temp;
      }),
    };
    exportExcelFile(config);
  };
</script>
<style scoped lang="scss">
  .import_result {
    text-align: center;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    margin-top: 8px;
    .icon {
      font-size: 50px;
    }

    .tip {
      margin: 20px 0 12px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
    }

    .desc {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 21px;
    }
  }

  .form-content-width {
    width: 300px !important;
  }

  .content-tip {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 22px;
  }

  .export-tips {
    font-weight: 400;
    font-size: 14px;
    color: var(--ouryun-color-brand-base);
    line-height: 21px;
    display: flex;
    justify-content: space-between;
  }

  .upload-text {
    margin-top: 4px;
  }
</style>
