<template>
  <div class="plugin-matched-condition-editor">
    <!-- 组件使用说明
    https://doc.weixin.qq.com/doc/w3_AUoAQwbsAMICN3DmzTiX4ThGXYPjJ?scode=ABwA9Qd2ABEbLvjixrAUoAQwbsAMI -->
    <ou-cascade-editor
      ref="CascadeEditorRef"
      v-model="rules"
      :form="props.showAny ? anyMatchForm : formConfig"
      :beforeAdd="beforeAdd"
      :beforeDel="beforeDel"
      :addBtnDisabled="addBtnDisabled"
      @cellFocus="index => (activeIndex = index)"></ou-cascade-editor>
  </div>
</template>

<script setup name="NewMatchedConditionEditor">
  import {
    fieldOptionsAll,
    logicOptionsAll,
    methodOptions,
    locationTreeList,
    queryParameterRegularList,
  } from "@/utils/MatchedCondition.js";
  import { validateIpAll } from "@/utils/validate.js";
  import { computed, watch, defineEmits, nextTick } from "vue";
  import { OuCascadeEditor, regex, OuModal } from "@ouryun/ouryun-plus";
  import hookStates from "./hook/index.js";
  import usePlugin from "basePluginStore";
  const store = usePlugin();

  const { anyMatchForm, CascadeEditorRef, rules, activeIndex } = hookStates();

  const props = defineProps({
    modelValue: {
      type: Array,
      required: true,
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    showAny: {
      type: Boolean,
      default: false,
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false,
    },
    // 匹配字段选项, 默认选项["源IP","路径","请求头部","请求方法","查询参数","账号"]
    fieldOptions: {
      type: Array,
      default: () => [
        "srcIp",
        "path",
        "requestHeader",
        "requestMethod",
        "queryParameter",
        "userName",
        // "sourceLocation",
        // "region",
        // "time",
      ],
    },
    // 最大添加行数
    maxRows: {
      type: Number,
      default: 10,
    },
  });

  const fieldOptions = computed(() => {
    return props.fieldOptions
      .map(item => {
        return fieldOptionsAll.value.find(item2 => item2.value === item);
      })
      .filter(item => item);
  });

  const emits = defineEmits(["update:modelValue"]);

  const onCreated = () => {
    if (props.showAny) {
      rules.value = [
        {
          field: "任意",
          logic: "任意",
          params: "任意",
          content: "任意",
        },
      ];
    } else {
      rules.value = props.modelValue.map(item => {
        return {
          field: item.field,
          logic: item.logic,
          params: item.params,
          content: item.content,
        };
      });
    }
  };

  const emitChange = () => {
    if (!props.showAny) {
      nextTick(() => {
        let modelValue = [];
        if (rules.value.length > 1 || rules.value[0].field) {
          modelValue = rules.value.map(item => {
            return {
              field: item.field,
              logic: item.logic,
              params: item.params,
              content: item.content,
            };
          });
        }
        emits("update:modelValue", modelValue);
      });
    }
  };

  watch(
    () => rules.value,
    () => {
      emitChange();
    },
    { deep: true, immediate: true },
  );

  const formConfig = [
    {
      prop: "field",
      type: () => "select",
      visible: () => true,
      span: () => 1,
      change: (item, index, expose) => {
        item["params"] = "";
        switch (item.field) {
          case "srcIp":
            item["logic"] = "containsTrue";
            item["content"] = "";
            break;
          case "path":
            item["logic"] = "prefix";
            item["content"] = "";
            break;
          case "requestHeader":
            item["logic"] = "containsTrue";
            item["content"] = "";
            break;
          case "requestMethod":
            item["logic"] = "exactTrue";
            item["content"] = [];
            break;
          case "queryParameter":
            item["logic"] = "containsTrue";
            item["content"] = "";
            break;
          case "userName":
            item["logic"] = "exactTrue";
            item["content"] = [];
            break;
          case "sourceLocation":
            item["logic"] = "containsTrue";
            item["content"] = "";
            break;
          case "region":
            item["logic"] = "containsTrue";
            item["content"] = [];
            break;
          case "time":
            item["logic"] = "containsTrue";
            item["content"] = null;
            break;
          default:
            item["field"] = "";
            item["logic"] = "";
            item["content"] = "";
        }
        expose.clearValidate(index);
      },
      validator: (item, index, callback) => {
        if (props.required) {
          if (!item["field"]) return callback(new Error("请选择匹配字段"));
        } else {
          if (!item["field"] && rules.value.length > 1) return callback(new Error("请选择匹配字段"));
        }
      },
      selectOptions: () => {
        fieldOptions.value.forEach(option => {
          if (
            rules.value.some(
              (ruleItem, ruleIndex) => ruleItem.field === option.value && ruleIndex !== activeIndex.value,
            ) &&
            !["requestHeader", "queryParameter"].includes(option.value)
          ) {
            option.disabled = true;
          } else {
            option.disabled = false;
          }
        });

        return fieldOptions.value;
      },
      attrs: () => {
        return {
          placeholder: "请选择匹配字段",
        };
      },
    },
    {
      prop: "params",
      type: () => "input",
      visible: item => ["requestHeader", "queryParameter"].includes(item["field"]),
      span: item => (["requestHeader", "queryParameter"].includes(item["field"]) ? 1 : 0),
      validator: (item, index, callback) => {
        // const formatCheck = regex.NO_HZ;
        const formatCheck = /^[a-zA-Z0-9!\"#$%&'()*+,\-./:;<=>?@\[\\\]^_`{|}~]+$/;

        if (item["field"] === "requestHeader") {
          if (!item.params) callback(new Error("请输入头部名称"));
          if (item.params.length > 128) callback(new Error("长度限制为128个字符"));
          if (!formatCheck.test(item.params)) callback(new Error("请输入英文、数字、英文标点符号"));
        } else if (item["field"] === "queryParameter") {
          if (!item.params) callback(new Error("请输入参数名称"));
          if (item.params.length > 128) callback(new Error("长度限制为128个字符"));
          if (!formatCheck.test(item.params)) callback(new Error("请输入英文、数字、英文标点符号"));
        }
      },
      attrs: item => {
        if (!["requestHeader", "queryParameter"].includes(item["field"])) {
          return {
            disabled: true,
          };
        }
      },
    },
    {
      prop: "logic",
      type: () => "select",
      change: (item, index, expose) => {
        if (item["field"] === "srcIp") {
          if (["containsTrue", "containsFalse"].includes(item["logic"])) {
            item["content"] = "";
          } else if (["belongToIpGroup", "notBelongToIpGroup"].includes(item["logic"])) {
            item["content"] = [];
          }
        } else if (item["field"] === "time") {
          if (["containsTrue", "containsFalse"].includes(item["logic"])) {
            item["content"] = null;
          } else if (["belongToTimeRange", "notBelongToTimeRange"].includes(item["logic"])) {
            item["content"] = "";
          }
        } else if (["requestHeader", "queryParameter"].includes(item["field"])) {
          item["content"] = "";
        }
        expose.clearValidate(index, "content");
      },
      span: () => 1,
      visible: item => item["field"],
      validator: (item, index, callback) => {
        if (!item["logic"]) callback(new Error("请选择逻辑"));
      },
      selectOptions: item => {
        let filterTag = [];

        switch (item.field) {
          case "srcIp":
            filterTag = ["containsTrue", "containsFalse", "belongToIpGroup", "notBelongToIpGroup"];
            break;
          case "path":
            filterTag = ["exactTrue", "containsTrue", "exactFalse", "containsFalse", "prefix", "regex"];
            break;
          case "requestHeader":
            filterTag = [
              "exactTrue",
              "containsTrue",
              "exactFalse",
              "containsFalse",
              "null",
              "presentMatchTrue",
              "presentMatchFalse",
              "regex",
            ];
            break;
          case "requestMethod":
            filterTag = ["exactTrue", "exactFalse"];
            break;
          case "queryParameter":
            filterTag = [
              "exactTrue",
              "containsTrue",
              "exactFalse",
              "containsFalse",
              "null",
              "presentMatchTrue",
              "presentMatchFalse",
              "regex",
            ];
            break;
          case "userName":
            filterTag = ["exactTrue", "exactFalse"];
            break;
          case "sourceLocation":
            filterTag = ["exactTrue", "exactFalse", "containsTrue", "containsFalse"];
            break;
          case "region":
            filterTag = ["containsTrue", "containsFalse"];
            break;
          case "time":
            filterTag = ["containsTrue", "containsFalse", "belongToTimeRange", "notBelongToTimeRange"];
            break;
        }

        return logicOptionsAll.filter(item => filterTag.includes(item.value));
      },
      attrs: () => {
        return {
          placeholder: "请选择逻辑",
        };
      },
    },
    {
      prop: "content",
      type: item => {
        if (
          (item["field"] === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(item["logic"])) ||
          (item["field"] === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(item["logic"])) ||
          item["field"] === "requestMethod"
        ) {
          return "selectV2";
        } else if (item["field"] === "userName") {
          return "selectV2";
        } else if (item["field"] === "queryParameter") {
          if (item["logic"] === "regex") {
            return "selectV2";
          } else {
            return "input";
          }
        } else if (item["field"] === "region") {
          return "treeSelect";
        } else if (item["field"] === "time" && ["containsTrue", "containsFalse"].includes(item["logic"])) {
          return "datePicker";
        } else {
          return "input";
        }
      },
      span: item => {
        if (item["field"] === "time" && ["containsTrue", "containsFalse"].includes(item["logic"])) {
          return 2;
        } else {
          return 1;
        }
      },
      visible: item => {
        return (
          item["field"] &&
          !(
            ["requestHeader", "queryParameter"].includes(item["field"]) &&
            ["null", "presentMatchTrue", "presentMatchFalse"].includes(item["logic"])
          )
        );
      },
      validator: (item, index, callback) => {
        // const formatCheck = regex.NO_HZ;
        const formatCheck = /^[a-zA-Z0-9!\"#$%&'()*+,\-./:;<=>?@\[\\\]^_`{|}~]+$/;
        const MAX_LENGTH = 10;

        if (item["field"] === "srcIp") {
          if (["containsTrue", "containsFalse"].includes(item.logic)) {
            if (!item.content) {
              callback(new Error("请输入单个IP、子网掩码或范围，多个以逗号分隔"));
            } else {
              const ipList = item.content.split(",");
              const newArr = ipList.filter(ip => ip.trim() !== "");
              if (newArr.length > MAX_LENGTH) {
                callback(new Error(`IP不能超过${MAX_LENGTH}条`));
              } else {
                for (let index = 0; index < newArr.length; index++) {
                  const e = newArr[index];
                  if (!validateIpAll(e)) {
                    callback(new Error(`第${index + 1}条IP不合法：${e}`));
                  }
                }
              }
            }
          } else if (["belongToIpGroup", "notBelongToIpGroup"].includes(item.logic)) {
            if (!item.content || !item.content.length) {
              callback(new Error("请选择IP组"));
            }
          }
        } else if (item["field"] === "path") {
          if (item.content.length > 512) callback(new Error("长度限制为512个字符"));
          if (!formatCheck.test(item.content)) callback(new Error("请输入英文、数字、英文标点符号"));
        } else if (item["field"] === "requestHeader") {
          if (item.content.length > 512) callback(new Error("长度限制为512个字符"));
          if (!formatCheck.test(item.content)) callback(new Error("请输入英文、数字、英文标点符号"));
        } else if (item["field"] === "requestMethod") {
          if (!item.content.length) callback(new Error("请选择请求方法"));
        } else if (item["field"] === "queryParameter") {
          if (item["logic"] === "regex") {
            if (!item.content) {
              callback(new Error("请选择或输入"));
            } else if (!queryParameterRegularList.some(regular => regular.value === item.content)) {
              // 用户添加的自定义选项
              if (item.content.length > 512) callback(new Error("长度限制为512个字符"));
              if (!formatCheck.test(item.content)) callback(new Error("请输入英文、数字、英文标点符号"));
            }
          } else {
            if (item.content.length > 512) callback(new Error("长度限制为512个字符"));
            if (!formatCheck.test(item.content)) callback(new Error("请输入英文、数字、英文标点符号"));
          }
        } else if (item["field"] === "userName") {
          if (!item.content.length) return callback(new Error("请选择或输入"));
          if (item.content.length > 10) return callback(new Error("账号不能超过10个"));

          const valueSet = new Set(store.accountList.map(item => item.value));
          const customContent = item.content.filter(valueItem => !valueSet.has(valueItem)); // 用户添加的自定义账号选项

          let errorText = "";

          if (customContent.length) {
            customContent.forEach(item => {
              if (item.length > 128) {
                errorText = "单个长度限制为128个字符";
              } else if (item && !item.trim()) {
                errorText = "单个账号不能只为空格";
              }
            });
          }
          errorText && callback(new Error(errorText));
        } else if (item["field"] === "sourceLocation") {
          if (!formatCheck.test(item.content)) callback(new Error("请输入英文、数字、英文标点符号"));
          if (!item.content.length) callback(new Error("请输入域名或地址"));
        } else if (item["field"] === "region") {
          if (!item.content.length) callback(new Error("请选择IP归属地区"));
        } else if (item["field"] === "time") {
          if (["containsTrue", "containsFalse"].includes(item["logic"]) && !item.content?.length) {
            callback(new Error("请选择时间范围"));
          } else if (["belongToTimeRange", "notBelongToTimeRange"].includes(item["logic"]) && !item.content) {
            callback(new Error("请选择时间段"));
          }
        }
      },
      selectOptions: item => {
        if (item.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(item.logic)) {
          return store.ipGroupList;
        } else if (item.field === "requestMethod") {
          return methodOptions;
        } else if (item.field === "queryParameter" && item["logic"] === "regex") {
          return queryParameterRegularList;
        } else if (item.field === "userName") {
          return store.accountList;
        } else if (item.field === "region") {
          return locationTreeList;
        } else if (item.field === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(item["logic"])) {
          return store.timePeriodsList;
        }
      },
      attrs: item => {
        if (
          (item["field"] === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(item["logic"])) ||
          item["field"] === "requestMethod"
        ) {
          return {
            checkAll: true,
          };
        } else if (item.field === "queryParameter") {
          if (item["logic"] === "regex") {
            return {
              // multiple: true,
              filterable: true,
              allowCreate: true,
              placeholder: "请选择或输入",
            };
          } else {
            return {
              placeholder: "请输入",
            };
          }
        } else if (item.field === "userName") {
          return {
            multiple: true,
            filterable: true,
            allowCreate: true,
            placeholder: "请选择或输入",
          };
        } else if (item.field === "requestMethod") {
          return {
            multiple: true,
          };
        } else if (item.field === "region") {
          return {
            multiple: true,
            filterable: true,
            "node-key": "value",
            "render-after-expand": false,
            "show-checkbox": true,
          };
        } else if (item["field"] === "time" && ["containsTrue", "containsFalse"].includes(item["logic"])) {
          return {
            type: "datetimerange",
            format: "YYYY-MM-DD HH:mm",
            "value-format": "YYYY-MM-DD-HH:mm",
            "range-separator": "-",
            "default-time": "[new Date(0, 0, 0, 0, 0, 0), new Date(0, 0, 0, 23, 59, 59)]",
          };
        }
      },
    },
    {
      // 没有值也不显示，只是为了占位
      prop: "holder",
      type: () => "null",
      span: item => {
        if (
          ["requestHeader", "queryParameter"].includes(item["field"]) ||
          (item["field"] === "time" && ["containsTrue", "containsFalse"].includes(item["logic"]))
        ) {
          // 不需要占位
          return 0;
        } else {
          // 需要占位
          return 1;
        }
      },
      visible: () => false,
    },
  ];

  // 添加一行前置事件
  const beforeAdd = (callback, index) => {
    if (!props.required && index === 0 && !rules.value[index]["field"]) {
      return callback(new Error("请先选择匹配条件匹配字段"));
    }
    const valid = CascadeEditorRef.value.validator();
    if (!valid) {
      return callback(new Error());
    } else {
      callback();
    }
  };

  // 删除一行前置事件
  const beforeDel = (callback, index) => {
    callback();
    if (!props.required && rules.value.length === 1 && !rules.value[0].field) {
      CascadeEditorRef.value.clearValidate();
    }
  };

  const addBtnDisabled = () => {
    if (props.showAny) {
      return true;
    } else {
      return rules.value.length >= props.maxRows;
    }
  };

  const validator = () => {
    return CascadeEditorRef.value.validator();
  };

  defineExpose({
    validator,
  });

  onCreated();
</script>
