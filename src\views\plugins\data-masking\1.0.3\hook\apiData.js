import { getLabelList, getMaskingRule, getSampleAll } from "@/api/pgModule.js";

export const getIdentityRule = () => {
  return new Promise((resolve, reject) => {
    getLabelList()
      .then(res => {
        resolve({
          type: "identityRule",
          data: {
            list: res.data.data_label_rules || [],
          },
        });
      })
      .catch(err => {
        reject(err);
      });
  });
};
export const getDesensitization = () => {
  const params = {
    type: "list",
    tab: "maskingRule",
    method: "get",
  };
  return new Promise((resolve, reject) => {
    getMaskingRule()
      .then(res => {
        resolve({ type: "desensitization", data: res.data || [] });
      })
      .catch(err => {
        reject(err);
      });
  });
};
export const getSamples = () => {
  return new Promise((resolve, reject) => {
    getSampleAll()
      .then(res => {
        resolve({ type: "samples", data: res.data || [] });
      })
      .catch(err => {
        reject(err);
      });
  });
};

export const getTmData = () => {
  return Promise.all([getIdentityRule(), getDesensitization(), getSamples()]);
};
