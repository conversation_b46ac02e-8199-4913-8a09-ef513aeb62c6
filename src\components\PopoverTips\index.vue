<template>
  <div>
    <ou-tooltip-icon style="font-size: 16px; position: relative; top: -1px">
      <template #content>
        <div class="plugin-tips-group">
          <div v-for="(item, index) in props.data" :key="index" class="plugin-tips-group-item">
            <span class="label">{{ item.label }}</span>
            <span class="value">{{ item.tips }}</span>
          </div>
        </div>
      </template>
    </ou-tooltip-icon>
  </div>
</template>

<script setup name="popoverTips">
  const props = defineProps({
    data: {
      required: true,
      type: [Array, String],
    },
  });
</script>

<style lang="scss" scoped>
  // 用于popover内的列表提示
  .plugin-tips-group {
    display: flex;
    flex-direction: column;
    line-height: 1.5;
    row-gap: 8px;
    .plugin-tips-group-item {
      .label,
      .value {
        display: inline;
      }
      .label::after {
        content: "：";
      }
    }
  }
</style>
