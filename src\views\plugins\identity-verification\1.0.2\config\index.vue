<template>
  <div class="plugin-authentication-config add_conent_flex1">
    <ou-form labelPosition="right" class="-mb-20" ref="formRef" :model="baseForm" label-width="140px">
      <div class="config-rule-container">
        <ou-form-item label="配置规则：">
          <div class="btn-row">
            <ou-button type="primary" text @click="showDialog('添加')">添加</ou-button>
            <ou-button type="primary" text :disabled="isCheckData" @click="batchRemove">批量删除</ou-button>
            <moveGroup
              v-model:list="baseForm.configRules"
              style="margin-left: 8px"
              :selections="selectionsList"
              @moved="list => routeMoved('route', list)"
              :disabled="isCheckData"></moveGroup>
          </div>
          <ou-table
            ref="tablesRef"
            class="editor"
            :data="baseForm.configRules"
            :height="!baseForm.configRules.length ? 200 : baseForm.configRules.length >= 5 ? 240 : 'auto'"
            style="width: 100%; --table-empty-size: 120px"
            @selection-change="handleSelectionChange">
            <ou-table-column type="selection" width="32" align="center" />
            <ou-table-column type="index" label="优先级" width="58" align="center" />
            <ou-table-column prop="rule_name" label="规则名称">
              <template #default="scope">
                <ou-text-ellipsis :content="String(scope.row.rule_name)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="matchingList" label="匹配条件">
              <template #default="scope">
                <ou-text-ellipsis :content="getTextInfo(scope.row.matchingList)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="account_transfer" label="账号传递" width="100">
              <template #default="{ row }">
                {{ accountTransferOptions.find(item => item.value === row.account_transfer)?.label }}
              </template>
            </ou-table-column>
            <ou-table-column prop="enable" label="状态" width="106">
              <template #default="scope">
                <div class="switch-group">
                  <ou-switch
                    size="small"
                    v-model="scope.row.enable"
                    active-text="启用"
                    inactive-text="禁用"
                    class="switch-offside"></ou-switch>
                </div>
              </template>
            </ou-table-column>
            <ou-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <ou-button type="primary" text @click="showDialog('编辑', scope.row, scope.$index)">编辑</ou-button>
                <ou-button type="primary" text @click="showDialog('复制', scope.row, scope.$index)">复制</ou-button>
                <ou-button type="primary" text @click="top(scope.row, scope.$index)">置顶</ou-button>
                <ou-button type="primary" text @click="remove(scope.row, scope.$index)">删除</ou-button>
              </template>
            </ou-table-column>
          </ou-table>
        </ou-form-item>
      </div>
    </ou-form>
    <config-rules
      v-if="isShowDialog"
      :visible="isShowDialog"
      :type="submitType"
      :model-value="rowForm"
      @comfirm="submitDialog"
      @cancel="cancel"></config-rules>
  </div>
</template>
<script setup>
  import { defineProps, defineExpose, onMounted } from "vue";
  import { getTextInfo, getMatchParameter } from "@/utils/MatchedCondition.js";
  import configRules from "../components/configRules";
  import useHooks from "../hook/index.js";
  import moveGroup from "@/components/MoveGroup/index.vue";
  import { accountTransferOptions, getUserInfoParameter } from "../hook/utils.js";
  import { rand } from "../hook/utils.js";

  import usePlugin from "basePluginStore";
  const store = usePlugin();

  const {
    baseForm,
    rowForm,
    isCheckData,
    submitType,
    selectionsList,
    tablesRef,
    isShowDialog,
    showDialog,
    routeMoved,
    handleSelectionChange,
    top,
    remove,
    batchRemove,
    cancel,
    submitDialog,
  } = useHooks();

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    caseLevel: {
      type: String,
      default: "",
    },
  });

  const initForm = async () => {
    if (props.content) {
      const content = JSON.parse(props.content);
      baseForm.configRules = content.plugin_config.rule_config.map((item, index) => {
        return {
          rule_name: item.rule_name,
          matchingList: content.meta_data[index].matchingList,
          authInfo: content.meta_data[index].authInfo,
          token_exp: item.token_exp,
          account_transfer: item.account_transfer,
          is_signal_IP: item.is_signal_IP,
          has_verify_page: item.has_verify_page,
          enable: item.enable,
          rule_id: item.rule_id,
        };
      });
    }
  };

  const submit = async () => {
    const params = {
      plugin_config: {
        rule_config: [], // c++ 需要的结构
      },
      meta_data: [], // 页面显示的数据
    };
    if (props.caseLevel === "strict" && baseForm.configRules.length === 0) {
      return Promise.reject("严格防护方案下，必须要手动输入认证信息");
    }

    baseForm.configRules.forEach((item, index) => {
      params.plugin_config.rule_config.push({
        rule_name: item.rule_name,
        match_info: getMatchParameter(item.matchingList).match,
        user_info: getUserInfoParameter(item.authInfo),
        token_exp: item.token_exp,
        account_transfer: item.account_transfer,
        is_signal_IP: item.is_signal_IP,
        has_verify_page: item.has_verify_page,
        enable: item.enable,
        rule_id: item.rule_id || rand(16),
      });
      params.meta_data.push({
        matchingList: item.matchingList,
        authInfo: item.authInfo,
      });
    });

    const extension = { refs: { ip_address_database: [] } };
    const ipAddress = [];
    params.meta_data.forEach(item => {
      item.matchingList.forEach(matchItem => {
        if (matchItem.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic)) {
          ipAddress.push(...matchItem.content);
        }
      });
    });

    extension.refs.ip_address_database = [...new Set(ipAddress)].map(item => {
      const ipGroup = store.ipGroupList.find(ipGroup => ipGroup.value === item);
      return {
        item_id: ipGroup.value,
        ...ipGroup,
      };
    });

    return { contentParams: params, extension };
  };

  onMounted(() => {
    initForm();
  });

  defineExpose({
    submit,
  });
</script>

<style scoped lang="scss">
  .plugin-authentication-config {
    .config-rule-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      .btn-row {
        height: 32px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
      }
    }
  }
</style>
