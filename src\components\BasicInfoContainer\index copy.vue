<template>
  <div class="ouryun-basic">
    <div class="ouryun-basictitle-container">
      <div class="detail-basic-title">
        <slot name="title">
          {{ title }}
        </slot>
      </div>
      <div class="basic-right-content"><slot name="titleRight"></slot></div>
    </div>
    <div class="ouryun-basicinfo-container">
      <div class="ouryun-basicinfo-left"><slot name="container"></slot></div>
      <!--  <div class="ouryun-basicinfo-right">
        <slot name="infoRight"></slot>
      </div> -->
    </div>
  </div>
</template>

<script setup name="BasicInfoTitle">
  const props = defineProps({
    // 标题
    title: {
      type: String,
      default: "基础信息",
    },
    bgColor: {
      type: String,
      default: "#fff",
    },
  });
</script>

<style lang="scss" scoped>
  .ouryun-basic {
    box-shadow: var(--ouryun-box-shadow-blue-base);
    border-radius: 8px;
    background-color: #fff;
    overflow: auto;
  }
  .ouryun-basictitle-container {
    width: 100%;
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333333;
    box-shadow: inset 0px -1px 0px 0px #e7e7e7;
    margin-bottom: 12px;

    .detail-basic-title {
      height: 26px;
      line-height: 26px;
      font-size: 16px;
      font-weight: 600;
      padding-left: 16px;
    }
    .basic-right-content {
      padding-right: 20px;
      font-size: 14px;
    }
  }
  .ouryun-basicinfo-container {
    .ouryun-basicinfo-left {
      width: 100%;
    }
    padding: 16px;
    // display: flex;
    // justify-content: space-between;
  }
</style>
