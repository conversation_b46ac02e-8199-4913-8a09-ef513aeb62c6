import { ref, computed } from "vue";
import { validateIpAll } from "@/utils/validate.js";
import {
  fieldOptionsAll,
  logicOptionsAll,
  methodOptions,
  locationTreeList,
  findNodeLabel,
} from "@/utils/MatchedCondition.js";
import usePlugin from "basePluginStore";
const store = usePlugin();

export default function () {
  const anyMatch = [
    {
      field: "任意",
      params: "任意",
      logic: "任意",
      content: "任意",
      status: "view",
    },
  ];

  const matchList = ref([]);
  const matchListBackup = ref("");

  // 校验"匹配参数"内容
  const verifyParams = row => {
    const formatCheck = /^[a-zA-Z0-9!\"#$%&'()*+,\-./:;<=>?@\[\\\]^_`{|}~]+$/;
    switch (row.field) {
      // case "srcIp":
      //   break;
      // case "path":
      //   break;
      case "requestHeader":
        if (!row.params) return "请输入头部名称";
        if (row.params.length > 128) return "长度限制为128个字符";
        if (!formatCheck.test(row.params)) return "请输入英文、数字、英文标点符号";
        break;
      // case "requestMethod":
      //   break;
      case "queryParameter":
        if (!row.params) return "请输入参数名称";
        if (row.params.length > 128) return "长度限制为128个字符";
        if (!formatCheck.test(row.params)) return "请输入英文、数字、英文标点符号";
        break;
      // case "userName":
      //   break;
    }
  };

  // 校验"匹配内容"内容
  const verifyContent = row => {
    let verifyText = "";
    const formatCheck = /^[a-zA-Z0-9!\"#$%&'()*+,\-./:;<=>?@\[\\\]^_`{|}~]+$/;

    const MAX_LENGTH = 10;
    switch (row.field) {
      case "srcIp":
        if (["containsTrue", "containsFalse"].includes(row.logic)) {
          if (!row.content) {
            verifyText = "请输入单个IP、子网掩码或范围，多个以逗号分隔";
          } else {
            const ipList = row.content.split(",");
            const newArr = ipList.filter(item => item.trim() !== "");
            if (newArr.length > MAX_LENGTH) {
              verifyText = `IP不能超过${MAX_LENGTH}条`;
            } else {
              for (let index = 0; index < newArr.length; index++) {
                const e = newArr[index];
                if (!validateIpAll(e)) {
                  verifyText = `第${index + 1}条IP不合法：${e}`;
                }
              }
            }
          }
        } else if (["belongToIpGroup", "notBelongToIpGroup"].includes(row.logic)) {
          if (!row.content || !row.content.length) {
            verifyText = "请选择IP组";
          }
        }
        break;
      case "path":
        if (row.content.length > 512) verifyText = "长度限制为512个字符";
        if (!formatCheck.test(row.content)) verifyText = "请输入英文、数字、英文标点符号";
        break;
      case "requestHeader":
        if (row.content.length > 512) verifyText = "长度限制为512个字符";
        if (!formatCheck.test(row.content)) verifyText = "请输入英文、数字、英文标点符号";
        break;
      case "requestMethod":
        if (!row.content.length) verifyText = "请选择请求方法";
        break;
      case "queryParameter":
        if (row.content.length > 512) verifyText = "长度限制为512个字符";
        if (!formatCheck.test(row.content)) verifyText = "请输入英文、数字、英文标点符号";
        break;
      case "userName":
        if (!row.content.length) verifyText = "请输入账号";
        const accountContentList = row.content.split(",");
        if (accountContentList.length > 10) verifyText = "账号不能超过10个";
        accountContentList.some(item => {
          if (item.length > 128) verifyText = "单个长度限制为128个字符";
          if (item && !item.trim()) verifyText = "单个账号不能只为空格";
        });
        break;
      case "sourceLocation":
        if (!formatCheck.test(row.content)) verifyText = "请输入英文、数字、英文标点符号";
        if (!row.content.length) verifyText = "请输入域名或地址";
        break;
      case "region":
        if (!row.content.length) verifyText = "请选择IP归属地区";
        break;
    }
    return verifyText;
  };

  // 获取“匹配参数”占位符
  const getParamsPlaceholder = row => {
    let placeholder = "请输入";
    switch (row.field) {
      case "requestHeader":
        placeholder = "请输入头部名称";
        break;
      case "queryParameter":
        placeholder = "请输入参数名称";
        break;
      default:
        placeholder = "请输入";
        break;
    }
    return placeholder;
  };

  // 获取“匹配内容”占位符
  const getContentPlaceholder = row => {
    let placeholder = "请输入";
    switch (row.field) {
      case "srcIp":
        // placeholder = "请输入单个IP、子网掩码或范围，多个以逗号分隔";
        placeholder = "请输入";
        break;
      case "path":
        // placeholder = "请输入英文、数字、英文标点符号";
        placeholder = "请输入";
        break;
      case "requestHeader":
        // placeholder = "请输入英文、数字、英文标点符号，512字符以内";
        placeholder = "请输入";
        break;
      // case "requestMethod":
      //   placeholder = "请选择";
      //   break;
      case "queryParameter":
        // placeholder = "请输入英文、数字、英文标点符号，512字符以内";
        placeholder = "请输入";
        break;
      case "userName":
        // placeholder = "请输入账号，128个字符以内";
        placeholder = "请输入";
        break;
      default:
        placeholder = "请输入";
        break;
    }
    return placeholder;
  };

  // 获取逻辑选项
  const getLogicOptions = (row, generalPurposeLibrary = false) => {
    let filterTag = [];
    switch (row.field) {
      case "srcIp":
        filterTag = ["containsTrue", "containsFalse"];
        generalPurposeLibrary && filterTag.push(...["belongToIpGroup", "notBelongToIpGroup"]);
        break;
      case "path":
        filterTag = ["exactTrue", "containsTrue", "exactFalse", "containsFalse", "prefix", "regex"];
        break;
      case "requestHeader":
        filterTag = [
          "exactTrue",
          "containsTrue",
          "exactFalse",
          "containsFalse",
          "null",
          "presentMatchTrue",
          "presentMatchFalse",
          "regex",
        ];
        break;
      case "requestMethod":
        filterTag = ["exactTrue", "exactFalse"];
        break;
      case "queryParameter":
        filterTag = [
          "exactTrue",
          "containsTrue",
          "exactFalse",
          "containsFalse",
          "null",
          "presentMatchTrue",
          "presentMatchFalse",
          "regex",
        ];
        break;
      case "userName":
        filterTag = ["exactTrue", "exactFalse"];
        break;
      case "sourceLocation":
        filterTag = ["exactTrue", "exactFalse", "containsTrue", "containsFalse"];
        break;
      case "region":
        filterTag = ["containsTrue", "containsFalse"];
        break;
    }
    return logicOptionsAll.filter(item => filterTag.includes(item.value));
  };

  // 获取显示状态下的"匹配字段"文字
  const getFieldText = field => {
    return fieldOptionsAll.value.find(item => item.value === field)?.label;
  };

  // 获取显示状态下的"逻辑"文字
  const getLogicText = logic => {
    return logicOptionsAll.find(item => item.value === logic)?.label;
  };

  // 获取显示状态下的"匹配内容"文字
  const getContentText = row => {
    if (row.field === "requestMethod") {
      if (row.content.length === methodOptions.length) {
        return "全部";
      } else {
        const checkList = methodOptions.filter(item => row.content.some(item2 => item.value === item2));
        const labelList = checkList.map(item => item.label);
        return labelList.join("，");
      }
    } else if (row.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(row.logic)) {
      return row.content
        .map(item => {
          return store.ipGroupList.find(ipItem => ipItem.value === item).label;
        })
        .join("，");
    } else if (row.field === "region") {
      return row.content
        .map(locationItem => {
          return findNodeLabel(locationItem, locationTreeList);
        })
        .join("，");
    } else {
      return row.content;
    }
  };

  // “确定”按钮禁用状态
  const confirmBtnDisabled = row => {
    if (
      ["requestHeader", "queryParameter"].includes(row.field) &&
      ["null", "presentMatchTrue", "presentMatchFalse"].includes(row.logic)
    ) {
      return Boolean(verifyParams(row) || !row.field || !row.logic);
    } else {
      return Boolean(verifyContent(row) || verifyParams(row) || !row.field || !row.logic);
    }
  };

  // “添加”按钮禁用状态
  const addBtnDisabled = computed(() => {
    return matchList.value.some(item => item.status === "edit") || matchList.value.length >= 10;
  });

  // “编辑”按钮禁用状态
  const editBtnDisabled = computed(() => {
    return matchList.value.some(item => item.status === "edit");
  });

  return {
    anyMatch,
    matchList,
    matchListBackup,
    verifyParams,
    verifyContent,
    getLogicOptions,
    getParamsPlaceholder,
    getContentPlaceholder,
    getFieldText,
    getContentText,
    getLogicText,
    confirmBtnDisabled,
    addBtnDisabled,
    editBtnDisabled,
  };
}
