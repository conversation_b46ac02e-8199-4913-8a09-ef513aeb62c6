<template>
  <!--  AAC创建配置 -->
  <div class="plugin-aac-creat-configuration add_conent_flex1">
    <ou-form label-width="140px" label-position="right" class="-mb-20">
      <ou-form-item label="配置规则：" prop="plugin">
        <div class="config-rule-container">
          <div class="btn-row">
            <ou-button @click="addDialogOpen" type="primary" text :disabled="configRule.length >= 100">添加</ou-button>
            <ou-button @click="batchesDelClick" type="primary" text :disabled="!selectionsList.length">
              批量删除
            </ou-button>
            <moveGroup
              v-model:list="configRule"
              :selections="selectionsList"
              @moved="routeMoved"
              :disabled="!selectionsList.length"
              style="margin-left: 8px"></moveGroup>
          </div>
          <ou-table
            ref="tableRef"
            :data="configRule"
            :height="!configRule.length ? 200 : configRule.length >= 5 ? 240 : 'auto'"
            class="editor"
            style="width: 100%; --table-empty-size: 120px"
            @select="value => handleSelectionChange(value)"
            @select-all="value => handleSelectionChange(value)">
            <ou-table-column type="selection" width="32" align="center" :selectable="row => !row.isDefault" />
            <ou-table-column type="index" label="优先级" width="58" align="center" />
            <ou-table-column prop="ruleName" label="规则名称">
              <template #default="scope">
                <ou-text-ellipsis :content="String(scope.row.ruleName)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="matchedConditionInfo" label="匹配条件">
              <template #default="scope">
                <ou-text-ellipsis :content="getTextInfo(scope.row.matchingList)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="act" label="动作" width="80">
              <template #default="scope">
                <ou-text-ellipsis :content="getOptionsLabel(scope.row.act, actionOptions) || '-'"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="enable" label="状态" width="106">
              <template #default="scope">
                <ou-switch
                  v-model="scope.row.enable"
                  active-text="启用"
                  inactive-text="禁用"
                  class="switch-offside"
                  size="small"></ou-switch>
              </template>
            </ou-table-column>
            <ou-table-column prop="address" label="操作" width="176">
              <template #default="scope">
                <ou-button @click="updateClick(scope.$index, scope.row)" type="primary" text>编辑</ou-button>
                <ou-button
                  @click="copyClick(scope.$index, scope.row)"
                  type="primary"
                  text
                  :disabled="scope.row.isDefault || configRule.length >= 100">
                  复制
                </ou-button>
                <ou-button
                  @click="topPostClick(scope.$index, scope.row)"
                  type="primary"
                  text
                  :disabled="scope.row.isDefault">
                  置顶
                </ou-button>
                <ou-button v-if="!scope.row.isDefault" @click="delClick(scope.$index)" type="primary" text>
                  删除
                </ou-button>
              </template>
            </ou-table-column>
          </ou-table>
        </div>
      </ou-form-item>
    </ou-form>
    <ou-opera-dialog
      :title="`${editType}配置规则`"
      v-model="dialogShow"
      @close="dialogClose"
      @cancel="dialogClose"
      @confirm="dialogConfirm"
      :close-on-click-modal="false"
      :width="1139"
      draggable>
      <template #content>
        <ou-form ref="dialogFormRef" :rules="dialogFormRules" :model="dialogForm" label-width="120px" class="-mb-20">
          <ou-form-item label="规则名称：" prop="ruleName">
            <ou-input
              v-model.trim="dialogForm.ruleName"
              placeholder="请输入"
              maxlength="50"
              show-word-limit
              style="width: 300px"
              :disabled="dialogForm.isDefault"></ou-input>
          </ou-form-item>
          <!-- <ou-form-item label="匹配条件：" prop="matchingList">
            <matchedConditionEditor
              v-if="dialogShow"
              ref="matchedConditionEditorRef"
              v-model="dialogForm.matchingList"
              :showAny="dialogForm.isDefault"
              :fieldOptions="[
                'srcIp',
                'path',
                'requestHeader',
                'requestMethod',
                'queryParameter',
                'userName',
                'sourceLocation',
                'region',
              ]"
              generalPurposeLibrary
              style="width: 100%"></matchedConditionEditor>
          </ou-form-item> -->
          <ou-form-item label="匹配条件：" prop="matchingList">
            <ou-collapse-panel class="matchedConditionEditorCollapse" v-model:isCollapse="matchConditionCollapse">
              <template #headerLeft>
                <span class="header-hint">多个条件同时满足才执行对应动作</span>
              </template>
              <template #content>
                <matchedConditionEditor
                  v-if="dialogShow"
                  ref="matchedConditionEditorRef"
                  v-model="dialogForm.matchingList"
                  :showAny="dialogForm.isDefault && dialogForm.ruleName === '默认规则'"
                  :fieldOptions="[
                    'srcIp',
                    'path',
                    'requestHeader',
                    'requestMethod',
                    'queryParameter',
                    'userName',
                    'sourceLocation',
                    'region',
                    'time',
                  ]"
                  style="width: 100%"></matchedConditionEditor>
              </template>
            </ou-collapse-panel>
          </ou-form-item>
          <!-- :showAny="dialogForm.isDefault" todo -->
          <ou-form-item label="动作：" prop="act" class="line-height-22">
            <ou-radio-group v-model="dialogForm.act">
              <ou-radio v-for="item in actionOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </ou-radio>
            </ou-radio-group>
          </ou-form-item>
          <template v-if="dialogForm.act === 1">
            <ou-form-item label="类型：" prop="type">
              <ou-select v-model="dialogForm.type" placeholder="请选择" style="width: 300px">
                <ou-option v-for="(item, index) in typeOptions" :key="index" :label="item.label" :value="item.value" />
              </ou-select>
            </ou-form-item>
            <ou-form-item label="返回页面：" prop="denyContext">
              <div class="return-page-area-content">
                <div v-if="['html', 'json'].includes(dialogForm.type)" class="content-header">
                  <div @click="denyContextPreviewOpen" class="preview-btn">
                    <ou-icon size="16"><YanjingKai /></ou-icon>
                    <span>预览</span>
                  </div>
                </div>
                <ou-input
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 3, maxRows: 9 }"
                  placeholder="请输入返回页面内容，不填写则显示默认内容"
                  v-model="dialogForm.denyContext"
                  style="width: 100%"></ou-input>
              </div>
            </ou-form-item>
          </template>
        </ou-form>
      </template>
    </ou-opera-dialog>
    <ou-opera-dialog
      title="预览"
      v-model="denyContextPreviewShow"
      width="1200px"
      :close-on-click-modal="false"
      @close="denyContextPreviewClose"
      @cancel="denyContextPreviewClose"
      draggable
      hideFooter
      class="aac--denyContext-preview-dialog">
      <template #content>
        <div class="denyContext-preview-content">
          <ou-scrollbar>
            <div v-if="dialogForm.type === 'html'" v-html="dialogForm.denyContext" class="html-content"></div>
            <pre v-if="dialogForm.type === 'json'" v-html="denyContextJSON" class="json-content"></pre>
          </ou-scrollbar>
        </div>
      </template>
      <template #footer>
        <ou-button @click="denyContextPreviewClose">关闭</ou-button>
      </template>
    </ou-opera-dialog>
  </div>
</template>
<script setup>
  import { defineProps, defineExpose, ref, nextTick, computed } from "vue";
  import moveGroup from "@/components/MoveGroup/index.vue";
  import matchedConditionEditor from "@/components/NewMatchedConditionEditor/index.vue";
  import { OuModal } from "@ouryun/ouryun-plus";
  import { getTextInfo, getMatchParameter, compatibleMatchingList } from "@/utils/MatchedCondition.js";
  import hookStates from "../hook/index.js";
  import { defaultConfigHooks } from "../hook/config-default.js";

  import usePlugin from "basePluginStore";
  // import usePlugin from "@/store/modules/plugin.js";
  const store = usePlugin();

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
  });

  const { actionOptions, typeOptions, dialogFormRules, DialogForm, dialogForm, matchConditionCollapse } = hookStates();
  const { getDefaultConfig } = defaultConfigHooks();

  const onCreated = () => {
    if (props.content || props.global) {
      initForm();
    } else {
      //使用【内置策略】
      const defaultConfig = getDefaultConfig();
      configRule.value = JSON.parse(JSON.stringify(defaultConfig));
    }
  };

  const initForm = () => {
    const content = JSON.parse(props.content || props.global);
    configRule.value = content.plugin_config.rules.map((item, index) => {
      return {
        ruleName: item.rule_name,
        matchingList: compatibleMatchingList(content.meta_data[index].matchingList),
        act: item.act,
        enable: item.enable,
        denyContext: item.deny_context,
        type: item.type,
        isDefault: content.meta_data[index].isDefault,
      };
    });
  };

  const configRule = ref([]);

  let selectionsList = ref([]);
  const handleSelectionChange = value => {
    selectionsList.value = value;
  };

  const dialogShow = ref(false);

  let activeTableIndex = null;
  const editType = ref("添加");
  const addDialogOpen = () => {
    editType.value = "添加";
    dialogShow.value = true;
  };

  const dialogClose = () => {
    dialogShow.value = false;

    setTimeout(() => {
      dialogFormRef.value.resetFields();
      Object.assign(dialogForm, new DialogForm());
    }, 500);
  };

  const dialogFormRef = ref(null);
  const matchedConditionEditorRef = ref(null);

  const dialogConfirm = () => {
    const matchedConditionValid = matchedConditionEditorRef.value.validator();
    dialogFormRef.value.validate(valid => {
      if (valid && matchedConditionValid) {
        const form = JSON.parse(JSON.stringify(dialogForm));

        if (["添加"].includes(editType.value)) {
          form.enable = true;
        } else {
          form.enable = configRule.value[activeTableIndex].enable || false;
        }

        if (["添加", "复制"].includes(editType.value)) {
          if (configRule.value.some(item => item.ruleName === form.ruleName)) {
            return OuModal.warning(`规则名称${form.ruleName}已存在`);
          }
          configRule.value.unshift(form);
        } else {
          if (
            configRule.value.some(item => item.ruleName === form.ruleName) &&
            configRule.value[activeTableIndex].ruleName !== form.ruleName
          ) {
            return OuModal.warning(`规则名称${form.ruleName}已存在`);
          }
          configRule.value[activeTableIndex] = form;
        }
        dialogClose();
      }
    });
  };
  const denyContextPreviewShow = ref(false);
  const denyContextPreviewOpen = () => {
    denyContextPreviewShow.value = true;
  };
  const denyContextPreviewClose = () => {
    denyContextPreviewShow.value = false;
  };
  const denyContextJSON = computed(() => {
    function isJSON(str) {
      try {
        JSON.parse(str);
        return true;
      } catch (e) {
        return false;
      }
    }
    if (isJSON(dialogForm.denyContext)) {
      return JSON.stringify(JSON.parse(dialogForm.denyContext), null, 2);
    } else {
      return "JSON格式错误";
    }
  });

  const updateClick = (tableIndex, row) => {
    editType.value = "编辑";
    activeTableIndex = tableIndex;
    for (let k in dialogForm) {
      if (dialogForm.hasOwnProperty(k)) {
        console.log(k, row[k]);
        dialogForm[k] = JSON.parse(JSON.stringify(row[k]));
      }
    }
    dialogShow.value = true;
  };
  const copyClick = (tableIndex, row) => {
    editType.value = "复制";
    activeTableIndex = tableIndex;
    for (let k in dialogForm) {
      if (["ruleName"].includes(k)) {
        dialogForm[k] = row[k] + "_复制";
      } else {
        if (dialogForm.hasOwnProperty(k)) {
          dialogForm[k] = row[k];
        }
      }
    }
    dialogShow.value = true;
  };
  const topPostClick = (tableIndex, row) => {
    configRule.value.splice(tableIndex, 1);
    configRule.value.unshift(row);
  };
  const delClick = tableIndex => {
    // 若seconds中存在删除项，则删除记录值
    const include = selectionsList.value.findIndex(item => item === configRule.value[tableIndex]);
    if (include !== -1) selectionsList.value.splice(include, 1);

    // 删除列表项
    configRule.value.splice(tableIndex, 1);
  };

  const batchesDelClick = () => {
    if (selectionsList.value.length) {
      const content = {
        content: `提示`,
        moreContent: `确定要删除${selectionsList.value.length}条规则吗？`,
      };
      OuModal.confirm(content)
        .then(() => {
          // 删除列表项
          configRule.value = configRule.value.filter(item => !selectionsList.value.includes(item));
          // 清空selections记录值
          selectionsList.value.splice(0, selectionsList.value.length);
        })
        .catch(() => {});
    }
  };

  const tableRef = ref(null);
  const routeMoved = () => {
    // 移动后重新设置选中
    nextTick(() => {
      selectionsList.value.forEach(row => {
        tableRef.value.toggleRowSelection(row);
      });
    });
  };

  const getOptionsLabel = (value, options) => {
    return options.find(item => item.value === value)?.label || "";
  };

  const submit = async () => {
    const params = {
      plugin_config: {
        rules: [],
      },
      meta_data: [],
    };

    configRule.value.forEach((item, index) => {
      params.plugin_config.rules.push({
        id: index + 1,
        enable: item.enable,
        rule_name: item.ruleName,
        act: item.act,
        deny_context: item.denyContext,
        type: item.type,
        ...getMatchParameter(item.matchingList),
      });
      params.meta_data.push({
        isDefault: item.isDefault,
        matchingList: item.matchingList,
      });
    });

    const extension = { refs: { ip_address_database: [], time_range_database: [] } };

    const ipAddressArr = [];
    const timeRangeArr = [];

    params.meta_data.forEach(item => {
      item.matchingList.forEach(matchItem => {
        if (matchItem.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic)) {
          ipAddressArr.push(...matchItem.content);
        } else if (
          matchItem.field === "time" &&
          ["belongToTimeRange", "notBelongToTimeRange"].includes(matchItem.logic)
        ) {
          timeRangeArr.push(matchItem.content);
        }
      });
    });

    const configs = [
      { key: "ip_address_database", arr: ipAddressArr },
      { key: "time_range_database", arr: timeRangeArr },
    ];

    configs.forEach(({ key, arr }) => {
      extension.refs[key] = [...new Set(arr)].map(item => ({ item_id: item }));
    });

    return {
      contentParams: params,
      extension,
    };
  };

  onCreated();

  defineExpose({
    submit,
  });
</script>

<style lang="scss" scoped>
  .plugin-aac-creat-configuration {
    .ouryun-form-item-label-group {
      display: flex;
      align-items: center;
      column-gap: 4px;
      position: relative;
    }

    .config-rule-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      .btn-row {
        height: 32px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
      }
    }

    .return-page-area-content {
      width: 100%;
      .content-header {
        width: 100%;
        height: 32px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        border: 1px solid var(--ouryun-input-border-color, var(--ouryun-border-color));
        border-bottom: none;
        border-radius: var(--ouryun-border-radius-base) var(--ouryun-border-radius-base) 0 0;
      }
      .preview-btn {
        font-size: 14px;
        color: #999;
        display: flex;
        column-gap: 4px;
        align-items: center;
        line-height: 1;
        cursor: pointer;
        &:hover {
          color: var(--ouryun-color-brand-base);
        }
      }
      ::v-deep {
        .ouryun-textarea__inner {
          margin-left: 1px;
          width: calc(100% - 2px);
          box-sizing: border-box;
          border-radius: var(--ouryun-border-radius-base) !important;
          box-shadow: initial;
          box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color));
          &:hover {
            box-shadow: 0 0 0 1px var(--ouryun-input-hover-border-color);
          }
          &:focus {
            box-shadow: 0 0 0 1px var(--ouryun-input-focus-border-color);
          }
        }
        .content-header + .ouryun-textarea {
          .ouryun-textarea__inner {
            border-radius: 0px 0px var(--ouryun-border-radius-base) var(--ouryun-border-radius-base) !important;
          }
        }
      }
    }

    .denyContext-preview-content {
      width: 100%;
      height: calc(60vh - 90px);
      background: #f6f7fc;
      border-radius: var(--ouryun-border-radius-base);
      box-sizing: border-box;

      .html-content,
      .json-content {
        padding: 12px;
        width: 100%;
        margin: 0;
        font-family: "思源黑体";
        color: #333333;
        line-height: 1.5;
      }
    }
  }
  .ouryun-form.ouryun-form--default > .ouryun-form-item-custom {
    margin-bottom: 0px !important;
  }
</style>
