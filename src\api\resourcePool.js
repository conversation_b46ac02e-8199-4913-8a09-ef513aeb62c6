import request from "baseRequest";

// 获取安全资源池列表
export const getResourcePoolListAPI = () => {
  return request({
    url: `/sr-engine/networkConfig/v1/object/upstream/superGlue`,
    method: "get",
  });
};
// 获取安全资源池详情
export const getResourcePoolDetailsAPI = params => {
  return request({
    url: `/sr-engine/networkConfig/v1/object/upstream/superGlueDetail`,
    method: "get",
    params,
  });
};
// 新增安全资源池
export const addResourcePoolAPI = (data, params) => {
  return request({
    url: "/v1/upstream/ENGINEID",
    method: "post",
    data,
    params,
  });
};
// 更新安全资源池
export const updateResourcePoolAPI = (data, params, superGlueUpstream, commonName) => {
  return request({
    url: `/v1/upstream/ENGINEID/${superGlueUpstream}`,
    // url: `/v1/upstream/ENGINEID/${superGlueUpstream}?commonName=${commonName}`,
    method: "put",
    data,
    params,
  });
};
// 删除安全资源池
export const delResourcePoolAPI = (data, params) => {
  return request({
    url: `/v1/upstream/ENGINEID/${data}`,
    method: "delete",
    params,
  });
};
// 获取安全资源状态
export const getEndpointsStateAPI = params => {
  return request({
    url: "/insights/v1/upstream/state/summary",
    method: "get",
    params,
    // noEngineId: true,
  });
};

export function healthCheckGET(data) {
  return request({
    url: "/v1/healthCheck/ENGINEID",
    method: "get",
    params: data,
  });
}

// 同步更新配置中的资源池数据
export const synchroUpdateResourcePoolAPI = data => {
  return request({
    url: `/plugiverse/v1/engine/updateResourcePool`,
    method: "put",
    data,
  });
};
