import usePlugin from "basePluginStore";

export const defaultConfig = [
  {
    rule_name: "内置规则-防止数据批量泄露",
    match_data: [{ label: "label", logic: "isBelongTrue", matchContent: [], symbol: "GE", quantity: "200" }],
    matchers: [],
    act: 1,
    deny_type: 1,
    deny_context: "",
    enable: false,
    isDefault: true,
  },
  {
    rule_name: "内置规则-非工作时间访问数据限制",
    match_data: [{ label: "label", logic: "isBelongTrue", matchContent: [], symbol: "GE", quantity: "100" }],
    matchers: [{ field: "time", logic: "notBelongToTimeRange", params: "", content: "" }],
    act: 1,
    deny_type: 1,
    deny_context: "",
    enable: false,
    isDefault: true,
  },
  {
    rule_name: "内置规则-防止境外IP获取敏感数据",
    match_data: [{ label: "label", logic: "isBelongTrue", matchContent: [], symbol: "GE", quantity: "50" }],
    matchers: [
      {
        field: "region",
        logic: "containsFalse",
        params: "",
        content: [
          "110000",
          "120000",
          "130000",
          "140000",
          "150000",
          "210000",
          "220000",
          "230000",
          "310000",
          "320000",
          "330000",
          "340000",
          "350000",
          "360000",
          "370000",
          "410000",
          "420000",
          "430000",
          "440000",
          "450000",
          "460000",
          "500000",
          "510000",
          "520000",
          "530000",
          "540000",
          "610000",
          "620000",
          "630000",
          "640000",
          "650000",
          "710000",
          "810000",
          "820000",
        ],
      },
    ],
    act: 1,
    deny_type: 1,
    deny_context: "",
    enable: true,
    isDefault: true,
  },
  {
    rule_name: "内置规则-防止用户信息批量泄露",
    match_data: [{ label: "label", logic: "isBelongTrue", matchContent: [], symbol: "GE", quantity: "100" }],
    matchers: [{ field: "path", logic: "regex", params: "", content: "^/(.*/)?(?:admin/|api/)?users" }],
    act: 1,
    deny_type: 1,
    deny_context: "",
    enable: true,
    isDefault: true,
  },
  {
    rule_name: "内置规则-防止API接口遍历翻页返回大量数据",
    match_data: [{ label: "label", logic: "isBelongTrue", matchContent: [], symbol: "GE", quantity: "100" }],
    matchers: [{ field: "queryParameter", params: "page", logic: "presentMatchTrue", content: "" }],
    act: 1,
    deny_type: 1,
    deny_context: "",
    enable: false,
    isDefault: true,
  },
];

export function defaultConfigHooks() {
  const store = usePlugin();

  const getDefaultConfig = list => {
    // 获取【策略】-【资产识别】-【数据识别】中所有内置的数据标签（状态为启用的数据标签）
    // 5条内置策略中，匹配数据均是数据标签，如查询不到【内置数据，且启用状态】的标签，则返回空数组
    const builtin_signs = list
      ?.filter(item => item.builtin_sign === 1 && item.enabled_sign === 1)
      ?.map(sign => sign.value);
    if (!builtin_signs?.length) {
      // 如果没有内置数据标签，则返回空数组
      return [];
    }
    defaultConfig.forEach(config => {
      config.match_data.forEach(item => {
        // 如果匹配内容是数据标签，则赋值为内置数据标签的值
        if (item.label === "label") {
          item.matchContent = builtin_signs;
        }
      });
    });

    // 获取时间段中【工作时间】这一条数据，通过label和disabled属性来查找
    const workTime = store.timePeriodsList?.find(item => item.label === "工作时间" && !item.disabled);
    if (workTime) {
      // 如果工作时间存在，则遍历默认配置赋值为工作时间id
      defaultConfig?.forEach(config => {
        config.matchers.forEach(matcher => {
          // 如果匹配内容是时间,且 属于时间段/不属于时间段，则赋值为工作时间id
          if (matcher.field === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(matcher.logic)) {
            matcher.content = workTime.value;
          }
        });
      });
      return defaultConfig;
    }

    // 如果工作时间不存在，删除该条时间匹配条件
    return defaultConfig.filter(config => {
      // 如果匹配内容是时间,且 属于时间段/不属于时间段，则赋值为工作时间id
      let isDeleteConfig = config.matchers.find(
        matcher => matcher.field === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(matcher.logic),
      );
      return isDeleteConfig?.field !== "time";
    });
  };
  return {
    getDefaultConfig,
  };
}
