import request from "baseRequest";

//脱敏插件
export const desensitization = params => {
  const config = {
    // url: `/sr-engine/engine-manager/v1/pluginApi?path=/plugiverse/v1/engine/dm/${params.tab}/${params.type}`,
    url: `/plugiverse/v1/engine/dm/${params.tab}/${params.type}`,
    method: params.method,
    noEngineId: true,
  };
  if (params.data) {
    config.data = params.data;
  }
  if (params.params) {
    config.params = params.params;
  }
  return request(config);
};

//获取标签
export const getLabelList = () => {
  const config = {
    url: "/apione/v2/data/labels/all",
    method: "get",
  };
  return request(config);
};

//获取脱敏规则
export const getMaskingRule = () => {
  const config = {
    url: "/apione/v2/dm/rules/all",
    method: "get",
  };
  return request(config);
};

//获取全部样本
export const getSampleAll = () => {
  const config = {
    url: `/apione/v2/dm/sample/all`,
    method: "get",
  };
  return request(config);
};

//测试脱敏数据
export const testResult = data => {
  const config = {
    // url: "/sr-engine/engine-manager/v1/pluginApi?path=/plugiverse/v1/engine/dm/transPreview",
    // url: "/sr-engine/engine-manager/v1/plugin/preview/dataTest",
    url: "/plugiverse/v1/engine/dm/transPreview",
    method: "post",
    data,
  };

  return request(config);
};

export const testSampleData = data => {
  const config = {
    url: "/plugiverse/v1/sc/dm/transPreview",
    method: "post",
    data,
  };
  return request(config);
};

//waf白名单文件上传
export const uploadExcel = data => {
  return request({
    url: "/plugiverse/v1/sc/plugin/waf/white_list/parse",
    method: "post",
    noTransmit: true,
    upload: true,
    data,
  });
};

//获取用户账号
export const getUserAccount = params => {
  return request({
    url: "/apione/v2/user/accounts/list",
    method: "get",
    params,
  });
};

//获取弱密码规则库
export const getWeakRuleBase = params => {
  return request({
    url: "/apione/v2/wpd/rules",
    method: "get",
    params,
  });
};

//获取waf防护类型
export const getWafType = params => {
  return request({
    url: "/insights/v1/plugin/waf/attack_rule",
    method: "get",
    params,
  });
};
