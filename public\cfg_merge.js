// 由于后端使用的Goja库不支持split方法，因此使用此函数替换split将字符串分割为数组
function customSplit(str, symbol) {
  let temp = "";
  const result = [];
  for (const char of str) {
    if (char === symbol) {
      result.push(temp);
      temp = "";
    } else {
      temp += char;
    }
  }
  result.push(temp);
  return result;
}

/**
 * 该函数用于判断是否需要重置匹配规则
 * @function isNeedReset
 * @param {array} matchingList - 匹配条件配置注解
 * @param {object} updateContent - 被修改的通用库
 * @return {boolean}  是否需要重置匹配规则
 */
function isNeedReset(matchingList, updateContent) {
  const needReset = matchingList.some(matchItem => {
    return (
      (matchItem.field === "srcIp" &&
        ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic) &&
        matchItem.content.includes(updateContent.id)) ||
      (matchItem.field === "time" &&
        ["belongToTimeRange", "notBelongToTimeRange"].includes(matchItem.logic) &&
        matchItem.content === updateContent.id)
    );
  });
  return needReset;
}

/**
 * 该函数用于将旧匹配条件配置参数转换为新匹配条件配置参数
 * @function mergeMatchConfig
 * @param {string} sourceName - 被修改的通用库名称
 * @param {array} matchParameter - 匹配条件配置参数
 * @param {object} updateContent - 被修改的通用库对象信息
 * @param {array} matchingList - 匹配条件配置注解列表
 */
function mergeMatchConfig(sourceName, matchParameter, updateContent, matchingList) {
  matchParameter.forEach(newRulesItem => {
    if (sourceName === "ip_address_database" && newRulesItem.hasOwnProperty("ip_list")) {
      newRulesItem.ip_list.ip_groups.forEach(ipGroup => {
        if (ipGroup.id === updateContent.id) {
          const ipRangeAllList = updateContent.ip_ranges;
          const ipRangeList = ipRangeAllList.filter(item => item.includes("-"));
          const ipSetList = ipRangeAllList.filter(item => item && !item.includes("-"));

          ipGroup.ip_range = ipRangeList.map(item => {
            const rangeItem = customSplit(item, "-");
            return {
              start_ip: rangeItem[0],
              end_ip: rangeItem[1],
            };
          });

          ipGroup.ip_set.list = ipSetList.map(item => {
            if (item.includes("/")) {
              const rangeItem = customSplit(item, "/");
              return {
                address_prefix: rangeItem[0],
                prefix_len: +rangeItem[1],
              };
            } else {
              return {
                address_prefix: item,
                prefix_len: 32,
              };
            }
          });
        }
      });
    }

    if (sourceName === "time_range_database" && newRulesItem.hasOwnProperty("date")) {
      const timeMatching = matchingList.find(
        item => item.field === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(item.logic),
      );

      const weekMapList = [
        {
          name: "Mon",
          value: 1,
        },
        {
          name: "Tue",
          value: 2,
        },
        {
          name: "Wed",
          value: 3,
        },
        {
          name: "Thu",
          value: 4,
        },
        {
          name: "Fri",
          value: 5,
        },
        {
          name: "Sta",
          value: 6,
        },
        {
          name: "Sun",
          value: 7,
        },
      ];

      newRulesItem.date.time = {
        time: {
          week: updateContent.rule.week.map(
            weekVal => weekMapList.find(weekMapItem => weekMapItem.value === weekVal).name,
          ),
          time_period: updateContent.rule.time_periods.map(item => `${item.begin}-${item.end}`),
        },
        invert: timeMatching.logic === "notBelongToTimeRange",
      };
    }
  });
}

// 应用级ACL插件
function mergeApplicationAcl(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(sourceName, newRules.match, updateContent, contentObj.meta_data[index].matchingList);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 请求频率控制插件
function mergeRatelimit(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(sourceName, newRules.matchers, updateContent, contentObj.meta_data[index].matchingList);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}
// 流量编排插件
function mergeTrafficArrangement(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data.match[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(sourceName, newRules.matchers, updateContent, contentObj.meta_data[index].matchingList);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 数据脱敏插件
function mergeMaskingData(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database", "date_label_rule"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }
  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    let rules = {};
    if (sourceName === "date_label_rule") {
      const { id: updateId, name: updateName } = updateContent;
      item.rewrite_data.forEach(({ identify_rule, rewrite_rule }) => {
        if (identify_rule.unique_identify_id === updateId) {
          identify_rule.sensitive_type = updateName;
          rewrite_rule.sensitive_type = updateName;
        }
      });
    }
    const needReset = isNeedReset(contentObj.meta_data.form.tacticsList[index].matchRule, updateContent);
    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(
        sourceName,
        newRules.match,
        updateContent,
        contentObj.meta_data.form.tacticsList[index].matchRule,
      );
      rules = newRules;
    } else {
      rules = item;
    }
    return rules;
  });
  //等C++兼容id再做转换
  // const pluginConfigWhite = contentObj.meta_data.form.whiteList.map((e) => {
  //   if (sourceName === "date_label_rule") {
  //     const { sensitiveType, conditionType } = e;
  //     const { id: updateId, name: updateName } = updateContent;
  //     const labelItemList = sensitiveType.split(",");
  //     const labelIsExist = labelItemList.indexOf(`${updateId}`);
  //     const { ip_whitelist, user_whitelist } = contentObj.plugin_config.whitelist;
  //     if (labelIsExist !== -1) {

  //       if(conditionType === 1) {

  //           console.log(, conditionType);
  //       }
  //        if(conditionType === 0) {

  //       }
  //     }
  //   }
  // });
  contentObj.plugin_config.rules = pluginConfigRules;
  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 认证鉴权
function mergeIdentityVerification(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rule_config.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(sourceName, newRules.match_info, updateContent, contentObj.meta_data[index].matchingList);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rule_config = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 数据访问控制
function mergeDac(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database", "date_label_rule"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules_config.map((item, index) => {
    if (["ip_address_database", "time_range_database"].includes(sourceName)) {
      const needReset = isNeedReset(contentObj.meta_data[index].matchers, updateContent);

      let rules = {};

      if (needReset) {
        const newRules = JSON.parse(JSON.stringify(item));
        mergeMatchConfig(sourceName, newRules.matchers, updateContent, contentObj.meta_data[index].matchers);
        rules = newRules;
      } else {
        rules = item;
      }

      return rules;
    } else if (sourceName === "date_label_rule") {
      const getPatternParameter = matchRuleArr => {
        const patternObj = {
          match_patterns: [],
          ignore_rules: [],
        };

        matchRuleArr.forEach(matchRule => {
          if (!matchRule) return;

          (matchRule.rule["identification_rules"] || []).forEach(rule => {
            rule.match_rules.forEach(matchRule => {
              // 识别规则，仅添加 通用 / 流量日志且识别位置包含响应体的正则
              if ([1].includes(rule.scope) || ([2].includes(rule.scope) && rule.region.includes(6))) {
                patternObj.match_patterns.push({
                  pattern: matchRule.Content,
                  match_type: matchRule.match_method === 1 ? "REGEX" : "KEYWORDS",
                  enable: rule.enabled_sign === 1,
                });
              }
            });
          });

          (matchRule.rule["ignore_rules"] || []).forEach(rule => {
            rule.match_rules.forEach(matchRule => {
              // 忽略规则，仅添加 通用 / 流量日志的正则
              if ([1, 2].includes(rule.scope)) {
                patternObj.ignore_rules.push({
                  pattern: matchRule.Content,
                  match_type: matchRule.match_method === 1 ? "REGEX" : "KEYWORDS",
                  enable: rule.enabled_sign === 1,
                });
              }
            });
          });
        });
        return patternObj;
      };

      const metaMatchData = contentObj.meta_data[index].match_data;

      const newRules = JSON.parse(JSON.stringify(item));

      metaMatchData.forEach((metaMatchDataItem, metaMatchDataIndex) => {
        if (metaMatchDataItem.label === "label") {
          metaMatchDataItem.matchContent.forEach((matchContentItem, matchContentIndex) => {
            if (matchContentItem === updateContent.id) {
              const patternObj = getPatternParameter([updateContent]);
              // 修改下发正则
              newRules.match_data[metaMatchDataIndex].match_patterns = patternObj.match_patterns;
              newRules.match_data[metaMatchDataIndex].ignore_rules = patternObj.ignore_rules;
              // 修改注解正则
              metaMatchDataItem.matchContentRegArr[matchContentIndex].pattern.match_patterns =
                patternObj.match_patterns;
              metaMatchDataItem.matchContentRegArr[matchContentIndex].pattern.ignore_rules = patternObj.ignore_rules;
            }
          });
        }
      });
      return newRules;
    }
  });

  contentObj.plugin_config.rules_config = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 数据水印控制插件
function mergeWebWatermark(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const needReset = isNeedReset(contentObj.meta_data.matchers, updateContent);
  let rule = {};

  if (needReset) {
    const newRule = JSON.parse(JSON.stringify(contentObj.plugin_config.rule));
    mergeMatchConfig(sourceName, newRule.matchers, updateContent, contentObj.meta_data.matchers);
    rule = newRule;
  } else {
    rule = contentObj.plugin_config.rule;
  }

  contentObj.plugin_config.rule = rule;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 数据下载控制插件
function mergeDdc(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rule_config.map((item, index) => {
    if (["ip_address_database", "time_range_database"].includes(sourceName)) {
      const needReset = isNeedReset(contentObj.meta_data[index].matchers, updateContent);

      let rules = {};

      if (needReset) {
        const newRules = JSON.parse(JSON.stringify(item));
        mergeMatchConfig(sourceName, newRules.matchers, updateContent, contentObj.meta_data[index].matchers);
        rules = newRules;
      } else {
        rules = item;
      }

      return rules;
    }
  });

  contentObj.plugin_config.rule_config = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

function mergeWeakBase(content, updateContent) {
  const { meta_data } = content;
  const { id } = updateContent;
  const indexList = findAllIndexesByRuleEditValue(id, meta_data.form.ruleList);

  if (!indexList.length) return;
  const meteList = meta_data.form.ruleList;
  const metaData = configMetaData(meteList, updateContent);
  return {
    metaData,
    pluginData: configSubmitData(metaData),
  };
}

const configPosition = list => {
  return list.reduce((acc, cur) => {
    const item = cur.keys.map(e => ({
      position: e,
      fields: cur.values.split(","),
    }));
    acc.push(...item);
    return acc;
  }, []);
};

const configSubmitData = data => {
  return data.reduce((acc, cur) => {
    const { isDefault, password_name, path, status, token, user_name, ruleEditData, status_code, rule_base } = cur;
    const ruleItem = {
      enable: status,
      built_in_rules: false,
      user_name: configPosition(user_name),
      password_name: configPosition(password_name),
      token: configPosition(token),
      path: path.split("\n"),
      status_code: status_code.split("\n").map(e => Number(e)),
      built_in_password: ruleEditData.some(item => item.is_default === true),
      custom_password: configCustomPassword(ruleEditData),
    };
    const defaultRule = {
      enable: status,
      built_in_rules: isDefault,
    };
    const item = isDefault ? defaultRule : ruleItem;
    acc.push(item);
    return acc;
  }, []);
};

const configCustomPassword = list => {
  return list
    .filter(e => !e.is_default)
    .reduce((acc, cur) => {
      const { union_password, simple_password, encrypt_type } = cur;
      const items = (union_password || []).reduce((acc, cur) => {
        for (let key in cur) {
          if (key !== "group_type") {
            if (!acc[key]) {
              acc[key] = [];
            }
            acc[key] = Array.from(new Set(acc[key].concat(cur[key])));
          }
        }
        return acc;
      }, {});
      const type_list = encrypt_type && encrypt_type.length ? encrypt_type : [1];
      const ruleItem = {
        simple_passwords: simple_password,
        encrypt_type: type_list.reduce((acc, cur) => (acc += cur), 0),
        filter_instance_id: `weak-password-detection${Date.now()}`,
      };
      if (items.date_range)
        ruleItem.date_parts = {
          date_range: items.date_range,
          date_format: items.date_format.reduce((acc, cur) => (acc += cur), 0),
        };
      if (items.symbol) ruleItem.symbol_parts = items.symbol;
      if (items.text) ruleItem.text_parts = items.text;
      acc.push(ruleItem);
      return acc;
    }, []);
};

function configMetaData(data, updateContent) {
  return data.reduce((acc, cur) => {
    if (cur.isDefault) {
      acc.push(cur);
    } else {
      const item = {
        ...cur,
        ruleEditData: cur.ruleEditData.map(e => {
          if (e.value === updateContent.id) {
            return {
              value: updateContent.id,
              is_default: updateContent.source_type === 1,
              simple_password: updateContent.simple_password,
              encrypt_type: updateContent.encrypt_type || [],
              union_password: updateContent.union_password,
              name: updateContent.name,
            };
          } else {
            return e;
          }
        }),
      };
      acc.push(item);
    }
    return acc;
  }, []);
}

function findAllIndexesByRuleEditValue(targetValue, data) {
  return data.reduce((result, item, index) => {
    if (Array.isArray(item.ruleEditData)) {
      const hasMatch = item.ruleEditData.some(rule => rule.value === targetValue);
      if (hasMatch) {
        result.push(index);
      }
    }
    return result;
  }, []);
}

function merRuleBase(sourceName, content, globalContent, updateContent) {
  if (!["wpd_rule_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }
  const indexList = findAllIndexesByRuleEditValue(updateContent.id, globalContent.meta_data.form.ruleList);
  if (!indexList.length) return;
  const { metaData, pluginData } = mergeWeakBase(globalContent, updateContent);
  const newContent = {
    content: {},
    globalContent: {},
  };
  const pgPlugin = {
    plugin_config: {
      check_config: [],
      buffer_limit: 1024,
    },
    meta_data: {
      form: {
        ruleList: [],
        multipleList: [],
      },
    },
  };
  pgPlugin.plugin_config.check_config = pluginData;
  pgPlugin.meta_data.form.ruleList = metaData;
  newContent.globalContent = pgPlugin;
  return newContent;
}

//数据复制控制
function mergeDataCopy(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "time_range_database"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data.form.rules[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(sourceName, newRules.match, updateContent, contentObj.meta_data.form.rules[index].matchingList);
      rules = newRules;
    } else {
      rules = item;
    }
    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

function mergeConfig(sourceName, pluginName, content, globalContent, updateContent) {
  switch (pluginName) {
    case "www.srhino.com.application-acl":
      return mergeApplicationAcl(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.ratelimit":
      return mergeRatelimit(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.traffic-arrangement":
      return mergeTrafficArrangement(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.data-masking":
      return mergeMaskingData(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.identity-verification":
      return mergeIdentityVerification(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.dac":
      return mergeDac(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.web-watermark":
      return mergeWebWatermark(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.weak-password-detection":
      return merRuleBase(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.ddc":
      return mergeDdc(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.data-replication-controller":
      return mergeDataCopy(sourceName, content || {}, globalContent || {}, updateContent);
    default:
      return {
        content,
        globalContent,
      };
  }
}
