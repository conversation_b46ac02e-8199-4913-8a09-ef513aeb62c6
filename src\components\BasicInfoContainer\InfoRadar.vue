<template>
  <div
    class="infoRadar"
    :style="{
      'grid-column': props.gridColumn ? props.gridColumn : `${column}/${column + 1}`,
      'grid-row-start': rowspan ? `span ${rowspan}` : '',
    }">
    <div class="radar_left">
      <ou-radar height="180" :indicator="indicator" :seriesData="seriesData" :customOptions="customOptions"></ou-radar>
    </div>
    <divider height="64px" margin="0 25px"></divider>
    <div class="radar_right">
      <div class="right_percent">{{ radarPercent }}</div>
      <div class="right_title" v-if="radarTitle">
        {{ radarTitle }}
        <slot name="tooltip"></slot>
        <!-- <ou-tooltip class="box-item" effect="light" placement="bottom" width="300px" popper-class="no-arrow">
          <template #content>
            multiple lines
            <br />
            second line
          </template>
          <ou-icon style="margin-left: 5px; color: #999"><Notice /></ou-icon>
        </ou-tooltip> -->
      </div>
    </div>
  </div>
</template>
<script setup>
  const props = defineProps({
    indicator: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    seriesData: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    // 分数
    radarPercent: {
      type: [Number, String],
      default: "0",
    },
    // 标题
    radarTitle: {
      type: String,
      default: "",
    },
    //标题描述
    radarDescription: {
      type: String,
      default: "description",
    },
    // 默认column网格线位置和所占的网格
    column: {
      type: [Number, String],
      default: 3,
    },
    //所占行数
    rowspan: {
      type: [Number, String],
      default: 5,
    },
    //自定义column网格线位置和所占的网格
    gridColumn: {
      type: String,
      default: "",
    },
  });
  const customOptions = {
    radar: {
      center: ["55%", "60%"],
    },
  };
</script>
<style scoped lang="scss">
  .infoRadar {
    // width: 380;
    display: flex;
    align-items: center;
    .radar_left {
      width: 260px;
    }
    .radar_right {
      display: flex;
      flex-direction: column;
      align-items: center;
      .right_percent {
        font-size: 32px;
        color: #0066f7;
        line-height: 39px;
        margin-bottom: 4px;
        font-style: italic;
        font-weight: 600;
        margin-left: -10px;
      }
      .right_title {
        font-size: 12px;
        color: #666666;
        line-height: 18px;
        display: flex;
        align-items: center;
      }
    }
  }
</style>
