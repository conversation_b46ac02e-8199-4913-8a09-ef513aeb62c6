
VERSION := $(shell gv)# run 'make tools' first and add $GOPATH/bin to system env PATH
app ?= sc-plugin
# dirs := $(wildcard dist/*/)  # 获取 dist 目录下的所有一级子目录
dirs := $(shell find dist -mindepth 1 -maxdepth 1 -type d)  # 获取 dist 目录下的所有一级子目录

.PHONY: compress_subdirs
compress_subdirs:
	@for dir in $(dirs); do \
		tar czf dist/$${dir##*/}.tar.gz -C $${dir} .; \
	done

.PHONY: check_git_version
check_git_version:
	@test $(VERSION) # check VERSION on line: 7

.PHONY: clean
clean:
	rm -rf dist

.PHONY: release
release: check_git_version clean
	npm run build:prod
	$(MAKE) compress_subdirs
	mkdir $(app)
	mv dist/* $(app)
