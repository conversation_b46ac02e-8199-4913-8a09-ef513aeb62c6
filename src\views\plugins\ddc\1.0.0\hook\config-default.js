import usePlugin from "basePluginStore";

export const defaultConfig = [
  {
    rule_name: "内置规则-非工作时间下载大文件",
    file_info: [{ label: "file_size", logic: "GE", matchContent: "100", unit: "MB" }],
    matchers: [{ field: "time", logic: "notBelongToTimeRange", params: "", content: "" }],
    act: "DENY",
    enable: false,
    isDefault: true,
  },
  {
    rule_name: "内置规则-境外IP大文件下载",
    file_info: [{ label: "file_size", logic: "GE", matchContent: "100", unit: "MB" }],
    matchers: [
      {
        field: "region",
        logic: "containsFalse",
        params: "",
        content: [
          "110000",
          "120000",
          "130000",
          "140000",
          "150000",
          "210000",
          "220000",
          "230000",
          "310000",
          "320000",
          "330000",
          "340000",
          "350000",
          "360000",
          "370000",
          "410000",
          "420000",
          "430000",
          "440000",
          "450000",
          "460000",
          "500000",
          "510000",
          "520000",
          "530000",
          "540000",
          "610000",
          "620000",
          "630000",
          "640000",
          "650000",
          "710000",
          "810000",
          "820000",
        ],
      },
    ],
    act: "DENY",
    enable: true,
    isDefault: true,
  },
];

export function defaultConfigHooks() {
  const store = usePlugin();
  const getDefaultConfig = () => {
    // 获取时间段中【工作时间】这一条数据，通过label和disabled属性来查找
    const workTime = store.timePeriodsList?.find(item => item.label === "工作时间" && !item.disabled);
    return defaultConfig
      .map(config => {
        config.matchers = config.matchers
          .map(matcher => {
            // 如果匹配内容是时间,且 属于时间段/不属于时间段，则赋值为工作时间id
            if (matcher.field === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(matcher.logic)) {
              if (workTime) {
                matcher.content = workTime.value;
              } else {
                matcher = null;
              }
            }
            return matcher;
          })
          .filter(Boolean);
        return config.matchers.length > 0 ? config : null; // 如果匹配条件为空，则不返回该配置
      })
      .filter(Boolean);
  };
  return {
    getDefaultConfig,
  };
}
