import { pluginPaths, pluginBuildDir } from "./dynamic-path-config.mjs";
import { execSync } from "child_process";
let globalBuild = true;

// 单独打包 🌰
// singlePluginVersionBuild("config1/1.0.0");
// singlePluginVersionBuild("identity-verification/1.0.2");
// singlePluginVersionBuild("application-acl/1.0.5");
// singlePluginVersionBuild("ddos-protection/1.0.0");
// singlePluginVersionBuild("cc-protection/1.0.4");
// singlePluginVersionBuild("data-masking/1.0.3");
// singlePluginVersionBuild("identity-verification/1.0.1");
// singlePluginVersionBuild("ratelimit/1.0.8");
// singlePluginVersionBuild("traffic-arrangement/1.0.2");
// singlePluginVersionBuild("waf/1.0.14");
// singlePluginVersionBuild("data-replication-controller/1.0.0");
// singlePluginVersionBuild("weak-password-detection/1.0.2");
// singlePluginVersionBuild("dac/1.0.0");
// singlePluginVersionBuild("web-watermark/1.0.0");
singlePluginVersionBuild("file-watermark/1.0.0");
// singlePluginVersionBuild("bot-protection/1.0.0");
// singlePluginVersionBuild("acl/1.0.2");
if (globalBuild) {
  const allPlugins = pluginPaths();
  dynamicPageBuild(allPlugins);
}

// 单独插件版本打包 📦
function singlePluginVersionBuild(path, version) {
  globalBuild = false;
  const targetPath = pluginBuildDir(path, version);
  runScript(path, targetPath);
}

//  全量插件打包 📦
async function dynamicPageBuild(allPlugins) {
  for await (let path of allPlugins) {
    const targetPath = pluginBuildDir(path);
    runScript(path, targetPath);
  }
}

//  全量插件最新版本打包 📦 todo
function dynamicNewVersionPageBuild(pluginPaths) {
  // todo
}

function runScript(srcPath, targetPath) {
  console.log(`Building ${srcPath} ......${targetPath} `);
  const cmd = `npx cross-env SRC=${srcPath} TARGET=${targetPath} vite build --emptyOutDir=false`;
  execSync(cmd, { stdio: "inherit" });
}
