@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin pct($pct) {
  width: #{$pct};
  position: relative;
  margin: 0 auto;
}

@mixin triangle($width, $height, $color, $direction) {
  $width: $width/2;
  $color-border-style: $height solid $color;
  $transparent-border-style: $width solid transparent;
  height: 0;
  width: 0;

  @if $direction==up {
    border-bottom: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==right {
    border-left: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  } @else if $direction==down {
    border-top: $color-border-style;
    border-left: $transparent-border-style;
    border-right: $transparent-border-style;
  } @else if $direction==left {
    border-right: $color-border-style;
    border-top: $transparent-border-style;
    border-bottom: $transparent-border-style;
  }
}

@mixin flex {
  display: flex;
}

.flex {
  @include flex;
}

@mixin flex_1 {
  flex: 1;
}

.flex_1 {
  @include flex_1;
}

@mixin flex_w {
  @include flex;
  flex-wrap: wrap;
}

.flex_w {
  @include flex_w;
}

@mixin flex_c {
  @include flex;
  align-items: center;
}

.flex_c {
  @include flex_c;
}

.flex_c_c {
  @include flex_c;
  justify-content: center;
}

.flex_c_s {
  @include flex_c;
  justify-content: flex-start;
}

.flex_c_sp_b {
  @include flex_c;
  justify-content: space-between;
}

@mixin flex_c_c {
  @include flex_c_c;
}

// svg图片样式和禁选样式
@mixin svg_icon_common {
  border-radius: 2px;
  border-color: transparent;
  outline: transparent;
}

@mixin svg_icon {
  @include svg_icon_common();
  cursor: pointer;
  color: #666;

  &:hover {
    color: var(--ouryun-color-brand-base);
    background-color: rgba($color: var(--ouryun-color-brand-base), $alpha: 0.2);
  }
}

@mixin svg_icon_disabled {
  @include svg_icon_common();
  cursor: not-allowed;
  color: #999;
}
