// @use "@ouryun/ouryun-plus/dist/theme/src/index.scss" as *;
// @use "./element/index.scss" as *;
@use "./mixin.scss" as *;
@use "./form.scss" as *;

// 去掉默认svg描边
svg g {
  stroke-width: 0;
}

//element 去掉上下箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  -o-appearance: none !important;
  -ms-appearance: none !important;
  appearance: none !important;
  margin: 0;
}

input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  -o-appearance: textfield;
  -ms-appearance: textfield;
  appearance: textfield;
}

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: "思源黑体" !important;
}

html {
  height: 100%;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

/* 修改滚动条的颜色 */
::-webkit-scrollbar {
  width: 6px;
  /* 设置滚动条宽度 */
}

/* 修改滚动条轨道的颜色 */
::-webkit-scrollbar-track {
  background: transparent;
}

/* 修改滚动条滑块的颜色 */
::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 20px;
}

/* 鼠标悬停时滚动条滑块的颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: #bfbfbf;
}

input:-webkit-autofill {
  background: transparent;
  transition: background-color 500000000s ease-in-out 0s;
  -webkit-text-fill-color: unset;
}

//提示图标hover色
.tip_icon_hover {
  color: #999999;

  &:hover {
    color: var(--ouryun-color-brand-base);
  }
}

#plugin-micro-app {
  // 编辑新增页表单样式
  .add_conent {
    width: 85%;
  }
  .add_conent_flex1 {
    width: 85%;
  }
  .add_conent_nopadding {
    padding: 0 !important;
  }
  .add_conent,
  .add_conent_flex1,
  .add_conent_nopadding {
    //标题下方的内容外框
    padding: 0 40px;

    .ouryun-radio-custom {
      margin-right: 40px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .FooterBtn {
    z-index: 500;
    padding-left: 20px;
    margin-top: 60px;
    position: sticky;
    height: 48px;
    line-height: 48px;
    background-color: #fff;
  }

  .ouryun-form-item.is-required:not(.is-no-asterisk).asterisk-left > .ouryun-form-item__label:before,
  .ouryun-form-item.is-required:not(.is-no-asterisk).asterisk-left
    > .ouryun-form-item__label-wrap
    > .ouryun-form-item__label:before {
    margin-right: 0 !important;
  }
}

.box-horizontal-center {
  display: flex;
  align-items: center;
}
