/**
 * 该函数用于获取数据识别列表中的正则
 * @function getPatternParameter
 * @param {array} matchRuleArr - 数据识别列表
 * @return {array}  数据识别的正则
 */

/**
 * 该函数用于获取数据识别列表中的正则
 * @function getPatternParameter
 * @param {array} matchRuleArr - 数据识别列表
 * @return {Object}  数据识别的正则参数
 */
export const getPatternParameter = matchRuleArr => {
  const patternObj = {
    match_patterns: [],
    ignore_rules: [],
  };
  matchRuleArr.forEach(matchRule => {
    if (!matchRule) return;

    (matchRule.rule["identification_rules"] || []).forEach(rule => {
      rule.match_rules.forEach(matchRule => {
        // 识别规则，仅添加 通用 / 流量日志且识别位置包含响应体的正则
        if ([1].includes(rule.scope) || ([2].includes(rule.scope) && rule.region.includes(6))) {
          patternObj.match_patterns.push({
            pattern: matchRule.Content,
            match_type: matchRule.match_method === 1 ? "REGEX" : "KEYWORDS",
            enable: rule.enabled_sign === 1,
          });
        }
      });
    });

    (matchRule.rule["ignore_rules"] || []).forEach(rule => {
      rule.match_rules.forEach(matchRule => {
        // 忽略规则，仅添加 通用 / 流量日志的正则
        if ([1, 2].includes(rule.scope)) {
          patternObj.ignore_rules.push({
            pattern: matchRule.Content,
            match_type: matchRule.match_method === 1 ? "REGEX" : "KEYWORDS",
            enable: rule.enabled_sign === 1,
          });
        }
      });
    });
  });
  return patternObj;
};

/**
 * 该函数用于转换匹配数据参数
 * @function getMatchDataParameter
 * @param {array} matchDataList - 匹配数据列表
 * @param {array} dataRecognitionList - 数据识别列表
 * @return {array}  匹配数据参数
 */
export const getMatchDataParameter = (matchDataList = [], dataRecognitionList = []) => {
  const parameter = [];

  matchDataList.forEach(item => {
    let patternObj = {
      match_patterns: [],
      ignore_rules: [],
    };
    let matchRuleArr = [];
    switch (item.label) {
      case "label":
        // 数据标签
        matchRuleArr = dataRecognitionList.filter(dataRecognitionItem =>
          item.matchContent.some(contentItem => contentItem === dataRecognitionItem.value),
        );
        patternObj = getPatternParameter(matchRuleArr);
        break;
      case "custom":
        // 自定义参数
        patternObj.match_patterns = item.matchContent
          .split(" ")
          .filter(item => item.trim())
          .map(pattern => {
            return {
              pattern,
              match_type: "REGEX",
              enable: true,
            };
          });
        break;
    }

    const parameterItem = {
      is_belong: item.logic === "isBelongTrue",
      ...patternObj,
    };

    if (item.logic === "isBelongTrue") {
      parameterItem.op = item.symbol;
      parameterItem.data_amount = +item.quantity;
    }

    parameter.push(parameterItem);
  });

  return parameter;
};

/**
 * 该函数用于获取匹配数据文本信息
 * @function getMatchDataTextInfo
 * @param {array} matchDataList - 匹配数据列表
 * @param {object} optionsObj - 标签选项、逻辑选项、数据识别选项、符号选项的对象
 * @return {string}  匹配数据文本信息
 */
export const getMatchDataTextInfo = (matchDataList, optionsObj) => {
  const { labelOptions, logicOptions, dataRecognitionList, symbolOptions } = optionsObj;

  const textArr = matchDataList.map(item => {
    const label = labelOptions.find(option => option.value === item.label)?.label;
    const logic = logicOptions.find(option => option.value === item.logic)?.label;

    let matchContent = "";
    switch (item.label) {
      case "label":
        // 数据标签
        matchContent = item.matchContent
          .map(matchContentItem => {
            return dataRecognitionList.find(option => option.value === matchContentItem)?.label;
          })
          .join("、");
        break;
      case "custom":
        // 自定义
        matchContent = item.matchContent;
        break;
    }

    const symbol = symbolOptions.find(option => option.value === item.symbol)?.label;
    const quantity = item.quantity;

    const arr = [label, logic, matchContent];

    if (item.logic === "isBelongTrue") {
      arr.push(symbol, quantity);
    }

    return `[${arr.join("，")}]`;
  });

  const textInfo = textArr.join("，");
  return textInfo;
};
