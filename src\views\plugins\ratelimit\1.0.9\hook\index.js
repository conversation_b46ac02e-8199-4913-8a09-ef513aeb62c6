import { reactive, ref } from "vue";
import { OuModal } from "@ouryun/ouryun-plus";

export default function () {
  const quotaGroupOptions = [
    {
      label: "无",
      value: 0,
      tips: "统计匹配条件的所有请求次数，超过频率设定值则触发防护动作",
    },
    {
      label: "IP",
      value: 1,
      tips: "根据匹配条件统计每个IP的请求次数，超过频率设定值则触发防护动作",
    },
    {
      label: "头部",
      value: 2,
      tips: "根据匹配条件统计头部名称每个值的请求次数，超过频率设定值则触发防护动作",
    },
    {
      label: "账号",
      value: 3,
      tips: "根据匹配条件统计每个账号的请求次数，超过频率设定值则触发防护动作",
    },
  ];

  const durationOptions = [
    {
      label: "秒",
      value: 1,
    },
    {
      label: "分钟",
      value: 60,
    },
    {
      label: "小时",
      value: 3600,
    },
  ];

  const actionOptions = [
    {
      label: "拒绝",
      value: false,
    },
    {
      label: "仅记录",
      value: true,
    },
  ];

  const nextStepOptions = [
    {
      label: "返回",
      value: 0,
      tips: "满足本次匹配条件并触发限流后，即使后续还有规则，也不再走后续的规则",
    },
    {
      label: "下一条规则",
      value: 1,
      tips: "满足本次匹配条件并触发限流后，如果后续还有规则，继续走限流规则",
    },
  ];

  const validateMatch = (rule, value, callback) => {
    if (value.length > 10) {
      const message = "规则条数已超过上限10条，请进行清理后再提交";
      OuModal.success(message);
      return callback(new Error(message));
    }
    if (value.some(item => item.status === "edit")) {
      return callback(new Error("当前正处于编辑状态"));
    } else {
      callback();
    }
  };

  const validateHead = (rule, value, callback) => {
    const regex = /^[a-zA-Z0-9.,!?'"@#$%^&*()\-+=<>{}\[\]\\/;: ]*$/;
    if (regex.test(value)) {
      callback();
    } else {
      return callback(new Error("请输入英文、数字、英文标点符号"));
    }
  };

  const dialogFormRules = {
    ruleName: [{ required: true, trigger: ["change", "blur"], message: "请输入规则名称" }],
    matchingList: [{ required: false, trigger: [], validator: validateMatch }],
    headName: [
      { required: true, trigger: ["change", "blur"], message: "请输入头部名称" },
      { trigger: ["change", "blur"], validator: validateHead },
    ],
    requestQuota: [{ required: true, trigger: ["change", "blur"], message: "" }],
  };

  const valdataorPlugin = (rule, value, callback) => {
    if (!value.length) {
      return callback(new Error("请添加配置规则"));
    }
    callback();
  };

  const form = reactive({
    configRule: [],
  });

  const formRules = {
    configRule: [
      {
        required: true,
        trigger: [],
        validator: valdataorPlugin,
      },
    ],
  };

  class DialogForm {
    ruleName = "";
    quotaGroup = 0;
    nextStep = 0;
    matchingList = [];
    action = false;
    requestQuota = [
      {
        duration: "",
        durationOption: 1,
        limit: "",
      },
    ];
    headName = "";
    isDefault = false;
  }

  const dialogForm = reactive(new DialogForm());
  const matchConditionCollapse = ref(true);

  return {
    quotaGroupOptions,
    durationOptions,
    actionOptions,
    nextStepOptions,
    form,
    formRules,
    dialogFormRules,
    DialogForm,
    dialogForm,
    matchConditionCollapse,
  };
}
