### 🪄 项目介绍

插件项目，支持插件单独打包构建。
技术栈： vite-plugin-federation + vite + vue3

### 使用

```
npm run dev (相当于运行 npm run preBuild && npm run serve）
```

🔔 第一次使用，会全量构建所有插件，Please wait for a moment. 🍭

```
构建产物: npm run preBuild
启动服务: npm run serve
```

修改文件，需要重新构建，运行命令： npm run preBuild
构建文件的调试，需要启动服务，运行命令：npm run serve

#### 构建一个插件 ✨

当然，也可以构建一个或多个所需插件，方法如下 👇👇👇

1. 在`./dynamic-build.js` 文件中，使用 `singlePluginVersionBuild` 方法后，执行`npm run preBuild`；
2. 再执行 `npm run dev`

### 开发体验升级 ✨ ✨ ✨

由于 Vite Dev 模式是 Bundleless 不打包的，需要使用 vite build 生成 RemoteEntry.js 包，可以使用 vite build --watch 到达类似热更新的效果。

```npm
需启动两个服务
热构建bundle： npm run build:watch
启动服务:      npm run serve
```

步骤详细说明 🐾

1. 开启 watch 后，支持边更新边构建 bundle，只需要将`build:watch` 这条命令的执行参数 `RESOURCE` `TARGET` 替换成自己的`插件/版本`

```json
"build:watch": "npx cross-env RESOURCE='identity-verification/1.0.1' TARGET='identity-verification/1.0.1' vite build  --emptyOutDir=false --watch"
```

2. 启动服务调试，运行命令： `npm run serve`

### 发布环节，构建全部插件

```npm
npm run build:prod
```

### 出包环节

1. 出前端插件包，项目中打 tag
2. 出 c++集成包：@对应人，将前端插件集成进去

   > > https://git.ouryun.cn/base/polycube @何彬
   > > https://git.ouryun.cn/base/srhino_plugins @鲁闪闪

3. 出插件集成包：CI 中打以 sp 开头的 tag
4. 出全量包：CI 中打以 v 开头的 tag

### 共享总控方法和状态列表

| 名称                   | 本项目中使用                                   | 总控位置                        |
| ---------------------- | ---------------------------------------------- | ------------------------------- |
| vue                    | `import {...} from vue;`                       | 总控全局安装的 vue              |
| vue-router             | ` import { useRouter } from "vue-router";`     | 总控全局安装的 vue-router       |
| @ouryun/ouryun-plus    | `import  {...} from “@ouryun/ouryun-plus";`    | 总控全局安装的组件库            |
| @ouryun-plus/icons-vue | `import  {...} from “@ouryun-plus/icons-vue";` | 总控全局安装的组件库            |
| request                | `import request from "baseRequest";`           | `./src/utils/request/index.js`  |
| 插件 store             | `import usePlugin from "basePluginStore";`     | `./src/store/modules/plugin.js` |

### 当前插件和版本

| 插件名称          | name/dir                | 当前版本 |
| ----------------- | ----------------------- | -------- |
| 认证鉴权          | identity-verification   | 1.0.2    |
| 四层 acl          | acl                     | 1.0.2    |
| 基础 Web 攻击防护 | waf                     | 1.0.12   |
| 弱密码检测        | weak-password-detection | 1.0.2    |
| 应用级 ACL        | application-acl         | 1.0.5    |
| 数据脱敏          | data-masking            | 1.0.3    |
| 请求频率控制      | ratelimit               | 1.0.8    |
| CC 攻击防护       | cc-protection           | 1.0.4    |

### 接口权限配置问题

### 🔗 相关文档

[vite-plugin-federation](https://github.com/originjs/vite-plugin-federation)

[前端插件打包动态加载设计方案](https://doc.weixin.qq.com/doc/w3_AT0AdwamABkCpek0rxESemLkszvcd?scode=ABwA9Qd2ABE5pQfV0EAT0AdwamABk)
