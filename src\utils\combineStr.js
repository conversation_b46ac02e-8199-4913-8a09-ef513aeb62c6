/**
 * a组合b = AB
 * @param {Array} arr1
 * @param {Array} arr2
 * @returns
 */
export function combinePart(arr1, arr2) {
  const result = [];
  if (!arr1.length) {
    return arr2.slice();
  }
  if (!arr2.length) {
    return arr1.slice();
  }
  for (let i = 0; i < arr1.length; i++) {
    for (let j = 0; j < arr2.length; j++) {
      result.push(arr1[i] + arr2[j]);
    }
  }
  return result;
}

/**
 * c左右组合AB
 * @param {Array} arr1
 * @param {Array} arr2
 * @returns
 */
export function combinePartDate(arr1, arr2) {
  const result = [];
  if (!arr1.length) {
    return arr2.slice();
  }
  for (let i = 0; i < arr1.length; i++) {
    for (let j = 0; j < arr2.length; j++) {
      result.push(arr1[i] + arr2[j]);
      result.push(arr2[j] + arr1[i]);
    }
  }
  return result;
}

/**
 * first、second、date
 * @param {Object} data
 * @returns
 */
export function accPartDate(data) {
  const { first, second, date } = data;
  const part = combinePart(first, second);
  const partDate = combinePartDate(date, part);
  const all = [...partDate, ...part, ...first, ...second, ...date];
  const result = [...new Set(all)];
  return {
    el: result,
    total: result.length,
  };
}
