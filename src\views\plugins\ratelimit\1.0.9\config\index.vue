<template>
  <!--  RL创建配置 -->
  <div class="plugin-rateLimit-creat-configuration add_conent_flex1">
    <ou-form ref="formRef" :model="form" :rules="formRules" label-width="140px" class="-mb-20">
      <ou-form-item label="配置规则：" prop="configRule">
        <div class="config-rule-container">
          <div class="btn-row">
            <ou-button @click="addDialogOpen" type="primary" text>添加</ou-button>
            <ou-button @click="batchesDelClick" type="primary" text :disabled="!selectionsList.length">
              批量删除
            </ou-button>
            <moveGroup
              v-model:list="form.configRule"
              :selections="selectionsList"
              @moved="routeMoved"
              :disabled="!selectionsList.length"
              style="margin-left: 8px"></moveGroup>
          </div>
          <ou-table
            ref="tableRef"
            class="editor"
            :data="form.configRule"
            :height="!form.configRule.length ? 200 : form.configRule.length >= 5 ? 240 : 'auto'"
            style="width: 100%; --table-empty-size: 120px"
            @select="value => handleSelectionChange(value)"
            @select-all="value => handleSelectionChange(value)">
            <ou-table-column type="selection" width="32" align="center" :selectable="row => !row.isDefault" />
            <ou-table-column type="index" label="优先级" width="58" align="center" />
            <ou-table-column prop="ruleName" label="规则名称">
              <template #default="scope">
                <ou-text-ellipsis :content="String(scope.row.ruleName)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="matchedConditionInfo" label="匹配条件">
              <template #default="scope">
                <ou-text-ellipsis :content="getTextInfo(scope.row.matchingList)"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="quotaGroup" label="配额分组" width="100">
              <template #default="scope">
                {{ getOptionsLabel(scope.row.quotaGroup, quotaGroupOptions) }}
              </template>
            </ou-table-column>
            <ou-table-column label="请求配额">
              <template #default="scope">
                <ou-text-ellipsis :content="getRequestQuotaText(scope.row.requestQuota) || '-'"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="action" label="动作" width="80">
              <template #default="scope">
                <ou-text-ellipsis :content="getOptionsLabel(scope.row.action, actionOptions) || '-'"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="nextStep" label="下一步" width="106">
              <template #default="scope">
                <ou-text-ellipsis
                  :content="getOptionsLabel(scope.row.nextStep, nextStepOptions) || '-'"></ou-text-ellipsis>
              </template>
            </ou-table-column>
            <ou-table-column prop="enable" label="状态" width="106">
              <template #default="scope">
                <ou-switch
                  v-model="scope.row.enable"
                  active-text="启用"
                  inactive-text="禁用"
                  class="switch-offside"
                  size="small"></ou-switch>
              </template>
            </ou-table-column>
            <ou-table-column prop="address" label="操作" width="170" fixed="right">
              <template #default="scope">
                <ou-button @click="updateClick(scope.$index, scope.row)" type="primary" text>编辑</ou-button>
                <ou-button @click="copyClick(scope.$index, scope.row)" type="primary" text>复制</ou-button>
                <ou-button @click="topPostClick(scope.$index, scope.row)" type="primary" text>置顶</ou-button>
                <ou-button v-if="!scope.row.isDefault" @click="delClick(scope.$index)" type="primary" text>
                  删除
                </ou-button>
              </template>
            </ou-table-column>
          </ou-table>
        </div>
      </ou-form-item>
    </ou-form>

    <ou-opera-dialog
      :title="`${editType}配置规则`"
      v-model="dialogShow"
      @close="dialogClose"
      @cancel="dialogClose"
      @confirm="dialogConfirm"
      :close-on-click-modal="false"
      width="1180px"
      draggable>
      <template #content>
        <ou-form ref="dialogFormRef" :rules="dialogFormRules" :model="dialogForm" label-width="120px" class="-mb-20">
          <ou-form-item label="规则名称：" prop="ruleName">
            <ou-input
              v-model.trim="dialogForm.ruleName"
              placeholder="请输入"
              maxlength="50"
              show-word-limit
              :disabled="dialogForm.isDefault"
              style="width: 300px"></ou-input>
          </ou-form-item>
          <!-- <ou-form-item label="匹配条件：" prop="matchingList">
            <matchedConditionEditor
              v-if="dialogShow"
              ref="matchedConditionEditorRef"
              v-model="dialogForm.matchingList"
              :required="false"
              generalPurposeLibrary
              style="width: 100%"></matchedConditionEditor>
          </ou-form-item> -->
          <ou-form-item label="匹配条件：" prop="matchingList">
            <ou-collapse-panel class="matchedConditionEditorCollapse" v-model:isCollapse="matchConditionCollapse">
              <template #headerLeft>
                <span class="header-hint">多个条件同时满足才执行对应动作</span>
              </template>
              <template #content>
                <matchedConditionEditor
                  v-if="dialogShow"
                  ref="matchedConditionEditorRef"
                  v-model="dialogForm.matchingList"
                  :required="false"
                  :fieldOptions="[
                    'srcIp',
                    'path',
                    'requestHeader',
                    'requestMethod',
                    'queryParameter',
                    'userName',
                    'time',
                  ]"
                  style="width: 100%"></matchedConditionEditor>
              </template>
            </ou-collapse-panel>
          </ou-form-item>
          <ou-form-item label="配额分组：" class="line-height-22">
            <ou-radio-group v-model="dialogForm.quotaGroup">
              <ou-radio v-for="item in quotaGroupOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </ou-radio>
            </ou-radio-group>
            <popoverTips :data="quotaGroupOptions" style="margin-left: 40px"></popoverTips>
          </ou-form-item>
          <ou-form-item v-if="dialogForm.quotaGroup === 2" label="头部名称：" prop="headName">
            <ou-input
              v-model.trim="dialogForm.headName"
              placeholder="请输入英文、数字、英文标点符号"
              style="width: 300px"></ou-input>
          </ou-form-item>
          <ou-form-item label="请求配额：" class="request-form-item" prop="requestQuota">
            <requestQuotaEditor
              v-if="dialogShow"
              ref="requestQuotaEditorRef"
              v-model="dialogForm.requestQuota"></requestQuotaEditor>
          </ou-form-item>
          <ou-form-item label="动作：" prop="action" class="line-height-22">
            <ou-radio-group v-model="dialogForm.action">
              <ou-radio v-for="item in actionOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </ou-radio>
            </ou-radio-group>
          </ou-form-item>
          <ou-form-item label="下一步：" prop="nextStep" class="line-height-22">
            <ou-radio-group v-model="dialogForm.nextStep">
              <ou-radio v-for="item in nextStepOptions" :key="item.value" :value="item.value" class="popover-radio">
                <span>{{ item.label }}</span>
              </ou-radio>
            </ou-radio-group>
            <popoverTips :data="nextStepOptions" style="margin-left: 40px"></popoverTips>
          </ou-form-item>
        </ou-form>
      </template>
      <template v-slot:footer>
        <ou-button @click="dialogClose">取消</ou-button>
        <ou-button @click="dialogConfirm" type="primary">确定</ou-button>
      </template>
    </ou-opera-dialog>
  </div>
</template>
<script setup>
  import { ref, defineExpose, nextTick } from "vue";
  import moveGroup from "@/components/MoveGroup/index.vue";
  import matchedConditionEditor from "@/components/NewMatchedConditionEditor/index.vue";
  import popoverTips from "@/components/PopoverTips/index.vue";
  import requestQuotaEditor from "../components/requestQuotaEditor.vue";
  import { OuModal } from "@ouryun/ouryun-plus";
  import { getTextInfo, getMatchParameter, compatibleMatchingList } from "@/utils/MatchedCondition.js";
  import hookStates from "../hook/index.js";
  import { defaultConfig } from "../hook/config-default.js";

  import usePlugin from "basePluginStore";
  const store = usePlugin();

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
  });

  const {
    quotaGroupOptions,
    durationOptions,
    actionOptions,
    nextStepOptions,
    form,
    formRules,
    dialogFormRules,
    DialogForm,
    dialogForm,
    matchConditionCollapse,
  } = hookStates();

  let selectionsList = ref([]);

  const onCreated = () => {
    if (props.content || props.global) {
      initForm();
    } else {
      // 使用【内置策略】配置
      form.configRule = JSON.parse(JSON.stringify(defaultConfig));
    }
  };

  const initForm = () => {
    const content = JSON.parse(props.content || props.global);
    form.configRule = content.plugin_config.rules.map((item, index) => {
      return {
        ruleName: item.rule_name,
        matchingList: compatibleMatchingList(content.meta_data[index].matchingList),

        action: item.action.dryrun,
        headName: item.action.header || "",
        nextStep: item.action.after_pass,

        requestQuota: content.meta_data[index].requestQuota,
        quotaGroup: item.action.target,
        enable: item.enable,
        isDefault: item.isDefault,
      };
    });
  };
  const handleSelectionChange = value => {
    selectionsList.value = value;
  };

  const dialogShow = ref(false);

  let activeTableIndex = null;
  let isCopyDefault = false; // 是否复制默认规则
  const editType = ref("添加");
  const addDialogOpen = () => {
    editType.value = "添加";
    dialogShow.value = true;
  };

  const requestQuotaEditorRef = ref(null);
  const dialogClose = () => {
    dialogShow.value = false;
    isCopyDefault = false;

    setTimeout(() => {
      dialogFormRef.value.resetFields();
      Object.assign(dialogForm, new DialogForm());
    }, 500);
  };

  const formRef = ref(null);
  const dialogFormRef = ref(null);
  const matchedConditionEditorRef = ref(null);

  const dialogConfirm = () => {
    const matchedConditionValid = matchedConditionEditorRef.value.validator();
    const requestQuotaValid = requestQuotaEditorRef.value.validate();
    dialogFormRef.value.validate(dialogFormValid => {
      if ([dialogFormValid, matchedConditionValid, requestQuotaValid].every(validItem => validItem)) {
        const formCopy = JSON.parse(JSON.stringify(dialogForm));

        if (["添加"].includes(editType.value)) {
          formCopy.enable = true;
        } else {
          // 编辑或复制时，保持原有状态
          // 复制默认规则后，允许修改，状态保持启用
          formCopy.enable = isCopyDefault ? true : form.configRule[activeTableIndex].enable || false;
        }

        if (["添加", "复制"].includes(editType.value)) {
          if (form.configRule.some(item => item.ruleName === formCopy.ruleName)) {
            return OuModal.warning(`规则名称${formCopy.ruleName}已存在`);
          }
          form.configRule.unshift(formCopy);
        } else {
          if (
            form.configRule.some(item => item.ruleName === formCopy.ruleName) &&
            form.configRule[activeTableIndex].ruleName !== formCopy.ruleName
          ) {
            return OuModal.warning(`规则名称${formCopy.ruleName}已存在`);
          }
          form.configRule[activeTableIndex] = formCopy;
        }
        dialogClose();
        formRef.value.validateField("configRule");
      }
    });
  };

  const updateClick = (tableIndex, row) => {
    editType.value = "编辑";
    activeTableIndex = tableIndex;
    for (let k in dialogForm) {
      if (dialogForm.hasOwnProperty(k)) {
        dialogForm[k] = JSON.parse(JSON.stringify(row[k]));
      }
    }
    dialogShow.value = true;
  };

  const copyClick = (tableIndex, row) => {
    editType.value = "复制";
    activeTableIndex = tableIndex;
    for (let k in dialogForm) {
      if (["ruleName"].includes(k)) {
        dialogForm[k] = row[k] + "_复制";
      } else {
        if (dialogForm.hasOwnProperty(k)) {
          dialogForm[k] = row[k];
        }
      }
    }
    // 复制默认规则后的规则，允许修改，状态保持启用
    if (row.isDefault) {
      dialogForm.isDefault = false;
      isCopyDefault = true;
    }
    dialogShow.value = true;
  };
  const topPostClick = (tableIndex, row) => {
    form.configRule.splice(tableIndex, 1);
    form.configRule.unshift(row);
  };
  const delClick = tableIndex => {
    // 若seconds中存在删除项，则删除记录值
    const include = selectionsList.value.findIndex(item => item === form.configRule[tableIndex]);
    if (include !== -1) selectionsList.value.splice(include, 1);

    // 删除列表项
    form.configRule.splice(tableIndex, 1);
    formRef.value.validateField("configRule");
  };

  const batchesDelClick = () => {
    if (selectionsList.value.length) {
      const content = {
        content: `提示`,
        moreContent: `确定要删除${selectionsList.value.length}条规则吗？`,
      };
      OuModal.confirm(content)
        .then(() => {
          // 删除列表项
          form.configRule = form.configRule.filter(item => !selectionsList.value.includes(item));
          // 清空selections记录值
          selectionsList.value.splice(0, selectionsList.value.length);
          formRef.value.validateField("configRule");
        })
        .catch(() => {});
    }
  };

  const tableRef = ref(null);
  const routeMoved = () => {
    // 移动后重新设置选中
    nextTick(() => {
      selectionsList.value.forEach(row => {
        tableRef.value.toggleRowSelection(row);
      });
    });
  };

  const getOptionsLabel = (value, options) => {
    return options.find(item => item.value === value)?.label || "";
  };

  const getRequestQuotaText = requestQuota => {
    const requestQuotaTextList = requestQuota.map(item => {
      return `${item.limit}次/${item.duration}${getOptionsLabel(item.durationOption, durationOptions)}`;
    });
    return requestQuotaTextList.join("，");
  };

  const submit = async () => {
    return await Promise.all([formRef.value.validate(valid => valid)]).then(res => {
      if (res.every(item => item)) {
        const params = {
          plugin_config: {
            rules: [],
          },
          meta_data: [],
        };

        form.configRule.forEach((item, index) => {
          const action = {};

          action.quotas = item.requestQuota.map(item => {
            return {
              duration: Number(item.duration) * item.durationOption,
              max_count: Number(item.limit),
            };
          });

          action.target = item.quotaGroup;

          if (action.target === 2) action.header = item.headName;

          action.dryrun = item.action;
          action.after_pass = item.nextStep;

          params.plugin_config.rules.push({
            rule_name: item.ruleName,
            enable: item.enable,
            isDefault: item.isDefault,
            action,
            matchers: getMatchParameter(item.matchingList).match,
          });
          params.meta_data.push({
            matchingList: item.matchingList,
            requestQuota: item.requestQuota,
          });
        });

        const extension = { refs: { ip_address_database: [], time_range_database: [] } };
        const ipAddressArr = [];
        const timeRangeArr = [];

        params.meta_data.forEach(item => {
          item.matchingList.forEach(matchItem => {
            if (matchItem.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic)) {
              ipAddressArr.push(...matchItem.content);
            } else if (
              matchItem.field === "time" &&
              ["belongToTimeRange", "notBelongToTimeRange"].includes(matchItem.logic)
            ) {
              timeRangeArr.push(matchItem.content);
            }
          });
        });

        const configs = [
          { key: "ip_address_database", arr: ipAddressArr },
          { key: "time_range_database", arr: timeRangeArr },
        ];

        configs.forEach(({ key, arr }) => {
          extension.refs[key] = [...new Set(arr)].map(item => ({ item_id: item }));
        });

        return {
          contentParams: params,
          extension,
        };
      } else {
        throw new Error("表单校验失败");
      }
    });
  };

  onCreated();

  defineExpose({
    submit,
  });
</script>

<style lang="scss" scoped>
  .plugin-rateLimit-creat-configuration {
    .dialog_tip_icon_hover {
      margin-left: 8px;
      color: #999999;
      cursor: pointer;
      &:hover {
        color: var(--ouryun-color-brand-base);
      }
    }
    .ouryun-form-item-label-group {
      display: flex;
      align-items: center;
      column-gap: 4px;
      position: relative;
    }
    .config-rule-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      .btn-row {
        height: 32px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
      }
    }

    .ouryun-radio-group {
      display: flex;
      align-items: center;
      .ouryun-radio {
        width: 120px;
        margin-right: 0;
        &.popover-radio {
          ::v-deep .ouryun-radio__label {
            display: flex;
            align-items: center;
            column-gap: 4px;
          }
        }
      }
    }
  }

  .ouryun-form.ouryun-form--default > .ouryun-form-item-custom {
    margin-bottom: 0px !important;
  }
</style>
