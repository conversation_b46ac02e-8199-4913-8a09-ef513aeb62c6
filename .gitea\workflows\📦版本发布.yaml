name: 📦发布流水线
on:
  push:
    tags:
      - "*"

jobs:
  release:
    runs-on: debian-node20
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.ref }}

      - name: 获取触发分支
        id: br
        run: |
          set -x
          # 确保 tag 和分支最新
          git fetch --prune --tags
          git fetch --prune origin +refs/heads/*:refs/remotes/origin/*

          # 获取触发 tag 指向的分支（远程分支）
          branch=$(git branch -r --points-at "${{ github.sha }}" \
            | grep -v 'origin/HEAD' \
            | head -n1 \
            | sed 's#origin/##')
          # 获取 tag 的创建时间
          tag_time=$(TZ=Asia/Shanghai date -d "${{ gitea.event.head_commit.timestamp }}" "+%F %T")
          echo "trigger_branch=${branch}" >> $GITHUB_OUTPUT
          echo "trigger_at=${tag_time}" >> $GITHUB_OUTPUT
      - name: 拉取依赖
        run: pnpm install

      - name: 执行构建
        id: build
        run: |

          echo "start_ts=$(date +%s)" >> $GITHUB_OUTPUT
          TAG_NAME="${{ github.ref_name }}"
          echo "当前 Tag: $TAG_NAME" 
          echo "🚀 执行 make release"
          make release
          echo "构建状态: ${{ job.status }}"

      - name: 生成并发送 CHANGELOG
        id: changelog
        run: |
          chmod +x ci_scripts/generate-changelog.sh
          TAG_NAME="${{ github.ref_name }}"
          BRANCH_BY="${{ steps.br.outputs.trigger_branch }}"
          COMPONENT_VERSION="${{ steps.version.outputs.version }}"

          END_TS=$(date +%s)
          START_TS=${{ steps.build.outputs.start_ts }}
          DURATION_SEC=$((END_TS - START_TS))

          # 格式化运行时长为 min:sec
          DURATION_FMT=$(printf '%d分%02d秒' $((DURATION_SEC / 60)) $((DURATION_SEC % 60)))

          CHANGELOG=$(timeout 25s ci_scripts/generate-changelog.sh \
            "$TAG_NAME" \
            "$BRANCH_BY" \
            "$COMPONENT_VERSION" \
            "$DURATION_FMT" || echo "::warning::CHANGELOG生成超时")

          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: 上传产物
        uses: actions/release-action@main
        with:
          api_key: '${{ secrets.GITEA_TOKEN }}'
          body: |
            <!-- {"notify":"frontend1"} -->
            # plugin-ui 组件包
            ${{ steps.changelog.outputs.changelog }}
          files: |-
            sc-plugin/*.tar.gz
            
      - name: 失败发送通知
        if: failure() || cancelled()  # 仅在失败或取消时触发
        run: |
          apt-get update && apt-get install -y --no-install-recommends curl # 确保安装curl等工具
          WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=af9796a4-fa90-415d-9f95-1b44f6078d0d"
          MESSAGE=$(cat <<EOF
          {
            "msgtype": "markdown",
            "markdown": {
              "content": "❌ **${{ github.workflow }} 执行失败**\n
              - **仓库**: ${{ github.repository }}\n
              - **分支**: ${{ github.ref }}\n
              - **触发者**: ${{ github.actor }}\n
              - **状态**: ${{ job.status }}\n"
            }
          }
          EOF
          )
          curl --max-time 15 -s -X POST -H "Content-Type: application/json" -d "$MESSAGE" "$WEBHOOK_URL"