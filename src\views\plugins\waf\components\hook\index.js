import ruleList from "@/assets/data/rule.json";

const hasDuplicateKey = (objArray, key) => {
  const valueCount = new Map();
  for (const obj of objArray) {
    const value = obj[key];
    if (valueCount.has(value)) {
      return obj[key];
    } else {
      valueCount.set(value, obj);
    }
  }
  return false;
};

const rule = ruleList.filter((item, index, self) => index === self.findIndex(t => t.TypeId === item.TypeId));

const configWhite = list => {
  if (!list.length) return [];
  const data = list.map(item => {
    const list = item.source_ip.split("\n");
    const ipRangeList = [];
    const defaultList = [];
    list.forEach(e => {
      if (e.includes("-")) {
        const rangeList = e.split("-");
        ipRangeList.push({
          start_ip: rangeList[0],
          end_ip: rangeList[1],
        });
      }
      if (e.includes("/")) {
        const rangeList = e.split("/");
        defaultList.push({
          address_prefix: rangeList[0],
          prefix_len: rangeList[1],
        });
      }
      if (!e.includes("/") && !e.includes("-")) {
        defaultList.push({
          address_prefix: e || "0.0.0.0",
          prefix_len: e ? 32 : 0,
        });
      }
    });
    return {
      url_path: item.path,
      enable: item.status,
      name: "",
      ip_list: {
        list: defaultList,
      },
      ip_range_list: ipRangeList,
      white_rule: {
        type_ids: item.rule_type && Array.isArray(item.rule_type) ? item.rule_type.map(e => Number(e)) : [],
        rule_ids: item.rule_id ? item.rule_id.split(",") : [],
      },
    };
  });
  return data;
};

export const useState = () => {
  return {
    rule,
    configWhite,
    hasDuplicateKey,
  };
};
