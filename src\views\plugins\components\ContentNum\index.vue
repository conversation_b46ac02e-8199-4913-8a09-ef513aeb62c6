<template>
  <div class="container">
    <div class="content-num">
      <span>已配置</span>
      <span>{{ total }}</span>
      <span>/{{ max }}条</span>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {};
    },
    props: {
      total: {
        type: Number,
        default: 0,
      },
      max: {
        type: Number,
        default: 3000,
      },
    },
    created() {},
    mounted() {},
    methods: {},
  };
</script>

<style scoped lang="scss">
  .container {
    width: 100%;
  }
  .content-num {
    height: 32px;
    padding: 0px 10px;
    border: 1px solid #d9d9d9;
    color: #999999;
    display: flex;
    align-items: center;
    border-top: none;
  }
</style>
