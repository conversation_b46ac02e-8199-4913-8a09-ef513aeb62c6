<template>
  <div class="pn-white-list">
    <div class="pn-table-handle">
      <ou-button text type="primary" @click="add">添加</ou-button>
      <ou-button text type="primary" @click="importData">导入</ou-button>
      <ou-button text type="primary" @click="batchDelete" :disabled="!multipleList.length">批量删除</ou-button>
    </div>
    <ou-table
      ref="multipleTableRef"
      style="width: 100%; --table-empty-size: 120px"
      :data="tableData"
      @selection-change="handleSelectionChange">
      <ou-table-column type="selection" width="32" />
      <ou-table-column type="index" label="序号" width="44" align="center" />
      <ou-table-column property="source_ip" label="源IP">
        <template #default="scope">
          <ou-text-ellipsis :content="scope.row.source_ip || '-'"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column property="path" label="路径">
        <template #default="scope">
          <ou-text-ellipsis :content="scope.row.path || '-'"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column property="rule_type" label="规则ID/类型">
        <template #default="scope">
          <ou-text-ellipsis
            :content="
              scope.row.type === 'rule_type' ? mapRule(scope.row.rule_type) : scope.row.rule_id
            "></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="状态" width="120">
        <template #default="scope">
          <ou-switch
            v-model="scope.row.status"
            size="small"
            active-text="启用"
            inactive-text="禁用"
            class="switch-offside" />
        </template>
      </ou-table-column>
      <ou-table-column fixed="right" label="操作" width="100">
        <template #default="scope">
          <ou-button text type="primary" @click="edit(scope.row, scope.$index)">编辑</ou-button>
          <ou-button text type="primary" @click="deleteItem(scope.row, scope.$index)">删除</ou-button>
        </template>
      </ou-table-column>
    </ou-table>
    <div class="table-pagination" v-if="whiteList.length > 10">
      <ou-pagination
        :background="true"
        layout="total, sizes,prev, pager, next, jumper"
        :total="whiteList.length"
        size="small"
        v-model:page="queryParams.currentPage"
        v-model:limit="queryParams.PageSize"
        @pagination="paginationChange"></ou-pagination>
    </div>
    <ou-opera-dialog
      :title="dialogConfig.title[dialogConfig.enums]"
      @close="closeWhiteDialog"
      :hideFooter="true"
      v-model="dialogConfig.isShow"
      draggable
      :width="'550px'">
      <template #content>
        <div class="import_result" v-if="step === 'import_result'">
          <div v-if="result.state === 1">
            <ou-icon class="icon" color="#62BF78"><SuccessFilled /></ou-icon>
            <div class="tip">导入成功</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <div v-else-if="result.state === 2">
            <ou-icon class="icon" color="#ffc53d"><WarningFilled /></ou-icon>
            <div class="tip">部分导入失败</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <div v-else>
            <ou-icon class="icon" color="#ff0b0b"><CircleCloseFilled /></ou-icon>
            <div class="tip">导入失败</div>
            <div class="desc">{{ result.text }}</div>
          </div>
        </div>
        <ou-form v-else ref="baseFormRef" :model="baseForm" :rules="baseRules" label-width="80px">
          <div v-if="dialogConfig.enums !== 'import'" style="margin-right: 24px">
            <ou-form-item label="源IP：" prop="sourceIp">
              <ou-input v-model="baseForm.sourceIp" placeholder="多个用英文逗号隔开" />
              <IpTip style="position: absolute; left: 373px; font-size: 16px"></IpTip>
            </ou-form-item>
            <ou-form-item label="路径：" prop="path">
              <ou-input v-model="baseForm.path" placeholder="请输入英文、数字、英文标点符号，精准匹配" />
            </ou-form-item>
            <ou-form-item
              label="规则类型："
              prop="ruleType"
              v-if="baseForm.type === 'rule_type'"
              style="margin-bottom: 0px">
              <ou-tree-select
                :safeTagWidth="200"
                v-model="baseForm.ruleType"
                :data="ruleSelectList"
                multiple
                filterable
                node-key="id"
                :props="{
                  label: 'label',
                }"
                :render-after-expand="false"
                :show-checkbox="true"
                style="width: 100%; overflow: hidden"
                check-strictly
                check-on-click-node></ou-tree-select>
            </ou-form-item>
            <ou-form-item label="规则ID：" prop="ruleId" v-if="baseForm.type === 'rule_id'">
              <ou-input v-model="baseForm.ruleId" placeholder="多个规则ID请用英文逗号隔开" />
            </ou-form-item>
          </div>
          <ou-form-item v-else style="margin-bottom: 0px">
            <div v-if="step === 'importing'" style="width: 100%">
              <div class="content-tip">请先下载导入模板，并严格按模板要求填写信息后导入，否则信息可能导入失败；</div>
              <div>
                <ou-button text type="primary" @click="downTemplate">下载Excel模板</ou-button>
              </div>
              <ou-drag-upload
                @getData="getUploadData"
                @on-change="fileChange"
                fileType=".xlsx, .xls"
                tip="仅限上传 xlsx、xls的文件， 最大1M"></ou-drag-upload>
            </div>
          </ou-form-item>
        </ou-form>
      </template>
      <template #footer>
        <div v-if="step !== 'import_result'">
          <ou-button @click="dialogConfig.isShow = false">取消</ou-button>
          <ou-button type="primary" @click="confirmWhiteList">确定</ou-button>
        </div>
        <div v-else>
          <ou-button v-if="result.icon !== 'error'" @click="importSuccess">完成</ou-button>
          <ou-button v-if="result.icon === 'error'" @click="dialogConfig.isShow = false">结束</ou-button>
          <ou-button type="primary" v-if="result.icon !== 'warning'" @click="step = 'importing'">继续导入</ou-button>
          <ou-button type="primary" v-if="result.icon == 'warning'" @click="downFail">下载失败数据</ou-button>
        </div>
      </template>
    </ou-opera-dialog>
  </div>
</template>

<script setup>
  import { defineProps, defineEmits, defineExpose, ref, reactive, computed, watch } from "vue";
  import IpTip from "@/components/IpTip";
  import { useState } from "../hook";
  import { OuModal, dayjs } from "@ouryun/ouryun-plus";
  import { regex } from "@ouryun/ouryun-plus";
  import { exportExcelFile } from "@/utils/readfile.js";
  import { uploadExcel } from "@/api/pgModule.js";
  import { validateForm } from "@/utils/tool.js";
  import { areArraysEqual } from "@/utils/index.js";
  import { validateIpAll } from "@/utils/validate.js";

  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  });
  const emits = defineEmits(["update:list"]);
  const { rule, configWhite } = useState();

  const validateIp = (rule, value, callback) => {
    if (value) {
      const ipList = value.split(",");
      const newArr = ipList.filter(item => item.trim() !== "");
      for (let index = 0; index < newArr.length; index++) {
        const e = newArr[index];
        if (!validateIpAll(e)) {
          callback(new Error(`第${index + 1}个IP不合法：${e}`));
        }
      }
      callback();
    }
    callback();
  };
  const validatePath = (rule, value, callback) => {
    if (value) {
      if (!regex.NO_HZ.test(value)) {
        callback(new Error("请输入英文、数字、英文标点符号"));
      }
      callback();
    }
    callback();
  };
  const queryParams = ref({
    currentPage: 1,
    PageSize: 10,
  });
  const ruleList = rule.reduce((acc, cur) => {
    acc[cur.TypeId] = cur.TypeName;
    return acc;
  }, {});
  const ruleSelectList = rule.map(item => ({
    label: item.TypeName,
    id: item.TypeId,
    value: item.TypeId,
  }));
  const result = ref({
    icon: "",
    title: "",
    text: "",
  });
  const baseForm = ref({
    sourceIp: "",
    path: "",
    ruleType: [],
    status: true,
    type: "rule_type",
    ruleId: "",
  });
  const step = ref("importing");
  const multipleList = ref([]);
  const whiteList = ref([]);
  const handleIndex = ref(0);
  const tableData = computed(() => {
    const currentPage = queryParams.value.currentPage;
    const pageSize = queryParams.value.PageSize;
    return whiteList.value.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  });
  const baseFormRef = ref(null);
  const baseRules = reactive({
    sourceIp: [{ validator: validateIp, trigger: "change" }],
    path: [{ validator: validatePath, trigger: "change" }],
  });
  const mapRule = rule => {
    if (!rule || !rule.length) return "-";
    return rule
      .reduce((acc, cur) => {
        acc.push(ruleList[cur]);
        return acc;
      }, [])
      .join(",");
  };
  const successList = ref([]);
  const failList = ref([]);
  const isRuleMultiple = ref(true);
  const importFileData = ref(null);
  const dialogConfig = ref({
    isShow: false,
    enums: "",
    title: {
      add: "添加",
      edit: "编辑",
      import: "导入",
    },
  });
  watch(
    () => whiteList.value,
    (newVal, oldVal) => {
      emits("update:list", newVal);
    },
    {
      deep: true,
    },
  );
  watch(
    () => dialogConfig.value.isShow,
    (newVal, oldVal) => {
      if (!newVal) {
        step.value = "importing";
        initForm();
        successList.value = [];
        failList.value = [];
      }
      importFileData.value = null;
    },
  );
  const initForm = () => {
    baseFormRef.value?.resetFields();
    baseForm.value = {
      sourceIp: "",
      path: "",
      ruleType: [],
      status: true,
      type: "rule_type",
      ruleId: "",
    };
  };
  const handleSelectionChange = val => {
    multipleList.value = val;
  };
  const batchDelete = () => {
    multipleList.value.forEach(e => {
      deleteItem(e);
    });
  };
  const importData = () => {
    showDialog("import");
  };
  const add = () => {
    showDialog("add");
  };
  const edit = (row, index) => {
    handleIndex.value = index + (queryParams.value.currentPage - 1) * queryParams.value.PageSize;
    const { source_ip, rule_type, rule_id, type, path, status } = row;
    baseForm.value = {
      sourceIp: source_ip,
      path,
      status,
      ruleType: rule_type,
      type,
      ruleId: rule_id,
    };
    showDialog("edit");
  };
  const deleteItem = (row, index) => {
    handleIndex.value = index + (queryParams.value.currentPage - 1) * queryParams.value.PageSize;
    whiteList.value.splice(handleIndex.value, 1);
  };
  const showDialog = type => {
    dialogConfig.value.enums = type;
    dialogConfig.value.isShow = true;
  };
  const closeWhiteDialog = () => {};
  //白名单多项查重
  const isWhiteExist = (item, list) => {
    if (!list.length) return false;
    const { path, ruleId, sourceIp, ruleType } = item;
    return list.some(
      e => e.path === path && e.source_ip === sourceIp && e.rule_id === ruleId && areArraysEqual(e.rule_type, ruleType),
    );
  };
  const handleWhite = async () => {
    const { sourceIp, path, ruleType, ruleId, status, type } = baseForm.value;
    const isExist = isWhiteExist(baseForm.value, whiteList.value);
    if (sourceIp || path || ruleType.length || ruleId) {
      const isValid = await validateForm(baseFormRef.value);
      if (!isValid) return;
      if (isExist) {
        OuModal.warning("白名单已存在");
        return;
      }
      switch (dialogConfig.value.enums) {
        case "add":
          whiteList.value.unshift({
            source_ip: sourceIp,
            rule_type: ruleType,
            rule_id: "",
            path,
            status: true,
            type: "rule_type",
          });
          queryParams.value.currentPage = 1;
          break;
        case "edit":
          whiteList.value.splice(handleIndex.value, 1, {
            source_ip: sourceIp,
            rule_type: ruleType,
            rule_id: ruleId,
            path,
            status,
            type,
          });
          break;
        default:
          break;
      }
      dialogConfig.value.isShow = false;
    } else {
      OuModal.warning("请至少填写一个条件");
    }
  };
  const importWhite = () => {
    importFile();
  };
  const confirmWhiteList = async () => {
    if (dialogConfig.value.enums !== "import") {
      handleWhite();
    } else {
      if (!importFileData.value || importFileData.value.type !== "upload") {
        OuModal.warning("请上传文件");
        return;
      }
      importWhite();
    }
  };
  const getUploadData = data => {};
  const exportResult = res => {
    const { text, state } = res;
    const type = {
      1: {
        title: "导入成功",
        icon: "success",
      },
      2: {
        title: "部分导入失败",
        icon: "warning",
      },
      3: {
        title: "导入失败",
        icon: "error",
      },
    };
    result.value.icon = type[state].icon;
    result.value.title = type[state].title;
    result.value.text = text;
    result.value.state = state;
    step.value = "import_result";
  };
  const fileChange = async file => {
    importFileData.value = file;
  };
  const importFile = async () => {
    const { type, files } = importFileData.value;
    const fd = new FormData();
    fd.append("file", files);
    await uploadExcel(fd).then(res => {
      const { state, success_items, fail_items, success_total, fail_total, fail_reason } = res.data;
      const successItem = success_items || [];
      const failItem = fail_items || [];
      successList.value.push(...successItem);
      failList.value.push(...failItem);
      const reason = {
        1: `全部导入成功，共计${success_total || 0}条`,
        2: `导入成功${success_total || 0}条，导入失败${fail_total || 0}条，点击下载`,
        3: fail_reason || "",
      };
      const msg = {
        state,
        text: reason[state],
      };
      exportResult(msg);
    });
    importFileData.value = null;
  };
  const downTemplate = () => {
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const config = {
      header: ["源IP", "路径", "规则类型"],
      fileName: `白名单模板${formatTime}`,
    };
    exportExcelFile(config);
  };
  const downFail = () => {
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const data = failList.value.map(e => {
      return {
        源IP: e[0] || "",
        路径: e[1] || "",
        规则类型: e[2] || "",
        失败原因: e[3] || "",
      };
    });
    const config = {
      header: ["源IP", "路径", "规则类型"],
      fileName: `导入失败白名单_${formatTime}`,
      data,
    };
    exportExcelFile(config);
  };
  const configMapRuleIdList = data => {
    const typeList = data && typeof data === "string" ? data.split(",") : [];
    return typeList.reduce((acc, cur) => {
      const ruleItem = rule.find(e => e.TypeName === cur);
      if (ruleItem) {
        acc.push(ruleItem.TypeId);
      }
      return acc;
    }, []);
  };
  const importSuccess = () => {
    const data = successList.value.map(e => ({
      source_ip: e[0] || "",
      rule_type: configMapRuleIdList(e[2]),
      rule_id: "",
      path: e[1] || "",
      status: true,
      type: "rule_type",
    }));
    whiteList.value.unshift(...data);
    queryParams.value.currentPage = 1;
    dialogConfig.value.isShow = false;
  };
  const configIpList = () => {
    return configWhite(whiteList.value);
  };
  const paginationChange = () => {};
  defineExpose({
    configIpList,
  });
  const initData = () => {
    whiteList.value = props.list;
  };
  initData();
</script>

<style scoped lang="scss">
  .import_result {
    text-align: center;
    font-weight: 400;
    font-size: 16px;
    color: var(--ouryun-text-color-gray-1);
    margin-top: 8px;
    .icon {
      font-size: 50px;
    }

    .tip {
      margin: 20px 0 12px;
      font-weight: 400;
      font-size: 16px;
      color: var(--ouryun-text-color-gray-1);
      line-height: 24px;
    }

    .desc {
      font-weight: 400;
      font-size: 14px;
      color: var(--ouryun-text-color-gray-2);
      line-height: 21px;
    }
  }

  .pn-white-list {
    width: 100%;
  }
  .table-pagination {
    display: flex;
    justify-content: end;
    padding: 12px 0px;
  }
  .rule-list-tips::before {
    content: "·";
  }
  .content-tip {
    font-weight: 400;
    font-size: 14px;
    color: var(--ouryun-color-font-secondary-light);
    line-height: 22px;
  }
</style>
