import { ref } from "vue";
import usePlugin from "basePluginStore";
const store = usePlugin();

export const fieldOptionsAll = ref([
  {
    label: "源IP",
    value: "srcIp",
    disabled: false,
  },
  {
    label: "路径",
    value: "path",
    disabled: false,
  },
  {
    label: "请求头部",
    value: "requestHeader",
    disabled: false,
  },
  {
    label: "请求方法",
    value: "requestMethod",
    disabled: false,
  },
  {
    label: "查询参数",
    value: "queryParameter",
    disabled: false,
  },
  {
    label: "账号",
    value: "userName",
    disabled: false,
  },
  {
    label: "来源地址",
    value: "sourceLocation",
    disabled: false,
  },
  {
    label: "地区",
    value: "region",
    disabled: false,
  },
  {
    label: "时间",
    value: "time",
    disabled: false,
  },
]);

export const logicOptionsAll = [
  {
    label: "等于",
    value: "exactTrue",
  },
  {
    label: "包含",
    value: "containsTrue",
  },
  {
    label: "不等于",
    value: "exactFalse",
  },
  {
    label: "不包含",
    value: "containsFalse",
  },
  {
    label: "内容为空",
    value: "null",
  },
  {
    label: "存在",
    value: "presentMatchTrue",
  },
  {
    label: "不存在",
    value: "presentMatchFalse",
  },
  {
    label: "前缀匹配",
    value: "prefix",
  },
  {
    label: "正则匹配",
    value: "regex",
  },
  {
    label: "属于IP组",
    value: "belongToIpGroup",
  },
  {
    label: "不属于IP组",
    value: "notBelongToIpGroup",
  },
  {
    label: "属于时间段",
    value: "belongToTimeRange",
  },
  {
    label: "不属于时间段",
    value: "notBelongToTimeRange",
  },
];

export const methodOptions = [
  {
    value: "GET",
    label: "GET",
    isCheck: false,
  },
  {
    value: "PUT",
    label: "PUT",
    isCheck: false,
  },
  {
    value: "CONNECT",
    label: "CONNECT",
    isCheck: false,
  },
  {
    value: "HEAD",
    label: "HEAD",
    isCheck: false,
  },
  {
    value: "POST",
    label: "POST",
    isCheck: false,
  },
  {
    value: "DELETE",
    label: "DELETE",
    isCheck: false,
  },
  {
    value: "TRACE",
    label: "TRACE",
    isCheck: false,
  },
  {
    value: "PATCH",
    label: "PATCH",
    isCheck: false,
  },
  {
    value: "OPTIONS",
    label: "OPTIONS",
    isCheck: false,
  },
];

export const locationTreeList = [
  {
    value: "156",
    label: "中国",
    children: [
      {
        value: "110000",
        label: "北京市",
      },
      {
        value: "120000",
        label: "天津市",
      },
      {
        value: "130000",
        label: "河北省",
      },
      {
        value: "140000",
        label: "山西省",
      },
      {
        value: "150000",
        label: "内蒙古自治区",
      },
      {
        value: "210000",
        label: "辽宁省",
      },
      {
        value: "220000",
        label: "吉林省",
      },
      {
        value: "230000",
        label: "黑龙江省",
      },
      {
        value: "310000",
        label: "上海市",
      },
      {
        value: "320000",
        label: "江苏省",
      },
      {
        value: "330000",
        label: "浙江省",
      },
      {
        value: "340000",
        label: "安徽省",
      },
      {
        value: "350000",
        label: "福建省",
      },
      {
        value: "360000",
        label: "江西省",
      },
      {
        value: "370000",
        label: "山东省",
      },
      {
        value: "410000",
        label: "河南省",
      },
      {
        value: "420000",
        label: "湖北省",
      },
      {
        value: "430000",
        label: "湖南省",
      },
      {
        value: "440000",
        label: "广东省",
      },
      {
        value: "450000",
        label: "广西壮族自治区",
      },
      {
        value: "460000",
        label: "海南省",
      },
      {
        value: "500000",
        label: "重庆市",
      },
      {
        value: "510000",
        label: "四川省",
      },
      {
        value: "520000",
        label: "贵州省",
      },
      {
        value: "530000",
        label: "云南省",
      },
      {
        value: "540000",
        label: "西藏自治区",
      },
      {
        value: "610000",
        label: "陕西省",
      },
      {
        value: "620000",
        label: "甘肃省",
      },
      {
        value: "630000",
        label: "青海省",
      },
      {
        value: "640000",
        label: "宁夏回族自治区",
      },
      {
        value: "650000",
        label: "新疆维吾尔自治区",
      },
      {
        value: "710000",
        label: "台湾省",
      },
      {
        value: "810000",
        label: "香港特别行政区",
      },
      {
        value: "820000",
        label: "澳门特别行政区",
      },
    ],
  },
  {
    value: "4",
    label: "阿富汗",
  },
  {
    value: "8",
    label: "阿尔巴尼亚",
  },
  {
    value: "10",
    label: "南极洲",
  },
  {
    value: "12",
    label: "阿尔及利亚",
  },
  {
    value: "16",
    label: "美属萨摩亚",
  },
  {
    value: "20",
    label: "安道尔",
  },
  {
    value: "24",
    label: "安哥拉",
  },
  {
    value: "28",
    label: "安提瓜和巴布达",
  },
  {
    value: "31",
    label: "阿塞拜疆",
  },
  {
    value: "32",
    label: "阿根廷",
  },
  {
    value: "36",
    label: "澳大利亚",
  },
  {
    value: "40",
    label: "奥地利",
  },
  {
    value: "44",
    label: "巴哈马",
  },
  {
    value: "48",
    label: "巴林",
  },
  {
    value: "50",
    label: "孟加拉国",
  },
  {
    value: "51",
    label: "亚美尼亚",
  },
  {
    value: "52",
    label: "巴巴多斯",
  },
  {
    value: "56",
    label: "比利时",
  },
  {
    value: "60",
    label: "百慕大",
  },
  {
    value: "64",
    label: "不丹",
  },
  {
    value: "68",
    label: "玻利维亚",
  },
  {
    value: "70",
    label: "波黑",
  },
  {
    value: "72",
    label: "博茨瓦纳",
  },
  {
    value: "74",
    label: "布韦岛",
  },
  {
    value: "76",
    label: "巴西",
  },
  {
    value: "84",
    label: "伯利兹",
  },
  {
    value: "86",
    label: "英属印度洋领地",
  },
  {
    value: "90",
    label: "所罗门群岛",
  },
  {
    value: "92",
    label: "英属维尔京群岛",
  },
  {
    value: "96",
    label: "文莱",
  },
  {
    value: "100",
    label: "保加利亚",
  },
  {
    value: "104",
    label: "缅甸",
  },
  {
    value: "108",
    label: "布隆迪",
  },
  {
    value: "112",
    label: "白俄罗斯",
  },
  {
    value: "116",
    label: "柬埔寨",
  },
  {
    value: "120",
    label: "喀麦隆",
  },
  {
    value: "124",
    label: "加拿大",
  },
  {
    value: "132",
    label: "佛得角",
  },
  {
    value: "136",
    label: "开曼群岛",
  },
  {
    value: "140",
    label: "中非",
  },
  {
    value: "144",
    label: "斯里兰卡",
  },
  {
    value: "148",
    label: "乍得",
  },
  {
    value: "152",
    label: "智利",
  },
  {
    value: "162",
    label: "圣诞岛",
  },
  {
    value: "166",
    label: "科科斯（基林）群岛",
  },
  {
    value: "170",
    label: "哥伦比亚",
  },
  {
    value: "174",
    label: "科摩罗",
  },
  {
    value: "175",
    label: "马约特",
  },
  {
    value: "178",
    label: "刚果共和国",
  },
  {
    value: "180",
    label: "刚果民主共和国",
  },
  {
    value: "184",
    label: "库克群岛",
  },
  {
    value: "188",
    label: "哥斯达黎加",
  },
  {
    value: "191",
    label: "克罗地亚",
  },
  {
    value: "192",
    label: "古巴",
  },
  {
    value: "196",
    label: "塞浦路斯",
  },
  {
    value: "203",
    label: "捷克",
  },
  {
    value: "204",
    label: "贝宁",
  },
  {
    value: "208",
    label: "丹麦",
  },
  {
    value: "212",
    label: "多米尼克",
  },
  {
    value: "214",
    label: "多米尼加",
  },
  {
    value: "218",
    label: "厄瓜多尔",
  },
  {
    value: "222",
    label: "萨尔瓦多",
  },
  {
    value: "226",
    label: "赤道几内亚",
  },
  {
    value: "231",
    label: "埃塞俄比亚",
  },
  {
    value: "232",
    label: "厄立特里亚",
  },
  {
    value: "233",
    label: "爱沙尼亚",
  },
  {
    value: "234",
    label: "法罗群岛",
  },
  {
    value: "238",
    label: "福克兰群岛",
  },
  {
    value: "239",
    label: "南乔治亚和南桑威奇群岛",
  },
  {
    value: "242",
    label: "斐济",
  },
  {
    value: "246",
    label: "芬兰",
  },
  {
    value: "248",
    label: "奥兰",
  },
  {
    value: "250",
    label: "法国",
  },
  {
    value: "254",
    label: "法属圭亚那",
  },
  {
    value: "258",
    label: "法属波利尼西亚",
  },
  {
    value: "260",
    label: "法属南部和南极领地",
  },
  {
    value: "262",
    label: "吉布提",
  },
  {
    value: "266",
    label: "加蓬",
  },
  {
    value: "268",
    label: "格鲁吉亚",
  },
  {
    value: "270",
    label: "冈比亚",
  },
  {
    value: "275",
    label: "巴勒斯坦",
  },
  {
    value: "276",
    label: "德国",
  },
  {
    value: "288",
    label: "加纳",
  },
  {
    value: "292",
    label: "直布罗陀",
  },
  {
    value: "296",
    label: "基里巴斯",
  },
  {
    value: "300",
    label: "希腊",
  },
  {
    value: "304",
    label: "格陵兰",
  },
  {
    value: "308",
    label: "格林纳达",
  },
  {
    value: "312",
    label: "瓜德罗普",
  },
  {
    value: "316",
    label: "关岛",
  },
  {
    value: "320",
    label: "危地马拉",
  },
  {
    value: "324",
    label: "几内亚",
  },
  {
    value: "328",
    label: "圭亚那",
  },
  {
    value: "332",
    label: "海地",
  },
  {
    value: "334",
    label: "赫德岛和麦克唐纳群岛",
  },
  {
    value: "336",
    label: "梵蒂冈",
  },
  {
    value: "340",
    label: "洪都拉斯",
  },
  {
    value: "348",
    label: "匈牙利",
  },
  {
    value: "352",
    label: "冰岛",
  },
  {
    value: "356",
    label: "印度",
  },
  {
    value: "360",
    label: "印度尼西亚",
  },
  {
    value: "364",
    label: "伊朗",
  },
  {
    value: "368",
    label: "伊拉克",
  },
  {
    value: "372",
    label: "爱尔兰",
  },
  {
    value: "376",
    label: "以色列",
  },
  {
    value: "380",
    label: "意大利",
  },
  {
    value: "384",
    label: "科特迪瓦",
  },
  {
    value: "388",
    label: "牙买加",
  },
  {
    value: "392",
    label: "日本",
  },
  {
    value: "398",
    label: "哈萨克斯坦",
  },
  {
    value: "400",
    label: "约旦",
  },
  {
    value: "404",
    label: "肯尼亚",
  },
  {
    value: "408",
    label: "朝鲜",
  },
  {
    value: "410",
    label: "韩国",
  },
  {
    value: "414",
    label: "科威特",
  },
  {
    value: "417",
    label: "吉尔吉斯斯坦",
  },
  {
    value: "418",
    label: "老挝",
  },
  {
    value: "422",
    label: "黎巴嫩",
  },
  {
    value: "426",
    label: "莱索托",
  },
  {
    value: "428",
    label: "拉脱维亚",
  },
  {
    value: "430",
    label: "利比里亚",
  },
  {
    value: "434",
    label: "利比亚",
  },
  {
    value: "438",
    label: "列支敦士登",
  },
  {
    value: "440",
    label: "立陶宛",
  },
  {
    value: "442",
    label: "卢森堡",
  },
  {
    value: "450",
    label: "马达加斯加",
  },
  {
    value: "454",
    label: "马拉维",
  },
  {
    value: "458",
    label: "马来西亚",
  },
  {
    value: "462",
    label: "马尔代夫",
  },
  {
    value: "466",
    label: "马里",
  },
  {
    value: "470",
    label: "马耳他",
  },
  {
    value: "474",
    label: "马提尼克",
  },
  {
    value: "478",
    label: "毛里塔尼亚",
  },
  {
    value: "480",
    label: "毛里求斯",
  },
  {
    value: "484",
    label: "墨西哥",
  },
  {
    value: "492",
    label: "摩纳哥",
  },
  {
    value: "496",
    label: "蒙古国",
  },
  {
    value: "498",
    label: "摩尔多瓦",
  },
  {
    value: "499",
    label: "黑山",
  },
  {
    value: "500",
    label: "蒙特塞拉特",
  },
  {
    value: "504",
    label: "摩洛哥",
  },
  {
    value: "508",
    label: "莫桑比克",
  },
  {
    value: "512",
    label: "阿曼",
  },
  {
    value: "516",
    label: "纳米比亚",
  },
  {
    value: "520",
    label: "瑙鲁",
  },
  {
    value: "524",
    label: "尼泊尔",
  },
  {
    value: "528",
    label: "荷兰",
  },
  {
    value: "531",
    label: "库拉索",
  },
  {
    value: "533",
    label: "阿鲁巴",
  },
  {
    value: "534",
    label: "荷属圣马丁",
  },
  {
    value: "535",
    label: "荷兰加勒比区",
  },
  {
    value: "540",
    label: "新喀里多尼亚",
  },
  {
    value: "548",
    label: "瓦努阿图",
  },
  {
    value: "554",
    label: "新西兰",
  },
  {
    value: "558",
    label: "尼加拉瓜",
  },
  {
    value: "562",
    label: "尼日尔",
  },
  {
    value: "566",
    label: "尼日利亚",
  },
  {
    value: "570",
    label: "纽埃",
  },
  {
    value: "574",
    label: "诺福克岛",
  },
  {
    value: "578",
    label: "挪威",
  },
  {
    value: "580",
    label: "北马里亚纳群岛",
  },
  {
    value: "581",
    label: "美国本土外小岛屿",
  },
  {
    value: "583",
    label: "密克罗尼西亚联邦",
  },
  {
    value: "584",
    label: "马绍尔群岛",
  },
  {
    value: "585",
    label: "帕劳",
  },
  {
    value: "586",
    label: "巴基斯坦",
  },
  {
    value: "591",
    label: "巴拿马",
  },
  {
    value: "598",
    label: "巴布亚新几内亚",
  },
  {
    value: "600",
    label: "巴拉圭",
  },
  {
    value: "604",
    label: "秘鲁",
  },
  {
    value: "608",
    label: "菲律宾",
  },
  {
    value: "612",
    label: "皮特凯恩群岛",
  },
  {
    value: "616",
    label: "波兰",
  },
  {
    value: "620",
    label: "葡萄牙",
  },
  {
    value: "624",
    label: "几内亚比绍",
  },
  {
    value: "626",
    label: "东帝汶",
  },
  {
    value: "630",
    label: "波多黎各",
  },
  {
    value: "634",
    label: "卡塔尔",
  },
  {
    value: "638",
    label: "留尼汪",
  },
  {
    value: "642",
    label: "罗马尼亚",
  },
  {
    value: "643",
    label: "俄罗斯",
  },
  {
    value: "646",
    label: "卢旺达",
  },
  {
    value: "652",
    label: "圣巴泰勒米",
  },
  {
    value: "654",
    label: "圣赫勒拿、阿森松和特里斯坦-达库尼亚",
  },
  {
    value: "659",
    label: "圣基茨和尼维斯",
  },
  {
    value: "660",
    label: "安圭拉",
  },
  {
    value: "662",
    label: "圣卢西亚",
  },
  {
    value: "663",
    label: "法属圣马丁",
  },
  {
    value: "666",
    label: "圣皮埃尔和密克隆",
  },
  {
    value: "670",
    label: "圣文森特和格林纳丁斯",
  },
  {
    value: "674",
    label: "圣马力诺",
  },
  {
    value: "678",
    label: "圣多美和普林西比",
  },
  {
    value: "682",
    label: "沙特阿拉伯",
  },
  {
    value: "686",
    label: "塞内加尔",
  },
  {
    value: "688",
    label: "塞尔维亚",
  },
  {
    value: "690",
    label: "塞舌尔",
  },
  {
    value: "694",
    label: "塞拉利昂",
  },
  {
    value: "702",
    label: "新加坡",
  },
  {
    value: "703",
    label: "斯洛伐克",
  },
  {
    value: "704",
    label: "越南",
  },
  {
    value: "705",
    label: "斯洛文尼亚",
  },
  {
    value: "706",
    label: "索马里",
  },
  {
    value: "710",
    label: "南非",
  },
  {
    value: "716",
    label: "津巴布韦",
  },
  {
    value: "724",
    label: "西班牙",
  },
  {
    value: "728",
    label: "南苏丹",
  },
  {
    value: "729",
    label: "苏丹",
  },
  {
    value: "732",
    label: "西撒哈拉",
  },
  {
    value: "740",
    label: "苏里南",
  },
  {
    value: "744",
    label: "斯瓦尔巴和扬马延",
  },
  {
    value: "748",
    label: "斯威士兰",
  },
  {
    value: "752",
    label: "瑞典",
  },
  {
    value: "756",
    label: "瑞士",
  },
  {
    value: "760",
    label: "叙利亚",
  },
  {
    value: "762",
    label: "塔吉克斯坦",
  },
  {
    value: "764",
    label: "泰国",
  },
  {
    value: "768",
    label: "多哥",
  },
  {
    value: "772",
    label: "托克劳",
  },
  {
    value: "776",
    label: "汤加",
  },
  {
    value: "780",
    label: "特立尼达和多巴哥",
  },
  {
    value: "784",
    label: "阿联酋",
  },
  {
    value: "788",
    label: "突尼斯",
  },
  {
    value: "792",
    label: "土耳其",
  },
  {
    value: "795",
    label: "土库曼斯坦",
  },
  {
    value: "796",
    label: "特克斯和凯科斯群岛",
  },
  {
    value: "798",
    label: "图瓦卢",
  },
  {
    value: "800",
    label: "乌干达",
  },
  {
    value: "804",
    label: "乌克兰",
  },
  {
    value: "807",
    label: "北马其顿",
  },
  {
    value: "818",
    label: "埃及",
  },
  {
    value: "826",
    label: "英国",
  },
  {
    value: "831",
    label: "根西",
  },
  {
    value: "832",
    label: "泽西",
  },
  {
    value: "833",
    label: "马恩岛",
  },
  {
    value: "834",
    label: "坦桑尼亚",
  },
  {
    value: "840",
    label: "美国",
  },
  {
    value: "850",
    label: "美属维尔京群岛",
  },
  {
    value: "854",
    label: "布基纳法索",
  },
  {
    value: "858",
    label: "乌拉圭",
  },
  {
    value: "860",
    label: "乌兹别克斯坦",
  },
  {
    value: "862",
    label: "委内瑞拉",
  },
  {
    value: "876",
    label: "瓦利斯和富图纳",
  },
  {
    value: "882",
    label: "萨摩亚",
  },
  {
    value: "887",
    label: "也门",
  },
  {
    value: "894",
    label: "赞比亚",
  },
];

export const queryParameterRegularList = [
  {
    label: "身份证号码",
    value: "^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$",
  },
  {
    label: "手机号码",
    value: "^1[3-9]\d{9}$",
  },
  {
    label: "银行卡号",
    value: "^\d{16,19}$",
  },
  {
    label: "邮箱地址",
    value: "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$",
  },
];

export const findNodeLabel = (targetValue, treeData) => {
  // 遍历每个节点
  for (let node of treeData) {
    // 如果当前节点匹配
    if (node.value === targetValue) {
      return node.label;
    }

    // 如果有子节点，递归查找
    if (node.children && node.children.length > 0) {
      const result = findNodeLabel(targetValue, node.children);
      if (result) return result;
    }
  }

  // 未找到节点
  return null;
};

/**
 * 获取匹配条件概览文字信息
 * @param {array} matchList - 匹配规则列表form
 * @returns {object} 匹配规则参数
 */
export const getTextInfo = matchList => {
  if (!matchList.length) return "任意";
  const textArr = matchList.map(item => {
    const field = fieldOptionsAll.value.find(option => option.value === item.field)?.label;
    const params = item.params;
    const logic = logicOptionsAll.find(option => option.value === item.logic)?.label;
    let content = "";
    if (item.field === "requestMethod") {
      if (item.content.length === methodOptions.length) {
        content = "全部";
      } else {
        content = item.content.join("、");
      }
    } else if (item.field === "queryParameter") {
      const currenterRegular = queryParameterRegularList.find(regular => regular.value === item.content);
      if (currenterRegular) {
        content = currenterRegular.label;
      } else {
        content = item.content;
      }
    } else if (item.field === "userName") {
      if (Array.isArray(item.content)) {
        content = item.content.join("、");
      }
    } else {
      content = item.content;
    }
    const arr = [];
    if (field) arr.push(field);
    if (["requestHeader", "queryParameter"].includes(item.field)) arr.push(params);
    if (item.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(item.logic)) {
      content = item.content
        .filter(id => store.ipGroupList.some(ipGroup => ipGroup.value === id))
        .map(contentItem => store.ipGroupList.find(ipItem => ipItem.value === contentItem).label)
        .join("、");
    }
    if (item.field === "time" && ["belongToTimeRange", "notBelongToTimeRange"].includes(item.logic)) {
      content = store.timePeriodsList.find(timePeriod => timePeriod.value === item.content)?.label || "-";
    }
    if (item.field === "region") {
      content = item.content
        .map(locationItem => {
          return findNodeLabel(locationItem, locationTreeList);
        })
        .join("、");
    }
    if (logic) arr.push(logic);
    if (content) arr.push(content);
    return `[${arr.join("，")}]`;
  });
  const textInfo = textArr.join("，");
  return textInfo;
};

/**
 * 将matchForm处理成后端需要的参数
 * @param {array} matchList - 匹配规则列表form
 * @returns {object} 匹配规则参数
 */
export const getMatchParameter = (matchList = []) => {
  // 根据数组的某字段归类生成一个新的对象
  const groupByField = (arr, field) => {
    let result = {};
    arr.forEach(item => {
      if (!result[item[field]]) {
        result[item[field]] = [];
      }
      result[item[field]].push(item);
    });
    return result;
  };
  const filterList = matchList.filter(item => item.field !== "srcIp");
  const matchObj = groupByField(filterList, "field");
  const parameterList = [];
  for (const key in matchObj) {
    switch (key) {
      case "path":
        const paths = matchObj[key].map(item => {
          let obj = {};
          switch (item.logic) {
            case "prefix":
              obj = {
                prefix: item.content,
                path_invert: false,
              };
              break;
            case "regex":
              obj = {
                regex: item.content,
              };
              break;
            case "exactTrue":
              obj = {
                exact: item.content,
                path_invert: false,
              };
              break;
            case "exactFalse":
              obj = {
                exact: item.content,
                path_invert: true,
              };
              break;
            case "containsTrue":
              obj = {
                contains: item.content,
                path_invert: false,
              };
              break;
            case "containsFalse":
              obj = {
                contains: item.content,
                path_invert: true,
              };
              break;
          }
          return obj;
        });
        // 路径不传数组，需要传对象
        parameterList.push({ ...paths[0] });
        break;
      case "requestHeader":
        const headers = matchObj[key].map(item => {
          let obj = {};
          switch (item.logic) {
            case "regex":
              obj = {
                safe_regex_match: {
                  regex: item.content,
                },
              };
              break;
            case "exactTrue":
              obj = {
                exact_match: item.content,
                invert_match: false,
              };
              break;
            case "exactFalse":
              obj = {
                exact_match: item.content,
                invert_match: true,
              };
              break;
            case "containsTrue":
              obj = {
                contains_match: item.content,
                invert_match: false,
              };
              break;
            case "containsFalse":
              obj = {
                contains_match: item.content,
                invert_match: true,
              };
              break;
            case "presentMatchTrue":
              obj = {
                present_match: true,
              };
              break;
            case "presentMatchFalse":
              obj = {
                present_match: false,
              };
              break;
          }
          obj.name = item.params;
          return obj;
        });
        parameterList.push({ headers });
        break;
      case "requestMethod":
        const method = matchObj[key].map(item => {
          let obj = {};
          let content = JSON.parse(JSON.stringify(item.content));
          switch (item.logic) {
            case "exactTrue":
              obj = {
                methods: content,
                method_invert: false,
              };
              break;
            case "exactFalse":
              obj = {
                methods: content,
                method_invert: true,
              };
              break;
          }
          return obj;
        });
        // 请求方法不传数组，需要传对象
        parameterList.push({ ...method[0] });
        break;
      case "queryParameter":
        const query_parameters = matchObj[key].map(item => {
          let obj = {
            value: {},
          };
          switch (item.logic) {
            case "exactTrue":
              obj.value.exact = item.content;
              obj.value.invert = false;
              break;
            case "containsTrue":
              obj.value.contains = item.content;
              obj.value.invert = false;
              break;
            case "exactFalse":
              obj.value.exact = item.content;
              obj.value.invert = true;
              break;
            case "containsFalse":
              obj.value.contains = item.content;
              obj.value.invert = true;
              break;
            case "presentMatchTrue":
              obj.value.present = true;
              break;
            case "presentMatchFalse":
              obj.value.present = false;
              break;
          }
          obj.name = item.params;
          return obj;
        });
        parameterList.push({ query_parameters });
        break;
      case "userName":
        const users = matchObj[key].map(item => {
          let obj = {};
          switch (item.logic) {
            case "exactTrue":
              obj = {
                // user_name: item.content,
                user_name: Array.isArray(item.content) ? item.content.join(",") : item.content,
                invert: false,
              };
              break;
            case "exactFalse":
              obj = {
                // user_name: item.content,
                user_name: Array.isArray(item.content) ? item.content.join(",") : item.content,
                invert: true,
              };
              break;
          }
          return obj;
        });
        parameterList.push({ users });
        break;
      case "sourceLocation":
        const referers = matchObj[key].map(item => {
          let obj = {};
          switch (item.logic) {
            case "exactTrue":
              obj = {
                exact_match: item.content,
                invert_match: false,
              };
              break;
            case "exactFalse":
              obj = {
                exact_match: item.content,
                invert_match: true,
              };
              break;
            case "containsTrue":
              obj = {
                contains_match: item.content,
                invert_match: false,
              };
              break;
            case "containsFalse":
              obj = {
                contains_match: item.content,
                invert_match: true,
              };
              break;
          }
          obj.name = "Referer";
          return obj;
        });
        parameterList.push({ referers });

        break;
      case "region":
        const region = matchObj[key].map(item => {
          let obj = {};
          switch (item.logic) {
            case "containsTrue":
              obj = {
                invert: false,
                name: item.content,
              };
              break;
            case "containsFalse":
              obj = {
                invert: true,
                name: item.content,
              };
              break;
          }
          return obj;
        });
        // 请求方法不传数组，需要传对象
        parameterList.push({ region: region[0] });
        break;
      case "time":
        const weekMapList = [
          {
            name: "Mon",
            value: 1,
          },
          {
            name: "Tue",
            value: 2,
          },
          {
            name: "Wed",
            value: 3,
          },
          {
            name: "Thu",
            value: 4,
          },
          {
            name: "Fri",
            value: 5,
          },
          {
            name: "Sta",
            value: 6,
          },
          {
            name: "Sun",
            value: 7,
          },
        ];
        const date = matchObj[key].map(item => {
          let obj = {};
          let timePeriodinfo = {};
          let time_period = "";
          if (["belongToTimeRange", "notBelongToTimeRange"].includes(item.logic)) {
            timePeriodinfo = store.timePeriodsList.find(timePeriod => timePeriod.value === item.content);
            time_period = timePeriodinfo.rule.time_periods.map(item => `${item.begin}-${item.end}`);
          }
          switch (item.logic) {
            case "containsTrue":
              obj = {
                exact_date_start: item.content[0],
                exact_date_end: item.content[1],
                invert: false,
              };
              break;
            case "containsFalse":
              obj = {
                exact_date_start: item.content[0],
                exact_date_end: item.content[1],
                invert: true,
              };
              break;
            case "belongToTimeRange":
              obj = {
                time: {
                  week: timePeriodinfo.rule.week.map(
                    weekVal => weekMapList.find(weekMapItem => weekMapItem.value === weekVal).name,
                  ),
                  time_period,
                },
                invert: false,
              };
              break;
            case "notBelongToTimeRange":
              obj = {
                time: {
                  week: timePeriodinfo.rule.week.map(
                    weekVal => weekMapList.find(weekMapItem => weekMapItem.value === weekVal).name,
                  ),
                  time_period,
                },
                invert: true,
              };
              break;
          }
          return obj;
        });
        parameterList.push({ date: date[0] });
        break;
    }
  }

  matchList.forEach(matchItem => {
    if (matchItem.field === "srcIp") {
      if (["containsTrue", "containsFalse"].includes(matchItem.logic)) {
        // 等于/不等于
        const ipListParamter = {
          ip_set: {},
          ip_range: [],
          invert: null,
        };

        const ipArr = matchItem.content.split(",");
        const list = ipArr.filter(ipArrItem => !ipArrItem.includes("-"));
        const ip_range = ipArr.filter(ipArrItem => ipArrItem.includes("-"));

        ipListParamter.ip_set.list = list.map(ipItem => {
          let address_prefix = ipItem;
          let prefix_len = 32;
          if (ipItem.includes("/")) {
            address_prefix = ipItem.split("/")[0];
            prefix_len = +ipItem.split("/")[1];
          }
          return {
            address_prefix,
            prefix_len,
          };
        });
        ipListParamter.ip_range = ip_range.map(ipRangeItem => {
          const rangeItem = ipRangeItem.split("-");
          return {
            start_ip: rangeItem[0],
            end_ip: rangeItem[1],
          };
        });
        ipListParamter.invert = matchItem.logic === "containsFalse";
        const ip_list = {};
        if (ipListParamter.ip_set.list.length) ip_list.ip_set = ipListParamter.ip_set;
        if (ipListParamter.ip_range.length) ip_list.ip_range = ipListParamter.ip_range;
        ip_list.invert = ipListParamter.invert;
        parameterList.push({ ip_list });
      } else if (["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic)) {
        // 属于IP组/不属于IP组
        const ipGroupList = matchItem.content.map(id => store.ipGroupList.find(ip => ip.value === id));

        const ip_groups = ipGroupList.map(ipGroup => {
          const ipRangeAllList = ipGroup.ipRanges.split(",");
          const ipRangeList = ipRangeAllList.filter(item => item.includes("-"));
          const ipSetList = ipRangeAllList.filter(item => item && !item.includes("-"));

          const ip_range = ipRangeList.map(item => {
            const rangeItem = item.split("-");
            return {
              start_ip: rangeItem[0],
              end_ip: rangeItem[1],
            };
          });

          const ip_set = ipSetList.map(item => {
            if (item.includes("/")) {
              const rangeItem = item.split("/");
              return {
                address_prefix: rangeItem[0],
                prefix_len: +rangeItem[1],
              };
            } else {
              return {
                address_prefix: item,
                prefix_len: 32,
              };
            }
          });
          return {
            ip_range,
            ip_set: {
              list: ip_set,
            },
            id: ipGroup.value,
            invert: matchItem.logic === "notBelongToIpGroup",
          };
        });

        parameterList.push({ ip_list: { ip_groups } });
      }
    }
  });

  const parameterObj = {
    match: parameterList,
  };

  return parameterObj;
};

/**
 * 将matchingList数据转换成兼容NewMatchedConditionEditor组件的结构
 * @param {array} matchingList - 匹配规则列表
 * @returns {array} 兼容后新的数据结构的匹配规则列表
 */
export const compatibleMatchingList = matchingList => {
  return matchingList.map(item => {
    return item.field === "userName" && !Array.isArray(item.content)
      ? { ...item, content: item.content.split(",") }
      : item;
  });
};
