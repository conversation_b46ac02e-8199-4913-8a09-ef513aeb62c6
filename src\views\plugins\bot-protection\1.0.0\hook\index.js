import { ref, reactive, computed } from "vue";

export default function () {
  const baseForm = reactive({
    check_mode: false,
    protection_level: 0,
    protection_type: [1, 2, 4, 8],
  });
  const rules = {
    protection_type: [{ required: true, trigger: ["change", "blur"], message: "请选择防护类型" }],
  };
  const formRef = ref(null);
  const LevelList = [
    {
      label: "宽松",
      value: 2,
    },
    {
      label: "中等",
      value: 1,
    },
    {
      label: "严格",
      value: 0,
    },
  ];
  const typeList = [
    {
      label: "恶意扫描型BOT",
      value: 1,
    },
    {
      label: "DDoS型BOT",
      value: 2,
    },
    {
      label: "社交媒体自动化型BOT",
      value: 4,
    },
    {
      label: "爬虫型BOT",
      value: 8,
    },
  ];
  const isIndeterminate = computed(() => {
    return baseForm.protection_type.length > 0 && baseForm.protection_type.length < typeList.length;
  });
  const checkAll = computed({
    get: () => baseForm.protection_type.length === typeList.length,
    set: newValue =>
      newValue ? (baseForm.protection_type = typeList.map(e => e.value)) : (baseForm.protection_type = []),
  });
  return {
    baseForm,
    rules,
    checkAll,
    LevelList,
    typeList,
    isIndeterminate,
    formRef,
  };
}
