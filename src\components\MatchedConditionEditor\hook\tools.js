// 设置字段禁用选项弹出层提示
let handleMouseMove = null;
let handleMouseLeave = null;
const popup = document.createElement("div");

export const addSetFieldOptionsTooltip = () => {
  const fieldDisabledOptions = document.querySelectorAll(".field-options-popover .field-option.is-disabled");
  if (!fieldDisabledOptions || !fieldDisabledOptions.length) return;
  fieldDisabledOptions.forEach(element => {
    // 清除旧提示
    const removePopup = () => {
      const oldPopups = element.querySelectorAll(".popup");
      if (oldPopups && oldPopups.length) {
        oldPopups.forEach(oldPopup => {
          element.removeChild(oldPopup);
        });
      }
    };

    // 鼠标移入
    handleMouseMove = e => {
      removePopup();

      const text = element.querySelector("span").innerText;
      popup.style.display = "block";
      popup.classList.add("popup");
      popup.innerText = text + "字段只支持添加一个条件";
      popup.style.left = e.clientX + "px";
      popup.style.top = e.clientY + "px";
      element.appendChild(popup);
    };

    // 鼠标移出
    handleMouseLeave = () => removePopup();

    element.addEventListener("mousemove", handleMouseMove);
    element.addEventListener("mouseleave", handleMouseLeave);
  });
};

export const removeSetFieldOptionsTooltip = () => {
  const fieldDisabledOptions = document.querySelectorAll(".field-options-popover .field-option.is-disabled");
  if (!fieldDisabledOptions || !fieldDisabledOptions.length) return;

  fieldDisabledOptions.forEach(element => {
    handleMouseMove && element.removeEventListener("mousemove", handleMouseMove);
    handleMouseLeave && element.removeEventListener("mouseleave", handleMouseLeave);
  });
};
