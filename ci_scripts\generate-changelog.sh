#!/bin/bash
###
# @Author: changelog bot
# @Description: 生成 CHANGELOG，推送企业微信，并输出供 GitHub Actions 使用
# @Usage: ./generate-changelog.sh <TAG_NAME>
###

set -x
set +e

TAG_NAME="$1"
BRANCH_BY="$2"
COMPONENT_VERSION="$3"
DURATION_FMT=${4:-""}

if [[ -z "$TAG_NAME" ]]; then
  echo "❌ 请提供 tag 名称作为第一个参数"
  exit 1
fi


# 解析 TAG 格式：v2.7.3-pre.101
if [[ "$TAG_NAME" =~ ^v([0-9]+\.[0-9]+\.[0-9]+)-([a-z]+)\.([0-9]+)$ ]]; then
  BASE="${BASH_REMATCH[1]}-${BASH_REMATCH[2]}"
  CUR_NUM=${BASH_REMATCH[3]}
  PREV_NUM=$((CUR_NUM - 1))
  FROM_TAG="v${BASE}.${PREV_NUM}"
  TO_TAG="$TAG_NAME"
else
  echo "❌ 无法解析 tag 格式: $TAG_NAME"
  exit 1
fi

echo "🔍 比较区间: $FROM_TAG..$TO_TAG"
git fetch --tags

# 检查 FROM_TAG 是否存在
if ! git rev-parse "$FROM_TAG" >/dev/null 2>&1; then
  # 前一个 tag不存在，将显示无提交差异
  LOG_LINES=""
else
  # 只展示前五十条提交记录
  RAW_LINES=$(git log "$FROM_TAG..$TO_TAG" --merges --pretty=format:"%s" | head -n 50)

  LOG_LINES=$(echo "$RAW_LINES" | sed -n "s/^Merge pull request '\(.*\)' (#\([0-9]\+\)).*/\1|\2/p")

fi
CHANGELOG="# ✨ plugin-ui版本: $TO_TAG"$'\n'
CHANGELOG+="> **基于分支**: $BRANCH_BY"$'\n'

if [ -n "$DURATION_FMT" ]; then
  CHANGELOG+="> ⏱ 构建耗时: **$DURATION_FMT**"$'\n'
fi

if [[ -z "$LOG_LINES" ]]; then
  CHANGELOG+=$'\n'"⚠️ 无提交差异"
else
  declare -A SECTIONS=(
    ["feat"]="✨ 新功能"
    ["fix"]="🐞 修复问题"
    ["docs"]="📃 文档"
    ["style"]="🎨 样式优化"
    ["chore"]="🔧 构建/工具"
    ["refactor"]="♻️ 代码重构"
    ["test"]="✅ 测试"
    ["perf"]="⚡️ 性能优化"
    ["revert"]="⏪️ 回退"
    ["merge"]="🔀 代码合并"
  )

  declare -A CONTENTS

while IFS='|' read -r SUBJECT PR_ID; do
  TYPE=$(echo "$SUBJECT" | grep -oE '^[a-z]+')
  DESC=$(echo "$SUBJECT" | sed -E 's/^[a-z]+: //')

  SECTION=${SECTIONS[$TYPE]}

  # 只处理已定义的 TYPE，忽略其他
  if [[ -n "$SECTION" ]]; then
    PR_LINK="[#${PR_ID}](https://git.ouryun.cn/base/plugin-ui/pulls/${PR_ID})"
    CONTENTS["$SECTION"]+="- $DESC ($PR_LINK)"$'\n'
  fi
done <<< "$LOG_LINES"

  for KEY in "${!CONTENTS[@]}"; do
    CHANGELOG+=$'\n'"### $KEY"$'\n'"${CONTENTS[$KEY]}"
  done
fi

# 推送企业微信 markdown 消息
# ESCAPED_CONTENT=$(echo "$CHANGELOG" | sed 's/"/\\"/g')

# WXPAYLOAD='{
#   "msgtype": "markdown",
#   "markdown": {
#     "content": "'"$ESCAPED_CONTENT"'"
#   }
# }'

# WEBHOOK_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=af9796a4-fa90-415d-9f95-1b44f6078d0d"
# curl --max-time 15 -s -X POST -H 'Content-Type: application/json' -d "$WXPAYLOAD" "$WEBHOOK_URL" > /dev/null
# 删除 changelog 中的标题行
CHANGELOG=$(echo "$CHANGELOG" | sed '1d')

echo "$CHANGELOG"
