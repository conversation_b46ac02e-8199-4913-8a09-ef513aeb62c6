// 为 MatchedConditionEditor 类的 table 设置样式
.ouryun-table.matchedConditionEditor {
  $table-border-color: var(--ouryun-table-border-color);
  border-radius: 2px;
  box-sizing: content-box;
  .ouryun-table__inner-wrapper::before {
    background-color: transparent;
  }

  th.ouryun-table__cell.is-leaf {
    border: none;
  }
  .ouryun-table__header-wrapper {
    height: 40px;
    border-bottom: 1px solid $table-border-color;
    box-sizing: border-box;
  }

  .ouryun-table__cell {
    padding: 0;
    height: 40px;
    box-sizing: border-box;
  }

  .ouryun-table__body-wrapper .ouryun-table__cell {
    &:not(.is-center) {
      .cell {
        height: 38px;
        display: flex;
        align-items: center;
      }
    }
    &.ouryun-table-fixed-column--right {
      .cell {
        background-color: #fff;
      }
    }
  }

  td.ouryun-table__cell {
    border-bottom: none !important;
  }
  .ouryun-table__row {
    background-color: transparent;
    box-shadow: 0 0 0 0.5px $table-border-color;
    &:last-child {
      box-shadow:
        0 1px 0 0 $table-border-color,
        0 0 0 0.5px $table-border-color;
    }
    &:hover {
      background-color: #f5f7fa;
      box-shadow: 0 0 0 1px $table-border-color;
    }
  }
  tr:hover > td.ouryun-table__cell {
    background-color: transparent;
  }
}

.ouryun-dropdown__popper {
  &.ouryun-popper {
    margin-top: -8px;
    .ouryun-dropdown-menu {
      padding: 6px;
      color: #333333;
      .ouryun-dropdown-menu__item:hover {
        background-color: #f3f3f3;
        border-radius: 4px;
      }
    }
  }

  .ouryun-popper__arrow {
    display: none;
  }
}
