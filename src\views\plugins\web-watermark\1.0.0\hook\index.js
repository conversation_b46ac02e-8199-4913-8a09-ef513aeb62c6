import { ref, reactive, computed } from "vue";
import { regex } from "@ouryun/ouryun-plus";

export default function () {
  // 十六进制色正则校验
  const regHex = /^#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/;

  const matchedConditionEditorRef = ref(null);

  // 默认水印效果
  const defaultConfig = {
    color: "#969696", // 默认颜色
    lightTransparency: "30", // 默认明水印透明度
    darkTransparency: "98", // 默认暗水印透明度
  };

  class Form {
    matchers = [];
    type = "BRIGHT";
    water_mark_context = [];
    font_color = "#969696";
    transparency = "30";
    font_size = "STANDARD";
    tilt_method = "UP";
    density = "STANDARDS";
  }

  const form = reactive(new Form());

  const formRef = ref(null);

  const validateFontColor = (rule, value, callback) => {
    if (value === "#") return callback(new Error("请输入"));
    if (value.length < 7 && value.length !== 4) {
      callback(new Error("请输入/选择完整的颜色代码"));
    }
    callback();
  };

  const validateTransparency = (rule, value, callback) => {
    if (!regex.NUMBER.test(value) || value > 100) {
      return callback(new Error("请输入0-100之间的正整数"));
    }
    callback();
  };

  const formRules = {
    type: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: "请选择水印类型",
      },
    ],
    water_mark_context: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: "请选择水印内容",
      },
    ],
    font_color: [
      {
        required: true,
        trigger: ["blur", "change"],
        validator: validateFontColor,
      },
    ],
    transparency: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: "请输入透明度",
      },
      {
        trigger: ["blur", "change"],
        validator: validateTransparency,
      },
    ],
    font_size: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: "请选择字体大小",
      },
    ],
    tilt_method: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: "请选择倾斜方式",
      },
    ],
    density: [
      {
        required: true,
        trigger: ["blur", "change"],
        message: "请选择密度",
      },
    ],
  };

  const typeOptions = [
    {
      label: "明水印",
      value: "BRIGHT",
    },
    {
      label: "暗水印",
      value: "DARK",
    },
  ];

  const newWatermarkContextOptions = [
    {
      label: "源IP",
      value: "SRCIP",
      content: "{IP}",
    },
    {
      label: "账号",
      value: "ACCOUNT",
      content: "{Account}",
    },
    {
      label: "应用名称",
      value: "APPNAME",
      content: "{App}",
    },
    {
      label: "当前时间",
      value: "CURTIME",
      content: "{Time}",
    },
    // {
    //   label: "自定义",
    //   value: "CUSTOM",
    //   content: "{Custom}",
    // },
  ];

  const watermarkContextOptions = ref([...newWatermarkContextOptions]);
  const fontSizeOptions = [
    {
      label: "小号",
      value: "SMALL",
    },
    {
      label: "标准",
      value: "STANDARD",
    },
    {
      label: "大号",
      value: "BIG",
    },
  ];
  const tiltMethodOptions = [
    {
      label: "向上倾斜",
      value: "UP",
    },
    {
      label: "水平",
      value: "HORIZONTALLY",
    },
    {
      label: "向下倾斜",
      value: "DOWN",
    },
  ];
  const densityOptions = [
    {
      label: "宽松",
      value: "LOOSE",
    },
    {
      label: "标准",
      value: "STANDARDS",
    },
    {
      label: "紧密",
      value: "TIGHT",
    },
  ];

  const fontColorComputed = computed({
    get: () => (regHex.test(form.font_color) ? form.font_color : defaultConfig.color),
    set: newValue => (form.font_color = newValue || "#"),
  });

  const watermarkContextComputed = computed(() => {
    return watermarkContextOptions.value
      .filter(item => form.water_mark_context.some(contentVal => contentVal === item.value))
      .map(item => item.content)
      .join("-");
  });

  // 将十六进制色转换为rgba色
  function hexToRgb(hex, transparency) {
    // 移除可能的 # 符号
    hex = regHex.test(hex) ? hex.replace(/^#/, "") : defaultConfig.color.replace(/^#/, "");

    // 处理3位和6位十六进制颜色
    if (hex.length === 3) {
      hex = hex
        .split("")
        .map(char => char + char)
        .join("");
    }

    // 将十六进制转换为十进制
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    const opacity = (100 - Number(transparency || defaultConfig.lightTransparency)) / 100;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  const font = computed(() => {
    return {
      color: hexToRgb(
        form.type === "BRIGHT" ? form.font_color : defaultConfig.color,
        form.type === "BRIGHT"
          ? form.transparency
          : previewSwitch.value
            ? defaultConfig.lightTransparency
            : defaultConfig.darkTransparency,
      ),
      fontSize: form.font_size === "SMALL" ? 16 : form.font_size === "BIG" ? 36 : 24,
    };
  });

  const rotate = computed(() => {
    switch (form.tilt_method) {
      // case "UP":
      //   return -22;
      case "HORIZONTALLY":
        return 0;
      case "DOWN":
        return 22;
      default:
        return -22;
    }
  });

  const gap = computed(() => {
    switch (form.density) {
      case "LOOSE":
        return [150, 150];
      // case "STANDARDS":
      //   return [100, 100];
      case "TIGHT":
        return [50, 50];
      default:
        return [100, 100];
    }
  });

  const previewSwitch = ref(false);

  return {
    regHex,
    matchedConditionEditorRef,
    defaultConfig,
    form,
    formRef,
    formRules,
    typeOptions,
    newWatermarkContextOptions,
    watermarkContextOptions,
    fontSizeOptions,
    tiltMethodOptions,
    densityOptions,
    fontColorComputed,
    watermarkContextComputed,
    font,
    rotate,
    gap,
    previewSwitch,
  };
}
