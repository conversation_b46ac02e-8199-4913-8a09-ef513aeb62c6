<template>
  <div class="tactics">
    <div class="list-handle">
      <ou-button type="primary" text @click="showDialog('add')">添加</ou-button>
      <ou-button type="primary" text :disabled="isCheckData" @click="batchRemove">批量删除</ou-button>
      <MoveGroup
        v-model:list="table.list"
        style="margin-left: 8px"
        :selections="table.multipleList"
        @moved="list => routeMoved('route', list)"
        :disabled="isCheckData"></MoveGroup>
    </div>
    <ou-table
      ref="tablesRef"
      class="editor"
      style="--table-empty-size: 120px"
      :data="table.list"
      :height="!table.list.length ? 205 : table.list.length >= 5 ? 245 : 'auto'"
      @selection-change="handleSelectionChange">
      <ou-table-column type="selection" width="32" align="center"></ou-table-column>
      <ou-table-column label="优先级" type="index" width="58" align="center"></ou-table-column>
      <ou-table-column label="策略名称">
        <template #default="scope">
          <ou-text-ellipsis :content="scope.row.ruleName"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="匹配条件">
        <template #default="scope">
          <ou-text-ellipsis :content="getTextInfo(scope.row.matchRule)"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="脱敏数据">
        <template #default="scope">
          <ou-text-ellipsis :content="configMaskingData(scope.row)"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="状态" width="100">
        <template #default="scope">
          <ou-switch
            v-model="scope.row.enable"
            active-text="启用"
            size="small"
            inactive-text="禁用"
            class="switch-offside"></ou-switch>
        </template>
      </ou-table-column>
      <ou-table-column label="操作" width="170" fixed="right">
        <template #default="scope">
          <ou-button type="primary" text @click="showDialog('edit', scope.row, scope.$index)">编辑</ou-button>
          <ou-button type="primary" text @click="showDialog('copy', scope.row, scope.$index)">复制</ou-button>
          <ou-button type="primary" text @click="top(scope.row, scope.$index)">置顶</ou-button>
          <ou-button type="primary" text @click="remove(scope.row, scope.$index)">删除</ou-button>
        </template>
      </ou-table-column>
    </ou-table>
    <ou-opera-dialog
      v-model="isShowDialog"
      :close-on-click-modal="false"
      :width="1150"
      :title="dialogTitle"
      @close="closeDialog"
      hideFooter
      draggable>
      <template #content>
        <ou-form :model="dialogForm" ref="tacticsRef" :rules="rules" label-position="right" label-width="85px">
          <ou-form-item label="策略名称：" prop="ruleName">
            <ou-input
              style="width: 300px"
              maxlength="63"
              show-word-limit
              placeholder="请输入"
              v-model="dialogForm.ruleName"></ou-input>
          </ou-form-item>
          <ou-form-item label="匹配条件：" prop="matchRule">
            <MatchedConditionEditor
              ref="matchRef"
              v-if="isShowDialog"
              style="width: 100%"
              v-model="dialogForm.matchRule"
              generalPurposeLibrary
              :showAny="false"></MatchedConditionEditor>
          </ou-form-item>
          <ou-form-item label="脱敏数据：" prop="maskingData" style="margin-bottom: 0px !important">
            <div class="masking-data-list">
              <ou-table
                :data="dialogForm.maskingData"
                class="masking-table-list"
                :height="dialogForm.maskingData.length >= 5 ? 239 : dialogForm.maskingData.length <= 4 ? 199 : 'auto'"
                empty-default>
                <ou-table-column label="序号" type="index" width="44" align="center" />
                <ou-table-column prop="date" label="数据标签">
                  <template #default="scope">
                    <ou-select
                      v-model="scope.row.sensitiveTypeName"
                      key="sensitiveTypeNameSelect"
                      v-if="scope.row.edit"
                      placeholder="请选择"
                      @change="selectType(scope.$index, scope.row)"
                      :options="labelList"
                      filterable></ou-select>
                    <span v-else style="padding-left: 8px">{{ sensitiveList[scope.row.sensitiveTypeName].name }}</span>
                  </template>
                </ou-table-column>
                <ou-table-column label="脱敏规则">
                  <template #default="scope">
                    <ou-select
                      v-model="scope.row.rule"
                      placeholder="请选择"
                      filterable
                      :options="ruleList"
                      v-if="scope.row.edit"></ou-select>
                    <ou-text-ellipsis
                      style="padding-left: 8px"
                      v-else
                      :content="ruleEnum[scope.row.rule]?.ruleName"></ou-text-ellipsis>
                  </template>
                </ou-table-column>
                <ou-table-column label="样本数据">
                  <template #default="scope">
                    <ou-input v-model="scope.row.sample" v-if="scope.row.edit" :clearable="false">
                      <template #suffix>
                        <span
                          @click="testMasking(scope.row, scope.$index)"
                          :class="
                            !scope.row.sample || !scope.row.rule || !scope.row.sensitiveTypeName
                              ? 'no-click'
                              : 'input-text-btn'
                          ">
                          测试
                        </span>
                      </template>
                    </ou-input>
                    <ou-text-ellipsis v-else style="padding-left: 8px" :content="scope.row.sample"></ou-text-ellipsis>
                  </template>
                </ou-table-column>
                <ou-table-column label="脱敏效果">
                  <template #default="scope">
                    <ou-text-ellipsis style="padding-left: 8px" :content="scope.row.result"></ou-text-ellipsis>
                  </template>
                </ou-table-column>
                <ou-table-column label="操作" width="100">
                  <template #default="scope">
                    <ou-button
                      type="primary"
                      text
                      v-if="scope.row.edit"
                      @click="saveMaskingItem(scope.$index)"
                      :disabled="maskingItemDisabled">
                      确定
                    </ou-button>
                    <ou-button
                      type="primary"
                      text
                      v-if="!scope.row.edit"
                      @click="editMaskingItem(scope.$index)"
                      :disabled="addBtnDisabled">
                      编辑
                    </ou-button>
                    <ou-button type="primary" text v-if="!scope.row.edit" @click="removeMasking(scope.$index)">
                      删除
                    </ou-button>
                    <ou-button type="primary" text v-if="scope.row.edit" @click="cancelHandle(scope.$index)">
                      取消
                    </ou-button>
                  </template>
                </ou-table-column>
              </ou-table>
              <div class="masking-data-list_btn">
                <ou-button type="primary" style="display: flex" text @click="addMaskingData" :disabled="addBtnDisabled">
                  <ou-icon><Plus /></ou-icon>
                  <span>添加</span>
                </ou-button>
                <div style="margin-left: 16px">
                  <span>已配置</span>
                  <span>{{ dialogForm.maskingData.length }}</span>
                  <span>条，还可添加</span>
                  <span>{{ 100 - dialogForm.maskingData.length }}</span>
                  <span>条</span>
                </div>
              </div>
            </div>
          </ou-form-item>
        </ou-form>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <ou-button @click="cancel">取消</ou-button>
          <ou-button type="primary" @click="submit">确定</ou-button>
        </div>
      </template>
    </ou-opera-dialog>
  </div>
</template>

<script setup>
  import { defineProps, defineEmits, watch, nextTick, computed, ref } from "vue";

  import MoveGroup from "@/components/MoveGroup";
  import { deepClone } from "@/utils/tool.js";
  import { testSampleData } from "@/api/pgModule.js";
  import { OuModal } from "@ouryun/ouryun-plus";
  import MatchedConditionEditor from "@/components/MatchedConditionEditor";
  import { getTextInfo } from "@/utils/MatchedCondition.js";
  import { useTacticsForm } from "@/views/plugins/data-masking/components/hook";
  const { rules, dialogForm, DialogForm } = useTacticsForm();
  const emits = defineEmits(["update:list"]);
  const props = defineProps({
    options: {
      type: Object,
      default: () => {},
    },
    list: {
      //白名单列表
      type: Array,
      default: () => [],
    },
  });
  const dialogTitle = ref("");
  const submitType = ref("");
  const tablesRef = ref(null);
  const matchRef = ref(null);
  const tacticsRef = ref(null);
  const isShowDialog = ref(false);
  const tmData = ref(null);
  const tmRuleList = ref(null);
  const oldMaskingData = ref([]);
  const handleMaskingIndex = ref(0);
  const sensitiveList = ref({});
  const dataLabelList = ref([]);
  const initDataLabeList = ref([]);
  const ruleList = ref([]);
  const ruleEnum = ref({});

  const table = ref({
    multipleList: [],
    list: [],
    head: [],
  });
  const isCheckData = ref(true);
  const handleIndex = ref(0);
  watch(
    () => isShowDialog.value,
    (nData, oData) => {
      if (!nData) {
        nextTick(() => {
          dialogForm.value = new DialogForm();
          closeDialog();
        });
      } else {
        initDataLabeList.value = deepClone(dataLabelList.value);
      }
    },
  );
  watch(
    () => [props.list, props.options],
    (nData, oData) => {
      if (nData[0].length && nData[1]) {
        tmData.value = props.options;
        table.value.list = props.list;
        // console.log("table.value.list", table.value.list);
      }
    },
  );
  watch(tmData, (nData, oData) => {
    if (nData) {
      sensitiveList.value = nData.identityRule.reduce((acc, cur) => {
        acc[cur.id] = cur;
        return acc;
      }, {});
      dataLabelList.value = nData.identityRule.map(item => ({
        label: item.name,
        value: item.id,
      }));
      ruleEnum.value = nData.desensitization.reduce((acc, cur) => {
        acc[cur.id] = cur;
        return acc;
      }, {});
      tmRuleList.value = nData.desensitization;
    }
  });
  const labelList = computed(() => {
    const list = dialogForm.value.maskingData.filter(e => !e.edit).map(e => e.sensitiveTypeName);
    return initDataLabeList.value.filter(v => !list.includes(v.value));
  });
  const showDialog = (type, row, index) => {
    const formType = {
      add: "添加",
      edit: "编辑",
      copy: "复制",
    };
    submitType.value = type;
    handleIndex.value = index;
    dialogTitle.value = `${formType[type]}脱敏策略`;
    if (row) {
      const {
        sensitiveTypeName,
        rule,
        result,
        sample,
        maskingData = [
          {
            sensitiveTypeName,
            rule,
            edit: false,
            sample,
            result,
          },
        ],
      } = row;
      const rowData = {
        ...row,
        maskingData,
      };
      dialogForm.value = deepClone(rowData);
      console.log("dialogForm.value", dialogForm.value, "------------");
    }
    if (type === "copy") dialogForm.value.ruleName = `${row.ruleName}_复制`;
    isShowDialog.value = true;
  };
  //选择数据标签
  const selectType = (index, row) => {
    setRuleSelectList(row.sensitiveTypeName);
    dialogForm.value.maskingData[index] = {
      ...dialogForm.value.maskingData[index],
      rule: "",
      sample: "",
      result: "",
    };
  };

  const setRuleSelectList = dmName => {
    const list = tmRuleList.value.filter(e => e.data_label_id == dmName);
    ruleList.value = list.map(item => ({
      label: item.ruleName,
      value: item.id,
    }));
  };
  const routeMoved = (type, list) => {
    table.value.list = list;
    nextTick(() => {
      table.value.multipleList.forEach(e => {
        tablesRef.value.toggleRowSelection(e);
      });
      updateData();
    });
  };
  //多选方法
  const handleSelectionChange = val => {
    isCheckData.value = !val.length;
    if (val.length) {
      table.value.multipleList = val;
    }
  };
  const initData = () => {
    if (props.list.length && props.options) {
      nextTick(() => {
        tmData.value = props.options;
        table.value.list = props.list;
      });
    }
  };
  initData();
  //置顶
  const top = (row, index) => {
    if (index >= 0 && index < table.value.list.length) {
      const element = table.value.list.splice(index, 1)[0];
      table.value.list.unshift(element);
    }
  };
  const remove = (row, index) => {
    table.value.list.splice(index, 1);
  };
  //批量删除
  const batchRemove = () => {
    const content = {
      content: `确定要批量删除${table.value.multipleList.length}条敏感策略信息吗？`,
      moreContent: `删除后将清理掉脱敏策略信息。`,
    };
    OuModal.confirm(content).then(() => {
      const delList = table.value.multipleList.map(e => e.ruleName);
      const filteredItems = table.value.list.filter(item => !delList.includes(item.ruleName));
      table.value.list = filteredItems;
      updateData();
    });
  };
  //表单确认操作
  const handelData = () => {
    const forms = deepClone(dialogForm.value);
    const isExist = table.value.list.find(item => item.ruleName === forms.ruleName);
    //修改操作
    if (submitType.value === "edit") {
      table.value.list.splice(handleIndex.value, 1, forms);
      isShowDialog.value = false;
      return;
    }
    //查重操作
    if (submitType.value === "add" && isExist) {
      OuModal.warning(`策略名称 (${forms.ruleName}) 已存在`);
      return;
    }
    //新增操作
    table.value.list.unshift({
      ...dialogForm.value,
      enable: true,
    });
    isShowDialog.value = false;
  };
  const submit = () => {
    tacticsRef.value.validate(valid => {
      if (!valid) return;
      handelData();
      updateData();
    });
  };
  const closeDialog = () => {
    tacticsRef.value.resetFields();
  };
  const cancel = () => {
    isShowDialog.value = false;
  };
  const updateData = () => {
    emits("update:list", table.value.list);
  };
  const addMaskingData = () => {
    dialogForm.value.maskingData.push({
      sensitiveTypeName: "",
      maskingData: [],
      rule: "",
      edit: true,
      sample: "",
      result: "",
    });
  };
  //删除脱敏数据
  const removeMasking = index => {
    dialogForm.value.maskingData.splice(index, 1);
  };
  //测试脱敏结果
  const testMasking = (row, index) => {
    const { rule, sample, sensitiveTypeName } = row;
    if (!rule || !sample || !sensitiveTypeName) return;
    const tmItem = tmRuleList.value.find(e => (e.id || e.name) === rule);

    if (tmItem?.replace?.type == 3) {
      let value = tmItem.replace?.sample?.id || tmItem.replace?.sample?.name; // 当前的一条数据
      let sample = tmData.value.samples?.find(e => e.id === value || e.name === value); // sample中查找
      tmItem.replace.sample.content = sample?.content; // 替换成sample中的内容
    }
    const data = {
      ruleId: `${rule}`,
      sampleData: sample,
      targetRule: tmItem,
    };
    testSampleData(data)
      .then(res => {
        const {
          data: { result },
        } = res;

        dialogForm.value.maskingData[index].result = result;
      })
      .finally(() => {});
  };
  //保存单条数据
  const saveMaskingItem = index => {
    dialogForm.value.maskingData[index].edit = false;
  };
  //脱敏数据编辑状态
  const editMaskingItem = index => {
    oldMaskingData.value = deepClone(dialogForm.value.maskingData);
    setRuleSelectList(dialogForm.value.maskingData[index].sensitiveTypeName);
    dialogForm.value.maskingData[index].edit = true;
  };
  //添加脱敏数据添加按钮状态
  const addBtnDisabled = computed(() => {
    return dialogForm.value.maskingData.some(e => e.edit) || dialogForm.value.maskingData.length >= 100;
  });
  //脱敏数据确定按钮状态
  const maskingItemDisabled = computed(() => {
    return dialogForm.value.maskingData.some(e => !e.sensitiveTypeName || !e.rule);
  });
  //取消
  const cancelHandle = index => {
    const newLow = oldMaskingData.value[index];
    if (newLow) {
      dialogForm.value.maskingData[index] = newLow;
      dialogForm.value.maskingData[index].edit = false;
    } else {
      dialogForm.value.maskingData.splice(index, 1);
    }
  };
  //配置单行脱敏数据
  const configMaskingData = row => {
    let resultText = [];
    if (row.maskingData) {
      resultText = row.maskingData.map(e => (e.sensitiveTypeName ? sensitiveList.value[e.sensitiveTypeName].name : ""));
    } else {
      const name = sensitiveList.value[row.sensitiveTypeName]
        ? sensitiveList.value[row.sensitiveTypeName].name
        : row.sensitiveTypeName;
      resultText = [name];
    }
    return `${resultText.length}（${resultText.join("，")}）`;
  };
</script>

<style scoped lang="scss">
  .tactics {
    width: 100%;
  }
  .form-content-width {
    width: 300px !important;
  }
  :deep(.ou-opear-content) {
    .ouryun-form-item {
      margin-bottom: 20px !important;
    }
  }
  .list-handle {
    height: 32px;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
  }
  .input-text-btn {
    color: var(--ouryun-color-brand-base);
    cursor: pointer;
  }
  .no-click {
    color: #9ec5fb;
    cursor: not-allowed;
  }
  .masking-data-list {
    width: 100%;
    border: 1px solid #e7e7e7;
    border-radius: 4px;
    .masking-data-list_btn {
      height: 42px;
      display: flex;
      align-items: center;
      padding-left: 10px;
      border-top: 1px solid #e7e7e7;
    }
  }
  ::v-deep {
    .masking-table-list .ouryun-table__cell {
      padding: 0px;
    }
    .masking-table-list .ouryun-table .ouryun-table__header .cell {
      padding-left: 16px !important;
    }
  }
  .masking-table-list ::v-deep.ouryun-table .ouryun-table__header tr th.ouryun-table__cell:not(:last-child) .cell {
    padding-left: 16px !important;
  }
  .masking-table-list ::v-deep.ouryun-table .cell {
    padding: 0px 0px 0px 8px !important;
  }
</style>
