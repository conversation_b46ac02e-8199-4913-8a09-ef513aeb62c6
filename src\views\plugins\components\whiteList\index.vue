<template>
  <div class="container">
    <div class="whiteList-box">
      <div class="whiteList-data">
        <div class="whiteList-data-handle">
          <ou-button type="primary" text @click="showAdd">添加</ou-button>
          <ou-button type="primary" text @click="importAdd">导入</ou-button>
        </div>
        <ou-table
          class="editor"
          :data="whiteListData"
          style="width: 100%; --table-empty-size: 120px"
          :height="whiteListData.length >= 5 ? 245 : whiteListData.length <= 4 ? 205 : 'auto'"
          :loading="isTableLoading"
          ref="tables">
          <ou-table-column type="index" label="序号" width="44" align="center"></ou-table-column>
          <ou-table-column prop="companyName" label="名单名称">
            <template #default="scope">
              <ou-input v-if="scope.row.edit" v-model="scope.row.companyName" placeholder="请输入内容"></ou-input>
              <ou-text-ellipsis v-else :content="scope.row.companyName" style="margin-left: 8px"></ou-text-ellipsis>
            </template>
          </ou-table-column>
          <ou-table-column prop="status" label="状态">
            <template #default="scope">
              <ou-switch
                v-model="scope.row.status"
                active-text="启用"
                size="small"
                inactive-text="禁用"
                class="switch-offside"></ou-switch>
            </template>
          </ou-table-column>
          <ou-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <div v-if="scope.row.edit">
                <ou-button type="primary" text @click="saveListName(scope.row, scope.$index)">保存</ou-button>
                <ou-button type="primary" text @click="cancel(scope.row, scope.$index)">取消</ou-button>
              </div>
              <div v-if="!scope.row.edit">
                <ou-button type="primary" text @click="handleWhiteUpdate(scope.row, scope.$index)">编辑</ou-button>
                <ou-button type="primary" text @click="showIpList(scope.row, scope.$index)">查看IP地址</ou-button>
                <ou-button type="primary" text @click="handleWhiteDelete(scope.row, scope.$index)">删除</ou-button>
              </div>
            </template>
          </ou-table-column>
        </ou-table>
      </div>
    </div>
    <ou-opera-dialog
      :title="dialogConfig.title[dialogConfig.enums]"
      @close="closeWhiteDialog"
      v-model="dialogConfig.isShow"
      :z-index="996"
      draggable
      hideFooter
      :width="dialogConfig.enums === 'showIp' ? '900' : step == 'importResult' ? '528' : '700'">
      <template #content>
        <div v-if="dialogConfig.enums !== 'showIp'">
          <ou-form
            v-if="step === 'form'"
            :model="whiteItemForm"
            labelPosition="right"
            ref="whiteItemFormRef"
            :rules="whiteItemRules"
            :label-width="labelWidth">
            <ou-form-item prop="companyName" label="名单名称：">
              <ou-input
                placeholder="请输入"
                v-model="whiteItemForm.companyName"
                maxlength="50"
                show-word-limit></ou-input>
            </ou-form-item>
            <ou-form-item
              prop="url"
              label="IPv4地址："
              v-if="dialogConfig.enums !== 'importWhite'"
              style="margin-bottom: 0px">
              <OuTextArea
                style="width: 100%"
                :placeholder="placeholder"
                :validateDuplicate="true"
                :validateEditValue="changIp"
                :allowSpaces="false"
                emptyMessage="IPv4不能为空"
                ref="urlTextAreaRef"
                :maxLines="256"
                :validateEmpty="true"
                :allowEmptyLines="false"
                v-model:defaultValue.sync="whiteItemForm.url" />
            </ou-form-item>
            <ou-form-item
              prop="file"
              style="margin-bottom: 0px"
              class="companyName"
              label="上传文件："
              v-if="dialogConfig.enums === 'importWhite'">
              <div class="content-tip">请先下载导入模板，并严格按模板要求填写信息后导入，否则信息可能导入失败；</div>
              <ou-button type="primary" text @click="downExcel" style="padding: 8px 0px">下载Excel模板</ou-button>
              <ou-drag-upload
                @getData="getFileData"
                @on-change="getImportFile"
                :fizeSize="1"
                ref="myUpload"
                fileType=".xlsx, .xls, .txt"
                tip="请上传 xlsx、xls、txt的文件，最大1M"></ou-drag-upload>
              <!-- <Upload
                @getData="getFileData"
                ref="myUpload"
                placeholder="仅限上传 xlsx、xls、csv、txt 文件， 最大1M"></Upload> -->
            </ou-form-item>
          </ou-form>
        </div>
        <div class="import_result" v-if="step == 'importResult'">
          <div v-if="result.state === 1">
            <ou-icon class="icon" color="#62BF78"><SuccessFilled /></ou-icon>
            <div class="tip">导入成功</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <div v-else-if="result.state === 2">
            <ou-icon class="icon" color="#ffc53d"><WarningFilled /></ou-icon>
            <div class="tip">部分导入失败</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <div v-else>
            <ou-icon class="icon" color="#ff0b0b"><CircleCloseFilled /></ou-icon>
            <div class="tip">导入失败</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <!-- <el-result :icon="result.icon" :title="result.title" :sub-title="result.text"></el-result> -->
        </div>
        <div v-if="dialogConfig.enums === 'showIp'">
          <div class="handle-show-ip">
            <div class="search-ip">
              <ou-input
                v-model="searchIp"
                style="width: 300px"
                :clearable="false"
                class="ip-sreach"
                placeholder="请输入IP地址搜索">
                <template #suffix>
                  <ou-icon :size="16" @click="likeSearchIp" style="cursor: pointer"><Sousuo /></ou-icon>
                </template>
              </ou-input>
            </div>
            <div class="handle-ip-btn">
              <ou-button :disabled="!multipleIpList.length" @click="batchDelete">批量删除</ou-button>
              <ou-button @click="clearableIpList">清空</ou-button>
              <ou-button @click="exportIpList" plain class="pn-info-btn">导出</ou-button>
              <ou-button @click="showImportIpList" plain class="pn-info-btn">导入</ou-button>
              <ou-button type="primary" @click="showAddIpList">新增</ou-button>
            </div>
          </div>
          <ou-table
            class="editor"
            :data="tableData"
            style="width: 100%; --table-empty-size: 120px"
            @selection-change="handleSelectionChange">
            <ou-table-column type="selection" width="32" align="center"></ou-table-column>
            <ou-table-column type="index" label="序号" width="60" align="center"></ou-table-column>
            <ou-table-column prop="IPV4" label="IPv4地址">
              <template #default="scope">
                <span v-if="!scope.row.edit" style="margin-left: 8px">
                  {{ scope.row.ip }}
                </span>
                <ou-input v-if="scope.row.edit" v-model="scope.row.ip" placeholder="请输入内容"></ou-input>
              </template>
            </ou-table-column>
            <ou-table-column label="操作" width="100">
              <template #default="scope">
                <div v-if="scope.row.edit">
                  <ou-button type="primary" text @click="saveIpItem(scope.row, scope.$index)">保存</ou-button>
                  <ou-button type="primary" text @click="cancelIpItem(scope.row, scope.$index)">取消</ou-button>
                </div>
                <div v-if="!scope.row.edit">
                  <ou-button type="primary" text @click="editIpItem(scope.row, scope.$index)">编辑</ou-button>
                  <ou-button type="primary" text @click="deleteIpItem(scope.row, scope.$index)">删除</ou-button>
                </div>
              </template>
            </ou-table-column>
          </ou-table>
          <div class="pagination-container" v-if="ipListTotal > 10">
            <ou-pagination
              layout="total, sizes,prev, pager, next, jumper"
              :total="ipListTotal"
              size="small"
              v-model:page="queryParams.currentPage"
              v-model:limit="queryParams.PageSize"
              :limit="10"></ou-pagination>
          </div>
        </div>
      </template>
      <template #footer>
        <div v-if="step === 'importResult'">
          <ou-button v-if="result.icon === 'warning' || result.icon === 'success'" @click="importOver">完成</ou-button>
          <ou-button v-if="result.icon === 'error'" @click="dialogConfig.isShow = false">结束</ou-button>
          <ou-button type="primary" v-if="result.icon === 'error' || result.icon === 'success'" @click="step = 'form'">
            继续导入
          </ou-button>
          <ou-button type="primary" v-if="result.icon === 'warning'" @click="downFail">下载失败数据</ou-button>
        </div>
        <div v-else>
          <ou-button @click="dialogConfig.isShow = false">取消</ou-button>
          <ou-button type="primary" @click="confirmWhiteListItem" style="margin-left: 10px">确定</ou-button>
        </div>
      </template>
    </ou-opera-dialog>
    <ou-opera-dialog
      :title="ipListDialog.title[ipListDialog.enums]"
      @close="closeIpListDialog"
      :z-index="998"
      draggable
      v-model="isShowIpHandle"
      :width="700"
      hideFooter>
      <template #content>
        <ou-form
          :model="ipListForm"
          labelPosition="right"
          ref="ipListFormRef"
          :rules="whiteItemRules"
          :label-width="labelWidth">
          <ou-form-item prop="ip" label="IPv4地址：" style="margin-bottom: 0px">
            <OuTextArea
              style="width: 100%"
              :placeholder="placeholder"
              :validateDuplicate="true"
              :validateEditValue="changIp"
              :allowSpaces="false"
              emptyMessage="IPv4不能为空"
              ref="urlTextAreaRef"
              :maxLines="256"
              :validateEmpty="true"
              :allowEmptyLines="false"
              v-model:defaultValue.sync="ipListForm.ip" />
          </ou-form-item>
        </ou-form>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <ou-button @click="isShowIpHandle = false">取消</ou-button>
          <ou-button type="primary" @click="confirmIpList">确定</ou-button>
        </div>
      </template>
    </ou-opera-dialog>
    <ou-opera-dialog title="导入白名单" draggable v-model="isImportWhite" width="528px" :hideFooter="true">
      <template #content>
        <div v-if="ipListStep === 'form'">
          <!-- <div>请先下载导入模板，并严格按模板要求填写信息后导入，否则信息可能导入失败；</div>
          <div class="export-tips upload-text">
            <div class="export-btn">
              <div class="pn-click-text" @click="downExcel">下载Excel模板</div>
            </div>
          </div> -->
          <div class="content-tip">请先下载导入模板，并严格按模板要求填写信息后导入，否则信息可能导入失败；</div>
          <ou-button type="primary" text @click="downExcel" style="padding: 8px 0px">下载Excel模板</ou-button>
          <ou-drag-upload
            @getData="getFileData"
            @on-change="getImportFile"
            :fizeSize="1"
            ref="myUpload"
            fileType=".xlsx, .xls, .txt"
            tip="请上传 xlsx、xls、txt的文件，最大1M"></ou-drag-upload>
        </div>
        <div class="import_result" v-if="ipListStep == 'importResult'">
          <div v-if="result.state === 1">
            <ou-icon class="icon" color="#62BF78"><SuccessFilled /></ou-icon>
            <div class="tip">导入成功</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <div v-else-if="result.state === 2">
            <ou-icon class="icon" color="#ffc53d"><WarningFilled /></ou-icon>
            <div class="tip">部分导入失败</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <div v-else>
            <ou-icon class="icon" color="#ff0b0b"><CircleCloseFilled /></ou-icon>
            <div class="tip">导入失败</div>
            <div class="desc">{{ result.text }}</div>
          </div>
          <!-- <el-result :icon="result.icon" :title="result.title" :sub-title="result.text"></el-result> -->
        </div>
      </template>
      <template #footer>
        <div v-if="ipListStep === 'form'">
          <ou-button @click="isImportWhite = false">取消</ou-button>
          <ou-button type="primary" @click="confirmImport">确定</ou-button>
        </div>
        <div v-else>
          <ou-button v-if="result.icon === 'warning' || result.icon === 'success'" @click="isImportWhite = false">
            完成
          </ou-button>
          <ou-button v-if="result.icon === 'error'" @click="isImportWhite = false">结束</ou-button>
          <ou-button type="primary" v-if="result.icon === 'error' || result.icon === 'success'" @click="step = 'form'">
            继续导入
          </ou-button>
          <ou-button type="primary" v-if="result.icon === 'warning'" @click="downFail">下载失败数据</ou-button>
        </div>
      </template>
    </ou-opera-dialog>
  </div>
</template>

<script setup>
  import { computed, ref, watch, defineProps, defineEmits, onMounted } from "vue";
  import { formatDate } from "@/utils/index.js";
  import { deepClone } from "@/utils/tool.js";
  import { validateIpAll } from "@/utils/validate.js";
  import * as XLSX from "xlsx";
  import { OuModal, dayjs, regex as REGEX } from "@ouryun/ouryun-plus";
  import { useState } from "../hook";
  import { exportExcelFile } from "@/utils/readfile.js";
  const { hasDuplicateKey, configUploadIPData } = useState();
  const emits = defineEmits(["update:list"]);
  const validateField = (rule, value, callback) => {
    if (!uploadFiles.value) {
      callback(new Error("请上传文件"));
    }
    callback();
  };
  const changIp = value => {
    if (!value) return { isValid: true, message: "" };
    if (validateIpAll(value)) {
      return { isValid: true, message: "" };
    } else {
      return { isValid: false, message: `IP不合法` };
    }
  };
  // const validateIp = (rule, value, callback) => {
  //   if (!value) {
  //     callback(new Error("IPv4不能为空"));
  //   } else {
  //     const newArr = value.filter(item => item.trim() !== "");
  //     if (newArr.length > MAX_LENGTH.value) {
  //       callback(new Error(`IP不能超过${MAX_LENGTH.value}条`));
  //     } else {
  //       for (let index = 0; index < newArr.length; index++) {
  //         const e = newArr[index];
  //         if (!validateIpAll(e)) {
  //           callback(new Error(`第${index + 1}行IP不合法：${e}`));
  //         }
  //       }
  //       callback();
  //     }
  //   }
  // };
  // import { handleWhiteList } from "@/api/plugin/index.js";
  const props = defineProps({
    list: {
      //白名单列表
      type: Array,
      default: [],
    },
    //文件名
    visitId: {
      type: String,
      default: "",
    },
  });
  const result = ref({});
  const query = ref(null);
  const isLoading = ref(false);
  const whiteListData = ref([]);
  const whiteAllData = ref([]);
  const fullName = ref("");
  const isImportWhite = ref(false);
  const isShowIpHandle = ref(false);
  const isTableLoading = ref(false);
  const labelWidth = ref("83px");
  const uploadFiles = ref(null);
  const searchIp = ref("");
  const placeholder = ref(
    `格式：每行一条数据，可输入多条数据，支持输入单个IP、IP范围、IP/掩码。示例如下：<br>单个IP：***********<br>IP范围：***********-100或***********-***********00(前三段必须相同，且结束IP需大于起始IP)<br>IP/掩码: ***********/24`,
  );
  const editItem = ref(null);
  const editIp = ref(null);
  const dialogConfig = ref({
    enums: "",
    isShow: false,
    title: {
      addWhite: "新增白名单",
      importWhite: "导入白名单",
      editWhite: "修改白名单",
      showIp: "查看IP地址",
    },
  });
  const ipListStep = ref("form");
  const step = ref("form");
  const ipListDialog = ref({
    enums: "",
    isShow: false,
    title: {
      addWhite: "新增白名单",
      importWhite: "导入白名单",
    },
  });
  const initImport = ref(true);
  const urlTextAreaRef = ref(null);
  const whiteItemFormRef = ref(null);
  const whiteItemForm = ref(null);
  const ipListFormRef = ref(null);
  const ipListForm = ref({
    ip: [],
  });
  const whiteItemRules = ref({
    companyName: [
      { required: true, message: "请输入名单名称", trigger: "change" },
      { required: true, message: "名单名称有误", trigger: "change", max: 50, pattern: REGEX.NO_SPECIAL },
      { required: true, message: "名单名称不能大于50", trigger: "change", max: 50 },
    ],
    url: [{ required: true, message: "", trigger: "change" }],
    ip: [{ required: true, message: "", trigger: "change" }],
    file: [{ required: true, validator: validateField, trigger: "change" }],
  });
  const total = ref(0);
  const queryParams = ref({
    currentPage: 1,
    PageSize: 10,
  });
  const tableHead = ref({
    data: [
      {
        label: "名单名称",
        prop: "companyName",
      },
      {
        label: "状态",
        prop: "status",
      },
    ],
    checkedOptions: ["companyName", "status", "createTime", "updateTime", "remark"],
  });
  const myUpload = ref(null);
  const myIpUpload = ref(null);
  const formIpData = ref("");
  const importResultData = ref(null);
  const ipUploadRef = ref(null);
  //操作后的ip
  const ipList = ref([]);
  //未操作前的ip
  const ipOldList = ref([]);
  //多选ip
  const multipleIpList = ref([]);
  //查看IP
  const showIpIndex = ref(0);
  //编辑IP下标
  const handleIpIndex = ref(0);
  const MAX_LENGTH = ref(256);
  //监听
  watch(
    () => queryParams.value.PageSize,
    (newData, oldData) => {
      if (newData !== oldData) {
        queryParams.value.currentPage = 1;
      }
    },
  );
  watch(
    () => dialogConfig.value.isShow,
    (newData, oldData) => {
      if (!newData && dialogConfig.value.enums === "importWhite") {
        // myUpload.value.clear();
      }
      if (!newData) {
        // step.value = "form";
        searchIp.value = "";
        uploadFiles.value = null;
      }
      if (newData) {
        step.value = "form";
      }
    },
  );
  watch(
    () => isImportWhite.value,
    (newData, oldData) => {
      if (!newData) {
        ipListStep.value = "form";
        uploadFiles.value = null;
      }
    },
  );
  watch(isShowIpHandle, (newVal, oldVal) => {
    ipListForm.value.ip = [];
    if (!newVal && ipListDialog.value.enums === "importWhite") {
      // myUpload.value.clear();
    }
  });
  watch(
    () => whiteListData.value,
    (newVal, oldVal) => {
      emits("update:list", newVal);
    },
    {
      deep: true,
    },
  );
  onMounted(() => {
    initData();
  });
  const importOver = () => {
    dialogConfig.value.isShow = false;
  };
  //分页数据
  const tableData = computed(() => {
    const currentPage = queryParams.value.currentPage;
    const pageSize = queryParams.value.PageSize;
    return ipList.value.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  });
  //当前操作下标
  const allHandleIndex = computed(() => {
    const index = handleIpIndex.value;
    const pageSize = queryParams.value.PageSize;
    const currentPage = queryParams.value.currentPage;
    return pageSize * (currentPage - 1) + index;
  });
  const ipListTotal = computed(() => {
    return ipList.value.length;
  });
  const initData = () => {
    whiteAllData.value = configWhiteList(props.list);
    whiteListData.value = configWhiteList(props.list);
    configWhiteItemForm();
    fullName.value = props.visitId;
  };
  const configWhiteList = data => {
    return data.map(item => {
      return {
        url: item.ipListStr,
        status: item.enabled,
        remark: item.desc,
        companyName: item.name,
        ...item,
      };
    });
  };
  //设置为最后一条page
  const setEntityLastPage = () => {
    const page = Math.ceil(this.whiteAllData.length / 10);
    queryParams.value.currentPage = page;
  };
  //筛选条件
  const select = val => {
    const { companyName, creationTimestamp, ip, status } = val;
    const regex = new RegExp(ip, "i");
    const THE_NEXT_DAY = 86400000;
    whiteListData.value = whiteAllData.value.filter(item => {
      return (
        (!companyName || item.companyName.toLowerCase().includes(companyName.toLowerCase())) &&
        (!status || item.status === Boolean(Number(status))) &&
        (!ip || regex.test(item.ipListStr)) &&
        (!creationTimestamp ||
          (item.updateTime * 1000 >= creationTimestamp[0] &&
            item.updateTime * 1000 <= creationTimestamp[1] + THE_NEXT_DAY))
      );
    });
    queryParams.value.currentPage = 1;
  };
  const configItemTime = time => {
    const times = time * 1000;
    return formatDate(times);
  };
  const configWhiteItemForm = () => {
    whiteItemForm.value = {
      companyName: "",
      status: true,
      url: [],
      edit: false,
    };
  };
  const uploadIp = fileData => {
    const resultData = configUploadIPData(fileData);
    const { fail, data } = resultData;
    importResultData.value = resultData;
    const ipStr = data || [];
    if (data.length > MAX_LENGTH.value) {
      OuModal.warning(`导入文件IP不能超过${MAX_LENGTH.value}条`);
      if (initImport.value) {
      } else {
        myIpUpload.value.clear();
      }
    }
    formIpData.value = ipStr;
  };
  const getImportFile = data => {
    const { type, files } = data;
    uploadFiles.value = type === "upload" ? JSON.stringify(data) : null;
  };
  const getUploadData = fileData => {
    uploadIp(fileData);
  };
  const getFileData = fileData => {
    uploadIp(fileData);
    if (initImport.value) {
      whiteItemForm.value.url = formIpData.value;
    } else {
      ipListForm.value.ip = formIpData.value;
    }
  };
  const confirmImport = () => {
    if (!uploadFiles.value) {
      OuModal.warning(`请上传文件`);
      return;
    }
    addImportIp();
    configResultText();
    ipListStep.value = "importResult";
    uploadFiles.value = null;
  };
  const addImportIp = () => {
    const IP_LIST_TOTAL = ipList.value.length;
    const ipListData = ipListForm.value.ip.filter(e => e);
    const addSum = ipListData.length + IP_LIST_TOTAL > MAX_LENGTH.value;
    const existSum = IP_LIST_TOTAL >= MAX_LENGTH.value;
    if (addSum || existSum) {
      importResultData.value.status = "error";
      importResultData.value.text = "IP添加个数已达上限，请先删除部分再进行添加";
      return;
    }
    let obj = null;
    ipListData.forEach(v => {
      const isExist = ipOldList.value.some(item => item.ip === v);
      if (!isExist) {
        obj = {
          ip: v,
          edit: false,
        };
        ipOldList.value.unshift(obj);
      } else {
        importResultData.value.status = "warning";
        const index = importResultData.value.data.findIndex(e => e === v);
        importResultData.value.data.splice(index, 1);
        importResultData.value.fail.push(v);
      }
    });
    ipList.value = deepClone(ipOldList.value);
    queryParams.value.currentPage = 1;
  };
  const getAllPlugin = val => {
    queryParams.value.PageSize = val.limit;
  };
  //显示修改弹框
  const handleWhiteUpdate = (row, index) => {
    editItem.value = deepClone(row);
    whiteListData.value[index].edit = true;
  };
  //查看Ip地址
  const showIpList = (row, index) => {
    showIpIndex.value = index;
    const list = (Array.isArray(row.ipList) ? row.ipList : row.ipList.split("\n")).map(v => ({ ip: v, edit: false }));
    ipList.value = deepClone(list);
    ipOldList.value = deepClone(list);
    dialogConfig.value.enums = "showIp";
    dialogConfig.value.isShow = true;
  };
  //模糊查找IP
  const likeSearchIp = () => {
    ipList.value = ipOldList.value.filter(item => {
      const regex = new RegExp(searchIp.value, "i");
      return regex.test(item.ip);
    });
  };
  //取消单行
  const cancel = (row, index) => {
    whiteListData.value[index].companyName = editItem.value.companyName;
    whiteListData.value[index].edit = false;
  };
  //显示添加弹框
  const showAdd = () => {
    dialogConfig.value.enums = "addWhite";
    dialogConfig.value.isShow = true;
  };
  //显示删除弹框
  const handleWhiteDelete = (row, index) => {
    const content = {
      content: `确认要删除${row.companyName}白名单吗？`,
      moreContent: "删除后将清理掉白名单信息。",
    };
    OuModal.confirm(content).then(() => {
      whiteListData.value.splice(index, 1);
    });
  };
  const downExcel = () => {
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const config = {
      header: ["IPv4地址"],
      fileName: `Ip白名单模板${formatTime}`,
    };
    exportExcelFile(config);
  };
  const importAdd = () => {
    initImport.value = true;
    dialogConfig.value.enums = "importWhite";
    dialogConfig.value.isShow = true;
  };
  // const onProgress = () => {};
  // const handleExceed = (files, fileList) => {
  //   OuModal.warning(
  //     `当前限制选择 ${this.fileLimit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
  //       files.length + fileList.length
  //     } 个文件`,
  //   );
  // };
  // // 上传
  // const upload = (params, key) => {
  //   this.File = params.file;
  // };
  const handleSelectionChange = val => {
    multipleIpList.value = val;
  };
  const showAddIpList = () => {
    ipListDialog.value.enums = "addWhite";
    isShowIpHandle.value = true;
  };
  //导出Ip
  const exportIpList = () => {
    const date = formatDate(new Date());
    const list = ipOldList.value.map(v => [v.ip]);
    const data = [["IPv4地址"], ...list];
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    XLSX.writeFile(workbook, `Ip白名单${date}.xlsx`);
  };
  const downFail = () => {
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const failData = importResultData.value.fail.map(e => ({
      ["IPv4地址"]: e,
    }));
    const config = {
      header: ["IPv4地址"],
      fileName: `Ip白名单模板${formatTime}`,
      data: failData,
    };
    exportExcelFile(config);
  };
  //导入IP列表
  const showImportIpList = () => {
    initImport.value = false;
    isImportWhite.value = true;
  };
  //清空IP
  const clearableIpList = () => {
    const content = {
      content: "确认要清空所有IP数据吗？",
      moreContent: "",
    };
    OuModal.confirm(content).then(() => {
      ipList.value = [];
      ipOldList.value = [];
      OuModal({
        type: "success",
        message: "清空成功!",
      });
    });
  };
  //批量删除ip
  const batchDelete = () => {
    multipleIpList.value.forEach(item => {
      deleteIpItem(item);
    });
  };
  //删除单个Ip
  const deleteIpItem = row => {
    const ipIndex = ipList.value.findIndex(v => v.ip === row.ip);
    const ipOldIndex = ipOldList.value.findIndex(v => v.ip === row.ip);
    ipList.value.splice(ipIndex, 1);
    ipOldList.value.splice(ipOldIndex, 1);
  };
  //保存单个Ip
  const saveIpItem = (row, index) => {
    const { ip } = row;
    const ipItem = hasDuplicateKey(ipList.value, "ip");
    if (ipItem) {
      OuModal.warning(`ip（${ipItem}）重复`);
      return;
    }
    if (validateIpAll(ip)) {
      const ipIndex = ipList.value.findIndex(v => v.ip === row.ip);
      const ipOldIndex = ipOldList.value.findIndex(v => v.ip === editIp.value.ip);
      ipOldList.value[ipOldIndex].ip = ip;
      ipList.value[ipIndex].edit = false;
    } else {
      OuModal.warning("请输入正确IP");
    }
  };
  //取消
  const cancelIpItem = (row, index) => {
    ipList.value[allHandleIndex.value].ip = editIp.value.ip;
    ipList.value[allHandleIndex.value].edit = false;
  };
  //编辑
  const editIpItem = (row, index) => {
    handleIpIndex.value = index;
    editIp.value = deepClone(row);
    ipList.value.forEach(e => {
      if (row.ip !== e.dit) {
        e.edit = false;
      }
    });
    const ipIndex = ipList.value.findIndex(e => e.ip === row.ip);
    ipList.value[ipIndex].edit = true;
  };
  const configResultText = () => {
    const { data, fail, status, text } = importResultData.value;
    const titleEnum = {
      warning: {
        state: 2,
        title: "部分导入失败",
        text: text || `导入成功${data.length}条，导入失败${fail.length}条，点击下载`,
      },
      success: {
        state: 1,
        title: "导入成功",
        text: text || `全部导入成功，共计${data.length}条`,
      },
      error: {
        state: 3,
        title: "导入失败",
        text: text || `文件数据有误，请重新核对后进行上传`,
      },
    };
    result.value = {
      icon: status,
      title: titleEnum[status].title,
      text: titleEnum[status].text,
      state: titleEnum[status].state,
    };
  };
  //白名单表单确定按钮
  const handleWhiteListItem = () => {
    if (dialogConfig.value.enums === "importWhite") {
      configResultText();
      if (result.value.icon !== "error") {
        whiteItemForm.value.url = formIpData.value;
        addWhiteData();
      }
      uploadFiles.value = null;
      step.value = "importResult";
    } else {
      step.value = "form";
      addWhiteData();
      dialogConfig.value.isShow = false;
    }
  };
  const addWhiteData = () => {
    const MAX_LIST_COUNT = 512;
    const { companyName, status, url } = whiteItemForm.value;
    const trueIp = url.filter(e => e);
    const setIp = [...new Set(trueIp)];
    const data = {
      companyName,
      ipList: setIp,
      status,
      edit: false,
    };
    if (whiteListData.value.length <= MAX_LIST_COUNT) {
      whiteListData.value.push(data);
    } else {
      OuModal.warning("名单添加个数已达上限，请先清理后再进行添加");
    }
  };
  //删除白名单
  const deleteWhiteList = data => {
    handleWhiteList(data, this.fullName, "delete").then(res => {
      const { data, code, message } = res;
      if (code === 200) {
        dialogConfig.value.isShow = false;
        const whiteData = data.data.metadata.annotations;
        setTableData(whiteData);
        if (!whiteListData.value.length) setEntityLastPage();
      } else {
        OuModal.warning(message);
      }
    });
  };
  const setTableData = list => {
    const data = list.ipWhitelist ? JSON.parse(list.ipWhitelist) : [];
    whiteAllData.value = configWhiteList(data);
    whiteListData.value = configWhiteList(data);
  };
  const addListIpv4 = () => {
    const ipList = ipOldList.value.map(v => v.ip);
    const newList = ipList.filter(v => validateIpAll(v));
    if (!newList?.length) {
      OuModal.warning("Ip地址不能为空");
      return;
    }
    whiteListData.value[showIpIndex.value].ipList = newList.join("\n");
    dialogConfig.value.isShow = false;
  };
  const confirmWhiteListItem = () => {
    const name = whiteItemForm.value.companyName;
    const isExist = whiteListData.value.find(e => e.companyName === name);
    if (isExist) {
      OuModal.warning(`名单名称（${name}）已存在`);
      return;
    }
    if (dialogConfig.value.enums === "showIp") {
      addListIpv4();
    } else {
      whiteItemFormRef.value.validate(valid => {
        if (dialogConfig.value.enums !== "importWhite") {
          const { isValid, message } = urlTextAreaRef.value.validateAll();
          if (!valid || !isValid) return;
        } else {
          if (!valid) return;
        }
        handleWhiteListItem();
      });
    }
  };
  const addIpList = () => {
    const IP_LIST_TOTAL = ipList.value.length;
    const ipListData = ipListForm.value.ip.filter(e => e);
    const addSum = ipListData.length + IP_LIST_TOTAL > MAX_LENGTH.value;
    const existSum = IP_LIST_TOTAL >= MAX_LENGTH.value;
    if (addSum || existSum) {
      OuModal.warning("IP添加个数已达上限，请先删除部分再进行添加");
      return;
    }
    let obj = null;
    ipListData.forEach(v => {
      const isExist = ipOldList.value.some(item => item.ip === v);
      if (!isExist) {
        obj = {
          ip: v,
          edit: false,
        };
        ipOldList.value.unshift(obj);
      }
    });
    ipList.value = deepClone(ipOldList.value);
    queryParams.value.currentPage = 1;
  };
  const confirmIpList = () => {
    const { isValid, message } = urlTextAreaRef.value.validateAll();
    if (!isValid) return;
    addIpList();
    isShowIpHandle.value = false;
  };
  const closeIpListDialog = () => {
    if (ipListDialog.value.enums !== "showIp") {
      ipListFormRef.value.resetFields();
    }
  };
  const closeWhiteDialog = () => {
    configWhiteItemForm();
    if (dialogConfig.value.enums !== "showIp" && step.value === "form") {
      whiteItemFormRef.value.resetFields();
    }
  };
  const saveListName = (row, index) => {
    const name = deepClone(row.companyName);
    const companyText = hasDuplicateKey(whiteListData.value, "companyName");
    const MAX_STR = 50;
    if (companyText) {
      OuModal.warning(`名单名称（${companyText}）已存在`);
      return;
    }
    if (!name) {
      OuModal.warning("名单名称不能为空");
      return;
    }
    if (name && name.length > MAX_STR) {
      OuModal.warning("名单名称不能大于50");
      return;
    }
    if (name && !REGEX.NO_SPECIAL.test(name)) {
      OuModal.warning("名单名称有误");
      return;
    }
    whiteListData.value[index].edit = false;
  };
  const configIpList = () => {
    const whiteList = whiteListData.value;
    if (!whiteList.length) return [];
    const data = whiteList.map(item => {
      const list = Array.isArray(item.ipList) ? item.ipList : item.ipList.split("\n");
      const ipRangeList = [];
      const defaultList = [];
      list.forEach(e => {
        if (e.includes("-")) {
          const rangeList = e.split("-");
          ipRangeList.push({
            start_ip: rangeList[0],
            end_ip: rangeList[1],
          });
        }
        if (e.includes("/")) {
          const rangeList = e.split("/");
          defaultList.push({
            address_prefix: rangeList[0],
            prefix_len: rangeList[1],
          });
        }
        if (!e.includes("/") && !e.includes("-")) {
          defaultList.push({
            address_prefix: e,
            prefix_len: 32,
          });
        }
      });
      return {
        name: item.companyName,
        enable: item.status,
        ip_list: {
          list: defaultList,
        },
        ip_range_list: ipRangeList,
      };
    });
    return data;
  };
  defineExpose({
    configIpList,
  });
</script>

<style scoped lang="scss">
  .container {
    width: 100%;
  }

  .companyName {
    :deep(.ouryun-form-item__label) {
      position: relative;
      top: -5px;
    }
  }
  .whiteList-box {
    background: #edeff4;
  }
  .whiteList-box-filter {
    overflow: hidden;
    width: 100%;
  }
  .whiteList-data {
    // padding: 20px;
    background: white;
  }
  .whiteList-data-handle {
    height: 32px;
    display: flex;
    align-items: center;
  }
  .export-tips {
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    .ept-tips {
      color: var(--ouryun-color-danger);
    }
  }

  .import_result {
    text-align: center;
    font-weight: 400;
    font-size: 16px;
    color: var(--ouryun-text-color-gray-1);
    margin-top: 8px;
    .icon {
      font-size: 50px;
    }

    .tip {
      margin: 20px 0 12px;
      font-weight: 400;
      font-size: 16px;
      color: var(--ouryun-text-color-gray-1);
      line-height: 24px;
    }

    .desc {
      font-weight: 400;
      font-size: 14px;
      color: var(--ouryun-text-color-gray-2);
      line-height: 21px;
    }
  }

  .content-tip {
    font-weight: 400;
    font-size: 14px;
    color: var(--ouryun-text-color-gray-2);
    line-height: 22px;
  }
  .handle-show-ip {
    display: flex;
    margin-bottom: 10px;
    justify-content: space-between;
    & > div {
      width: 50%;
    }
    .handle-ip-btn {
      white-space: nowrap;
      text-align: right;
    }
  }
  .upload-text {
    line-height: 20px;
  }
  .pagination-container {
    width: 100%;
    display: flex;
    margin-top: 12px;
    justify-content: end;
  }
  .pn-form-item-label-group {
    display: flex;
    align-items: center;
    column-gap: 4px;
  }
  .pn-pagination {
    margin-bottom: 0px;
  }
  :deep(.ouryun-table) {
    .ouryun-table__header tr th.ouryun-table__cell:not(:last-child) .cell {
      padding-left: 16px !important;
    }
    .ouryun-table__header tr th.ouryun-table__cell:first-child .cell {
      padding-left: 8px !important;
    }
  }
  :deep(.ouryun-table--enable-row-transition) {
    .ouryun-table__body td.ouryun-table__cell {
      padding: 0px !important;
    }
  }
</style>
