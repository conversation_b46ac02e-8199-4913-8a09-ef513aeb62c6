/**
 * 数组对象去重
 * @param {*} arr
 * @param {*} key
 * @returns
 */
export function uniqueFunc(arr, key) {
  const res = new Map();
  return arr.filter(item => !res.has(item[key]) && res.set(item[key], 1));
}

/**
 * 字符首字母转大写
 * @param {*} str
 * @returns
 */
export function capitalizeFirstLetter(str) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 对比两个数组是否一样
 * @param {*} arr1
 * @param {*} arr2
 * @returns
 */
export function compareArraysById(arr1, arr2) {
  if (arr1.length !== arr2.length) {
    return false;
  }
  for (let i = 0; i < arr1.length; i++) {
    const idToCompare = arr1[i].id;
    const foundElement = arr2.find(element => element.id === idToCompare);
    if (!foundElement || !compareObjects(arr1[i], foundElement)) {
      return false;
    }
  }

  return true;
}
function compareObjects(obj1, obj2) {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
}

export function areArraysEqual(arr1, arr2) {
  if (arr1.length !== arr2.length) {
    return false;
  }

  // 先对数组进行排序
  const sortedArr1 = arr1.slice().sort();
  const sortedArr2 = arr2.slice().sort();

  // 比较排序后的数组是否相等
  return sortedArr1.every((element, index) => element === sortedArr2[index]);
}

/**
 * 深拷贝
 * @param {*} obj
 * @returns
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    const arrCopy = [];
    for (let i = 0; i < obj.length; i++) {
      arrCopy[i] = deepClone(obj[i]);
    }
    return arrCopy;
  }

  const objCopy = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      objCopy[key] = deepClone(obj[key]);
    }
  }
  return objCopy;
}

// 截取分页列表
export function getPageList(arr, currentPage, pageSize) {
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return arr.slice(startIndex, endIndex);
}

//处理对象为空情况的初始值
export function replaceEmptyValues(obj, replacementValue) {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (obj[key] === "" || obj[key] === null || obj[key] === undefined || !obj[key]) {
        obj[key] = replacementValue;
      } else if (typeof obj[key] === "object") {
        obj[key] = replaceEmptyValues(obj[key], replacementValue);
      }
    }
  }
  return obj;
}

/**
 * 表单验证方法
 * @param {*} ref
 * @returns {boolean}
 */
export const validateForm = ref => {
  return ref.validate(valid => {
    return new Promise(resolve => {
      resolve(valid);
    });
  });
};

/**
 *验证IP
 * @param {String} ip
 * @returns
 */
export function getLastOctet(ip) {
  const lastOctet = ip.split("-");
  const firstIpv4 = lastOctet[0].split(".");
  const lastNode = firstIpv4[firstIpv4.length - 1];
  const lastIp = parseInt(lastNode);
  const IntervalIp = parseInt(lastOctet[1]);
  if (IntervalIp > lastIp) {
    return true;
  } else {
    return false;
  }
}

/**
 * 判断IP前三段是否相同
 * @param {*} ip1
 * @param {*} ip2
 * @returns
 */
export function areFirstThreeSegmentsEqual(ip1, ip2) {
  // 使用正则表达式提取IP地址的前三个段
  const regex = /^(\d+\.\d+\.\d+)/;
  const match1 = ip1.match(regex);
  const match2 = ip2.match(regex);
  if (match1 && match2) {
    // 比较提取的前三个段是否相等
    return match1[1] === match2[1];
  } else {
    // 处理无效的IP地址格式
    console.error("Invalid IP address format");
    return false;
  }
}

/**
 * 比较最后一位
 * @param {*} ip1
 * @param {*} ip2
 * @returns
 */
export function compareLastOctet(ip1, ip2) {
  const lastOctet1 = parseInt(ip1.split(".").pop());
  const lastOctet2 = parseInt(ip2.split(".").pop());
  if (lastOctet1 > lastOctet2) {
    return false;
  } else if (lastOctet1 < lastOctet2) {
    return true;
  } else {
    return false;
  }
}

// 复制文本
export function copytext({ id, className, text }, type = "input") {
  if (id) {
    text = document.getElementById(className).innerText;
  }
  if (className) {
    text = document.getElementsByClassName(className)[0].innerText;
  }
  const input = document.createElement(type);
  input.setAttribute("readonly", "readonly"); // 设置只读，否则移动端使用复制功能时可能会造成软件盘弹出
  input.value = text;
  document.body.appendChild(input);
  input.select(); // 选择对象
  document.execCommand("Copy"); // 执行浏览器复制命令
  input.onfocus = () => {
    stopKeyborad(el);
  };
  input.className = "input";
  input.style.display = "none";
}

//通用ip处理
export function parseIpEntry(ipList) {
  const ipHandleList = Array.isArray(ipList) ? ipList : ipList.split("\n").filter(e => e);
  let prefix_len = 32;
  const initIp = {
    ip_list: {
      list: [],
    },
    ipRangeList: [],
  };
  ipHandleList.forEach(entry => {
    if (entry.includes("-")) {
      const [startIp, endIp] = entry.split("-");
      initIp.ipRangeList.push({ startIp, endIp });
    }
    if (entry.includes("/")) {
      [address_prefix, prefix_len] = entry.split("/");
      prefix_len = parseInt(prefix_len, 10);
      initIp.ip_list.list.push({ address_prefix: entry, prefix_len });
    }
    if (!entry.includes("-") && !entry.includes("/")) {
      initIp.ip_list.list.push({ address_prefix: entry, prefix_len });
    }
  });
  return initIp;
}
