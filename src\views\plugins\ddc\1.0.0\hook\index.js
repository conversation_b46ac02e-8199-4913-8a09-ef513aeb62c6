import { reactive, ref, computed } from "vue";
import { regex } from "@ouryun/ouryun-plus";

export default function () {
  const form = reactive({
    configRule: [],
  });
  const formRef = ref(null);

  const validateConfigRule = (rule, value, callback) => {
    if (!value.length) {
      callback(new Error("请添加配置规则"));
    } else {
      callback();
    }
  };

  const formRules = {
    configRule: [{ required: true, trigger: [], validator: validateConfigRule }],
  };

  const selectionsList = ref([]);

  const dialogShow = ref(false);

  const editType = ref("添加");

  const dialogFormRef = ref(null);
  const matchedConditionEditorRef = ref(null);

  const actOptions = [
    {
      label: "拒绝",
      value: "DENY",
    },
    {
      label: "仅记录",
      value: "RECORD",
    },
  ];

  const typeOptions = [
    {
      label: "文本",
      value: 1,
    },
    {
      label: "HTML",
      value: 2,
    },
    {
      label: "JSON",
      value: 3,
    },
  ];

  const labelOptions = [
    {
      label: "文件名称",
      value: "file_name",
    },
    {
      label: "文件格式",
      value: "file_suffix",
    },
    {
      label: "文件大小",
      value: "file_size",
    },
  ];

  const logicOptions = [
    {
      label: "包含",
      value: "isBelongTrue",
    },
    {
      label: "不包含",
      value: "isBelongFalse",
    },
  ];

  const symbolOptions = [
    {
      label: ">",
      value: "GT",
    },
    {
      label: "<",
      value: "LT",
    },
    {
      label: "≥",
      value: "GE",
    },
    {
      label: "≤",
      value: "LE",
    },
    {
      label: "=",
      value: "EQ",
    },
  ];

  const unitOptions = ["B", "KB", "MB", "GB", "TB"];

  const validateMatch = (rule, value, callback) => {
    if (value.length > 10) {
      const message = "规则条数已超过上限10条，请进行清理后再提交";
      callback(new Error(message));
    } else {
      callback();
    }
  };

  const dialogFormRules = {
    rule_name: [{ required: true, trigger: ["change", "blur"], message: "请输入规则名称" }],
    file_info: [{ required: true, trigger: [] }],
    matchers: [{ required: false, trigger: [], validator: validateMatch }],
    type: [{ required: true, trigger: ["change", "blur"], message: "请选择类型" }],
  };

  class DialogForm {
    rule_name = "";
    file_info = [{ label: "", logic: "", matchContent: "", unit: "" }];
    matchers = [];
    act = "DENY";
    isDefault = false;
  }

  const dialogForm = reactive(new DialogForm());

  const tableRef = ref(null);

  const activeMatchDataIndex = ref(0);

  const matchingDataFormRef = ref(null);

  const matchingDataFormConfig = computed(() => {
    return [
      {
        prop: "label",
        type: () => "select",
        change: item => {
          item["logic"] = null;
        },
        visible: () => true,
        span: () => 1,
        validator: (item, index, callback) => {
          if (!item["label"]) callback(new Error("请选择数据"));
        },
        selectOptions: () => {
          labelOptions.forEach(option => {
            if (
              dialogForm.file_info.some(
                (ruleItem, ruleIndex) => ruleItem.label === option.value && ruleIndex !== activeMatchDataIndex.value,
              )
            ) {
              option.disabled = true;
            } else {
              option.disabled = false;
            }
          });
          return labelOptions;
        },
      },
      {
        prop: "logic",
        type: () => "select",
        change: item => {
          item["matchContent"] = "";
        },
        visible: item => item["label"],
        span: () => 1,
        validator: (item, index, callback) => {
          if (!item["logic"]) callback(new Error("请选择"));
        },
        selectOptions: item => {
          if (item["label"] === "file_name") {
            return logicOptions;
          } else if (item["label"] === "file_suffix") {
            return [{ label: "属于", value: "isBelongTrue" }];
          } else if (item["label"] === "file_size") {
            return symbolOptions;
          }
        },
      },
      {
        prop: "matchContent",
        type: () => "input",
        visible: item => item.logic,
        span: () => 1,
        validator: (item, index, callback) => {
          if (item["label"] === "file_name") {
            if (!item["matchContent"]) {
              callback(new Error("请输入"));
            } else if (!regex.NO_HZ.test(item["matchContent"])) {
              callback(new Error("输入英文、数字、英文标点符号"));
            }
          } else if (item["label"] === "file_suffix") {
            if (!item["matchContent"]) {
              callback(new Error("输入后缀名"));
            } else if (item["matchContent"].length > 4) {
              callback(new Error("最多输入4位字符"));
            }
          } else if (item["label"] === "file_size") {
            if (!item["matchContent"]) {
              callback(new Error("请输入"));
            } else if (!regex.NU_ZERO.test(item["matchContent"])) {
              callback(new Error("请输入正整数"));
            }
          }
        },
        attrs: item => {
          if (item["label"] === "file_name") {
            return {
              placeholder: "输入英文、数字、英文标点符号",
            };
          } else if (item["label"] === "file_suffix") {
            return {
              placeholder: "输入后缀名",
            };
          } else if (item["label"] === "file_size") {
            return {
              placeholder: "请输入",
            };
          }
        },
      },
      {
        prop: "unit",
        type: () => "select",
        visible: item => item["label"] === "file_size" && item["logic"],
        span: () => 1,
        validator: (item, index, callback) => {
          if (!item["unit"]) callback(new Error("请选择单位"));
        },
        attrs: () => ({ placeholder: "请选择单位" }),
        selectOptions: () =>
          unitOptions.map(unit => ({
            label: unit,
            value: unit,
          })),
      },
    ];
  });

  const matchDataCollapse = ref(true);
  const matchConditionCollapse = ref(true);

  // 列表项-匹配数据
  const getMatchDataTextInfo = matchDataList => {
    // console.log("matchDataList", matchDataList);
    const textArr = matchDataList.map(item => {
      let label = labelOptions.find(option => option.value === item.label)?.label;
      let logic = "",
        matchContent = item.matchContent;

      switch (item.label) {
        case "file_name":
          logic = logicOptions.find(option => option.value === item.logic)?.label;
          break;
        case "file_suffix":
          logic = "属于";
          break;
        case "file_size":
          logic = symbolOptions.find(option => option.value === item.logic)?.label;
          matchContent = `${item.matchContent}${item.unit}`;
          break;
      }

      const arr = [label, logic, matchContent];
      return `[${arr.join("，")}]`;
    });

    return textArr.join("，");
  };

  function convertValue(value, type = 1, unitName = "") {
    // 字节单位转换为B
    const units = ["B", "KB", "MB", "GB", "TB"];
    let result = Number(value); // 确保 result 是数字类型
    let currentUnitIndex = units.findIndex(i => i === unitName);
    while (currentUnitIndex > 0) {
      result = result * 1024;
      currentUnitIndex--;
    }
    return result;
  }
  // 获取匹配数据参数-c++使用
  const getMatchDataParameter = info => {
    let matchData = {};
    info.forEach(item => {
      const { label, logic, matchContent, unit } = item;
      if (label === "file_name") {
        matchData[label] = {
          value: matchContent,
          invert: logic === "isBelongFalse",
        };
      } else if (label === "file_suffix") {
        matchData[label] = {
          value: matchContent,
        };
      } else if (label === "file_size") {
        matchData[label] = {
          opt: logic,
          value: convertValue(matchContent, 3, unit),
        };
      }
    });
    return matchData;
  };

  return {
    form,
    formRef,
    formRules,
    selectionsList,
    dialogShow,
    editType,
    dialogFormRef,
    matchedConditionEditorRef,
    actOptions,

    dialogFormRules,
    DialogForm,
    dialogForm,
    tableRef,
    activeMatchDataIndex,
    matchingDataFormRef,
    matchingDataFormConfig,

    labelOptions,
    logicOptions,
    symbolOptions,
    matchDataCollapse,
    matchConditionCollapse,
    getMatchDataParameter,
    getMatchDataTextInfo,
  };
}
