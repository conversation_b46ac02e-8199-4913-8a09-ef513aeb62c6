import { regex as REGEX } from "@ouryun/ouryun-plus";
import { areFirstThreeSegmentsEqual, compareLastOctet, getLastOctet } from "./tool";

export function validateIpAll(ip) {
  const { IPV4_INTERVAL, IPV4_MASK, IPV4, IPV4_LAST_INTERVAL } = REGEX.IP;
  if (IPV4_INTERVAL.test(ip)) {
    const ipInter = ip.split("-");
    return areFirstThreeSegmentsEqual(ipInter[0], ipInter[1]) && compareLastOctet(ipInter[0], ipInter[1]);
  }
  if (IPV4_LAST_INTERVAL.test(ip)) {
    return getLastOctet(ip);
  }
  return IPV4.test(ip) || IPV4_MASK.test(ip) || IPV4_INTERVAL.test(ip) || IPV4_LAST_INTERVAL.test(ip);
}
