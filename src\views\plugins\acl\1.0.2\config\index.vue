<template>
  <!-- ACL -->
  <div class="acl-four-level">
    <ou-search-table
      ref="TableREF"
      :data="tables"
      v-loading="loading"
      v-model:form="searchForm"
      @select="select"
      @selectionChange="selectChange"
      :selectable="selectable"
      :total="total"
      :selectConfig="selectConfig"
      :tableConfig="tableConfig"
      operaWidth="120px"
      :tabHeids="tabHeids">
      <template #BtnLeft>
        <div class="move-container" style="display: flex; justify-content: center; align-items: center">
          <ou-button :disabled="isBatchAvailable" @click="batch('delete')">批量删除</ou-button>
          <ou-button :disabled="isBatchAvailable" @click="batch('switch', true)">批量启用</ou-button>
          <ou-button :disabled="isBatchAvailable" @click="batch('switch', false)">批量禁用</ou-button>
          <ou-move-group
            style="margin-left: 8px"
            :disabled="isBatchAvailable"
            :total="total"
            dialogLabel="移动到此优先级："
            placeholder="请输入优先级"
            :selections="selectedRow"
            @moved="routeMoved" />
          <!-- <moveGroup
            v-model:list="tables"
            :selections="selectedRow"
            @moved="list => routeMoved('route', list)"
            :disabled="isBatchAvailable"></moveGroup> -->
        </div>
      </template>
      <template #BtnRight>
        <ou-button type="primary" @click="showDialog('新增')">新增</ou-button>
        <ou-button :disabled="total.value > 100" @click="showImportDialog">导入</ou-button>
      </template>
      <template #scope="{ scope }">
        <div style="text-align: center; width: 42px" v-if="scope.item.prop === 'prior'">{{ scope.row.prior }}</div>
        <ou-switch
          size="small"
          v-else-if="scope.item.prop === 'enable'"
          :modelValue="scope.row.enable"
          @change="opera('switch', [scope.row], !scope.row.enable)"
          active-text="启用"
          inactive-text="禁用"
          class="switch-offside"></ou-switch>
        <ou-text-ellipsis v-else :content="getTableLabel(scope)"></ou-text-ellipsis>
      </template>
      <template #default="scope">
        <ou-button type="primary" @click="showDialog('编辑', scope.row, scope.$index)" text>编辑</ou-button>
        <template v-if="!scope.row.special">
          <ou-button type="primary" @click="opera('delete', [scope.row])" text>删除</ou-button>
        </template>
      </template>
    </ou-search-table>
    <editRule
      v-if="isShowDialog"
      :visible="isShowDialog"
      :type="submitType"
      :model-value="rowForm"
      @comfirm="submitDialog"
      @cancel="cancel" />
    <upload
      v-if="isShowUploadDialog"
      :visible="isShowUploadDialog"
      :model-value="AllTables"
      @comfirm="submitDialog"
      @close="isShowUploadDialog = false" />
  </div>
</template>
<script setup>
  import { onMounted, onBeforeUnmount, defineProps, ref, computed, nextTick, shallowRef } from "vue";
  import useTable from "./index.js";
  import editRule from "../components/editRule.vue";
  import upload from "../components/upload.vue";
  import { OuModal } from "@ouryun/ouryun-plus";
  import { getTableLabel, updateTable } from "./utils.js";
  import { saveLayer4PluginInstance } from "@/api/plugin.js";
  import usePlugin from "basePluginStore";

  const TableREF = shallowRef(null);
  const tables = ref([]);
  const total = ref(0);
  const AllTables = ref([]);
  let tableDateInstance = null;
  let { searchForm, tableConfig, tabHeids, selectConfig, TableData, updateTableRows } = useTable(TableREF);
  const loading = ref(false);

  const selectedRow = ref([]); // 所选插件配置批量/单个
  const isShowDialog = ref(false);
  const selectedIndex = ref(0);
  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
    matedata: {
      type: Object,
      default: () => ({}),
    },
  });

  const rowForm = ref();
  const submitType = ref("");

  const isShowUploadDialog = ref(false);

  const showDialog = (type, row, index) => {
    submitType.value = type;
    selectedIndex.value = index;
    rowForm.value = row || {};
    isShowDialog.value = true;
  };
  const cancel = () => {
    isShowDialog.value = false;
  };

  // 移动
  const routeMoved = ({ op_type, ids, index }) => {
    let temp =
      updateTable(
        {
          op_type,
          ids,
          rowIndex: +index,
        },
        AllTables.value,
      ) || [];
    submit(temp?.map((i, index) => ({ ...i, prior: index + 1 })));
    nextTick(() => {
      selectedRow.value.forEach(e => {
        TableREF.value?.tableREF.toggleRowSelection(e);
      });
    });
  };

  const showImportDialog = () => {
    if (total.value > 100) {
      OuModal.warning("规则条数已超过上限100条，请进行清理后再导入");
      return;
    }
    isShowUploadDialog.value = true;
  };

  const getList = async list => {
    loading.value = true;
    tableDateInstance = new TableData(list);
    const { table, sum } = tableDateInstance.create(searchForm);
    tables.value = table;
    total.value = sum;
    AllTables.value = tableDateInstance.allTables; // 所有数据
    loading.value = false;
  };

  const initPage = () => {
    const content = JSON.parse(props.content || props.global || "{}");
    console.log(content, "content");
    // let rules = JSON.parse() || [];
    let rules = JSON.parse(content?.plugin_config?.rules || "[]") || [];
    const list =
      rules.map((item, index) => {
        return {
          prior: item.prior,
          name: item.name,
          proto: item.proto,
          saddr: item.saddr,
          sport: item.sport,
          daddr: item.daddr,
          dport: item.dport,
          action: item.action,
          enable: item.enable,
          remark: content?.meta_data?.[index]?.remark || "",
          id: item.id,
        };
      }) || [];
    getList(list);
  };

  const submitDialog = (dialogForm, submitTxt) => {
    let temp = updateTableRows(AllTables.value, dialogForm, submitTxt);
    if (!temp) {
      return;
    }
    isShowDialog.value = false;
    submit(temp); // 调接口保存数据
    if (submitTxt == "新增") {
      searchForm.value.page = 1;
    }
  };

  onMounted(() => {
    initPage();
  });

  const submit = async temp => {
    // const params = {
    //   type: 1,
    //   rules: [], // c++ 需要的结构
    //   meta_data: [], // 页面显示的数据
    // };
    const params = {
      plugin_config: {
        type: 1,
        rules: [], // c++ 需要的结构
      },
      meta_data: [], // 页面显示的数据
    };
    let rules = [];
    temp.forEach(item => {
      const { remark, ...rest } = item;
      rules.push({ ...rest });
      params.meta_data.push({ remark });
    });
    params.plugin_config.rules = JSON.stringify(rules);
    // 参数处理
    let { id, display_name } = props.matedata;
    let body = {
      globalContent: JSON.stringify(params),
      display_name: display_name, //插件实例名称
      id: id, //插件实例id
    };
    saveLayer4PluginInstance(body)
      .then(res => {
        const { msg, code } = res;
        if (code === 200) {
          getList(temp);
        } else {
          OuModal.warning(msg);
        }
      })
      .finally(() => {
        // isSubmitLoading.value = false;
      });
  };

  const select = ({ type, form } = {}) => {
    searchForm = form;
    const { table, sum } = tableDateInstance.search({ ...form });
    tables.value = table;
    total.value = sum;
  };

  function selectChange(e) {
    selectedRow.value = e;
  }

  const isBatchAvailable = computed(() => !selectedRow.value?.length);

  const batch = (type, status) => {
    if (selectedRow.value.length === 0) {
      OuModal.warning("请选择要操作的数据");
      return;
    }
    opera(type, selectedRow.value, status);
  };

  const selectable = row => {
    return !row.special;
  };

  onBeforeUnmount(() => {
    tableDateInstance = null;
  });
  const opera = (type, row, status) => {
    let len = row?.length || 0;
    let content = {};
    switch (type) {
      case "switch":
        if (status) {
          content = {
            content: `${len > 1 ? `确定要启用选中的${len}条规则吗？` : "确定要启用该条规则吗？"}`,
            moreContent: `规则被启用后，将开始生效。`,
          };
        } else {
          content = {
            content: `${len > 1 ? `确定要禁用选中的${len}条规则吗？` : "确定要禁用该条规则吗？"}`,
            moreContent: `规则被禁用后，将不再生效。`,
          };
        }
        OuModal.confirm(content).then(() => {
          let temp = AllTables.value.map(i => {
            let currentRow = row.find(row => row.id === i.id);
            return { ...i, enable: currentRow?.id ? status : i.enable };
          });
          submit(temp);
          selectedRow.value = [];
          TableREF.value?.tableREF.clearSelection(); // 清空选中状态
        });
        break;
      case "delete":
        content = {
          content: `${len > 1 ? `确定要删除选中的${len}条规则吗？` : "确定要删除该条规则吗？"}`,
          moreContent: `规则删除后，将不能被使用，也不会生效。`,
        };
        OuModal.confirm(content).then(() => {
          // 删除列表项
          let temp = AllTables.value
            .filter(item => !row.includes(item))
            ?.map((i, index) => ({ ...i, prior: index + 1 }));
          submit(temp);
          selectedRow.value = [];
          TableREF.value?.tableREF.clearSelection(); // 清空选中状态
        });
        break;
    }
  };
</script>
<style lang="scss" scoped>
  .acl-four-level {
    height: 100%;
  }
  .primary-text {
    cursor: pointer;
    :deep(.tooltip_content) {
      color: var(--ouryun-color-brand-base);
    }
  }
  :deep(.move-container .ouryun-form.ouryun-form--default) {
    margin-bottom: 0px;
    // & > .ouryun-form-item-custom {
    //   margin-bottom: 20px;
    // }
  }
</style>
