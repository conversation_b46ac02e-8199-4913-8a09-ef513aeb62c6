<template>
  <div class="tm-pg-config add_conent_flex1">
    <ou-form labelPosition="right" ref="formRef" :model="baseForm" :rules="rules" :label-width="'140px'">
      <ou-form-item label="脱敏策略：">
        <TacticsList :options="tmData" v-model:list="baseForm.tacticsList"></TacticsList>
      </ou-form-item>
      <ou-form-item>
        <ou-button type="primary" text @click="changeHidden">
          {{ baseForm.isHidden ? "展开更多配置" : "隐藏以下配置" }}
          <ou-icon
            :style="{
              marginLeft: '4px',
              fontSize: '20px',
              position: 'relative',
              top: baseForm.isHidden ? '-1px' : '-2px',
            }">
            <CaretBottom v-if="baseForm.isHidden" />
            <CaretTop v-else />
          </ou-icon>
        </ou-button>
      </ou-form-item>
      <div v-if="!baseForm.isHidden">
        <ou-form-item label="白名单：">
          <WhiteList :options="tmData" :list="baseForm.whiteList"></WhiteList>
        </ou-form-item>
        <ou-form-item label="状态码：" class="form_item_status" prop="statusCode">
          <div style="width: 300px">
            <!-- <ou-input
              v-model="baseForm.statusCode"
              :autosize="{ minRows: 3, maxRows: 5 }"
              type="textarea"
              @input="handleInput"
              placeholder="请输入状态码，一行一个"></ou-input> -->
            <OuTextArea
              ref="textAreaRef"
              style="width: 100%"
              height="120"
              :isInfinity="true"
              :validateDuplicate="true"
              :validateEmpty="false"
              emptyMessage="请输入状态码"
              :validateEditValue="validateCodeStatus"
              v-model:defaultValue.sync="baseForm.status_codes"
              placeholder="请输入200-600（不包含600），一行一个" />
          </div>
          <ou-tooltip-icon
            style="position: absolute; left: 309px; top: 5px; font-size: 16px"
            trigger="hover"
            content="服务器返回给客户端的状态码，非必填，如果填写，则只有符合状态码的返回数据，才会被脱敏，默认为全部"></ou-tooltip-icon>
        </ou-form-item>
        <ou-form-item label="脱敏压缩数据：">
          <ou-radio-group v-model="baseForm.compress">
            <ou-radio label="1">启用</ou-radio>
            <ou-radio label="0">禁用</ou-radio>
          </ou-radio-group>
          <!-- <ou-tooltip
            class="box-item"
            style="position: relative; top: 1px; position: relative; top: -1px;  margin-left: 40px;"
            effect="light"
            placement="right"
            content="服务器返回的数据被压缩时，如果启用该功能，脱敏插件会先解压数据，再将数据脱敏，然后将脱敏后的数据进行压缩传输；如果禁用该功能，脱敏插件将不处理压缩类型的数据"
            width="400px">
            <ou-icon class="tip_icon_hover"><Notice /></ou-icon>
          </ou-tooltip> -->
          <ou-tooltip-icon
            style="position: relative; margin-left: 40px; font-size: 16px"
            trigger="hover"
            content="服务器返回的数据被压缩时，如果启用该功能，脱敏插件会先解压数据，再将数据脱敏，然后将脱敏后的数据进行压缩传输；如果禁用该功能，脱敏插件将不处理压缩类型的数据"></ou-tooltip-icon>
        </ou-form-item>
        <ou-form-item label="缓冲区大小限制：" prop="astrictSize.count">
          <ou-input
            style="max-width: 300px !important"
            placeholder="不填默认为1024KB"
            oninput="value=value.replace(/^\.+|[^\d.]/g,'')"
            v-model.number="baseForm.astrictSize.count"
            class="input-with-select">
            <template #append>
              <ou-select :clearable="false" style="width: 70px" v-model="baseForm.astrictSize.unit">
                <ou-option v-for="(v, k) in flowList" :key="k" :label="v" :value="k"></ou-option>
              </ou-select>
            </template>
          </ou-input>
          <!-- <ou-tooltip
            class="box-item"
            style="position: absolute; top: -1px; left: 309px"
            effect="light"
            placement="right"
            content="包含发送缓冲区和接收缓冲区的大小，合理设置缓冲区大小，可以提高网络数据的传输效率和可靠性，避免不必要的资源浪费和损失。默认值为1024KB，最小值为256KB，最大值为50MB"
            width="400px">
            <ou-icon class="tip_icon_hover"><Notice /></ou-icon>
          </ou-tooltip> -->

          <ou-tooltip-icon
            style="position: absolute; left: 309px; font-size: 16px"
            trigger="hover"
            content="包含发送缓冲区和接收缓冲区的大小，合理设置缓冲区大小，可以提高网络数据的传输效率和可靠性，避免不必要的资源浪费和损失。默认值为1024KB，最小值为256KB，最大值为50MB"></ou-tooltip-icon>
        </ou-form-item>
      </div>
    </ou-form>
  </div>
</template>
<script setup>
  import { ref, defineProps, defineExpose, shallowRef } from "vue";
  import TacticsList from "@/views/plugins/data-masking/components/TacticsList.vue";
  import WhiteList from "@/views/plugins/data-masking/components/WhiteList.vue";
  import { Notice, CaretBottom, CaretTop } from "@ouryun-plus/icons-vue";
  import { validateForm } from "@/utils/tool.js";
  import usePlugin from "basePluginStore";
  import { useState, initMaskingStrategy, configSubmitData, getTmList } from "../hook";
  const store = usePlugin();
  const { baseForm, flowList, tmData, rules } = useState();
  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
  });
  const formRef = ref(null);
  const resultForm = ref(null);
  const textAreaRef = ref(null);
  const handleInput = () => {
    // console.log("baseForm.value.statusCode", baseForm.value.statusCode);
    // baseForm.value.statusCode = baseForm.value.statusCode.replace(/[^\d\s]/g, "");
  };
  // 兼容升级数据： id匹配新数据，name匹配旧数据；最终返回id
  const compateData = (value, list) => {
    const tmItem = list.find(e => value === e.id || value === e.name);
    return tmItem ? tmItem.id : "";
  };

  // 校验状态码
  const validateCodeStatus = val => {
    if (!val) return { isValid: true, message: "" }; //避免回车换行时校验不通过
    if (!/^(2[0-9]{2}|[3-5][0-9]{2}|5[0-9]{2})$/.test(val)) return { isValid: false, message: `请输入正确的状态码` };
    else return { isValid: true, message: "" };
  };

  const initData = async () => {
    await getTmList();
    if (props.content) {
      const content = JSON.parse(props.content);
      let list = content.meta_data.form?.tacticsList || [];
      let desensitization = tmData.value.desensitization;
      console.log("list", JSON.parse(JSON.stringify(list)));
      list = list.map(e => {
        if (e.maskingData) {
          // 兼容旧数据-自定义规则
          e.maskingData = e.maskingData || [];
          e.maskingData.forEach(item => {
            item.rule = compateData(item.rule, desensitization);
          });
        }
        // console.log("e.rule", e.rule);
        e.rule = compateData(e.rule, desensitization);
        return e;
      });
      baseForm.value = { ...content.meta_data.form, status_codes: content.plugin_config.status_codes };
    } else {
      const tacticsData = initMaskingStrategy(tmData.value);
      resultForm.value = tacticsData;
      baseForm.value.tacticsList = tacticsData;
    }
  };

  const changeHidden = () => {
    baseForm.value.isHidden = !baseForm.value.isHidden;
  };
  initData();
  const submit = async () => {
    const isValid = await validateForm(formRef.value);
    if (!isValid) return;
    let areaValid = true;
    if (textAreaRef.value) {
      areaValid = textAreaRef.value.validateAll().isValid;
    }
    if (!areaValid) return;
    const { tacticsList } = baseForm.value;
    const params = {
      plugin_config: configSubmitData(baseForm.value),
      meta_data: {
        form: baseForm.value,
      },
    };
    const ipList = tacticsList.reduce((acc, cur) => {
      cur.matchRule.forEach(e => {
        if (e.field === "srcIp" && ["belongToIpGroup", "notBelongToIpGroup"].includes(e.logic)) {
          acc.push(...e.content);
        }
      });
      return acc;
    }, []);
    const extension = { refs: { ip_address_database: [], date_label_rule: [], data_masking_rules: [] } };
    extension.refs.ip_address_database = [...new Set(ipList)].map(item => {
      const ipGroup = store.ipGroupList.find(ipGroup => ipGroup.value === item);
      return {
        item_id: ipGroup.value,
        ...ipGroup,
      };
    });
    const labelDataList = tacticsList.reduce((acc, cur) => {
      const label_id = cur.maskingData
        ? cur.maskingData.map(e => Number(e.sensitiveTypeName))
        : [cur.sensitiveTypeName];
      acc.push(...label_id);
      return acc;
    }, []);
    extension.refs.date_label_rule = [...new Set(labelDataList)].map(item => {
      return {
        item_id: item,
      };
    });
    const ruleList = tacticsList.reduce((acc, cur) => {
      const rule_id = cur.maskingData ? cur.maskingData.map(e => Number(e.rule)) : [cur.rule];
      acc.push(...rule_id);
      return acc;
    }, []);
    extension.refs.data_masking_rules = [...new Set(ruleList)].map(item => {
      return {
        item_id: item,
      };
    });
    return {
      contentParams: params,
      extension,
    };
  };
  defineExpose({
    submit,
  });
</script>

<style scoped>
  .form_item_status .ouryun-form-item__content {
    position: relative;
  }
</style>
