<template>
  <div class="whiteList">
    <div class="list-handle">
      <ou-button type="primary" text @click="showDialog('add')">添加</ou-button>
    </div>
    <ou-table
      class="editor"
      :data="table.list"
      style="width: 100%; --table-empty-size: 120px"
      :height="!table.list.length ? 205 : table.list.length >= 5 ? 245 : 'auto'"
      ref="tables">
      <ou-table-column label="序号" type="index" width="44" align="center"></ou-table-column>
      <ou-table-column label="名单名称" prop="listName">
        <template #default="scope">
          <ou-text-ellipsis :content="scope.row.listName"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="条件类型">
        <template #default="scope">
          <ou-text-ellipsis :content="conditionTypeList[[scope.row.conditionType]]"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="IP/账号">
        <template #default="scope">
          <ou-text-ellipsis :content="configUserIp(scope.row)" />
        </template>
      </ou-table-column>
      <ou-table-column label="数据标签" prop="sensitiveType">
        <template #default="scope">
          <ou-text-ellipsis :content="configTypeName(scope.row.sensitiveType)"></ou-text-ellipsis>
        </template>
      </ou-table-column>
      <ou-table-column label="状态" prop="enable" width="100">
        <template #default="scope">
          <ou-switch
            v-model="scope.row.enable"
            active-text="启用"
            inactive-text="禁用"
            size="small"
            class="switch-offside"></ou-switch>
        </template>
      </ou-table-column>
      <ou-table-column label="操作" width="100" fixed="right">
        <template #default="scope">
          <ou-button type="primary" text @click="showDialog('edit', scope.row, scope.$index)">编辑</ou-button>
          <ou-button type="primary" text @click="remove(scope.row, scope.$index)">删除</ou-button>
        </template>
      </ou-table-column>
    </ou-table>
    <ou-opera-dialog
      draggable
      :close-on-click-modal="false"
      :width="form.conditionType === 1 ? 530 : 700"
      :title="dialog.title"
      hideFooter
      v-model="dialog.isShow">
      <template #content>
        <div class="add_conent_nopadding">
          <ou-form
            :model="form"
            ref="whiteListRef"
            class="-mb-20"
            :rules="rules"
            labelPosition="right"
            label-width="90px">
            <ou-form-item label="名单名称：" prop="listName">
              <ou-input
                maxlength="63"
                show-word-limit
                style="width: 100%"
                placeholder="请输入"
                v-model="form.listName"></ou-input>
            </ou-form-item>
            <ou-form-item label="条件类型：" prop="conditionType">
              <ou-radio-group v-model="form.conditionType">
                <ou-radio :label="+k" v-for="(v, k) in conditionTypeList" :key="k">{{ v }}</ou-radio>
              </ou-radio-group>
            </ou-form-item>
            <ou-form-item label="IP：" prop="ipWhitelist" v-if="form.conditionType === 0" key="form-ipWhitelist">
              <OuTextArea
                style="width: 100%"
                :placeholder="placeholderText"
                :validateDuplicate="true"
                :validateEditValue="changIp"
                :allowSpaces="false"
                emptyMessage="IPv4不能为空"
                ref="urlTextAreaRef"
                :maxLines="256"
                :validateEmpty="true"
                :allowEmptyLines="false"
                v-model:defaultValue.sync="form.ipWhitelist" />
            </ou-form-item>
            <ou-form-item label="账号：" prop="account" v-if="form.conditionType === 1" key="form-userWhitelist">
              <ou-page-select
                :multiple="true"
                v-model="form.account"
                :edit-data="form.accountDisplay"
                :load-data="featData"
                placeholder="请选择"></ou-page-select>
            </ou-form-item>
            <ou-form-item label="数据标签：" prop="sensitiveType" class="pn_required_label">
              <ou-select
                v-model="form.sensitiveType"
                placeholder="请选择"
                :multiple="true"
                :options="labelDataList"
                style="width: 100%"
                :isSelectAll="true"></ou-select>
            </ou-form-item>
          </ou-form>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <ou-button @click="cancel">取消</ou-button>
          <ou-button type="primary" @click="submit">确定</ou-button>
        </div>
      </template>
    </ou-opera-dialog>
  </div>
</template>

<script setup>
  import { deepClone } from "@/utils/tool.js";
  import { getUserAccount } from "@/api/pgModule.js";
  import { computed, watch, ref } from "vue";
  import { useWhiteListState, changIp, configUserIp } from "@/views/plugins/data-masking/components/hook";
  const {
    rules,
    form,
    DialogForm,
    placeholderText,
    conditionTypeList,
    tmData,
    dialog,
    urlTextAreaRef,
    whiteListRef,
    sensitiveTypeList,
    labelDataList,
    userAccountList,
    formType,
    table,
    handelIndex,
  } = useWhiteListState();
  const emits = defineEmits(["update:list"]);
  const props = defineProps({
    options: {
      type: Object,
      default: () => {},
    },
    list: {
      //白名单列表
      type: Array,
      default: () => [],
    },
  });
  watch(tmData, (nData, oData) => {
    if (nData) {
      sensitiveTypeList.value = nData.identityRule.reduce((acc, cur, index) => {
        acc[cur.name] = cur;
        return acc;
      }, {});
      labelDataList.value = nData.identityRule.reduce((acc, cur, index) => {
        acc[cur.id] = cur.name;
        return acc;
      }, {});
    }
  });
  watch(
    table,
    (nData, oData) => {
      emits("update:list", nData.list || []);
    },
    {
      deep: true,
    },
  );
  watch(
    () => dialog.value.isShow,
    (nData, oData) => {
      if (!nData) {
        form.value = new DialogForm();
        whiteListRef.value.resetFields();
      }
    },
  );
  watch(
    () => props.options,
    (newData, oldData) => {
      if (newData) {
        tmData.value = newData;
      }
    },
  );
  //配置显示数据标签类型同时兼容上个版本数据
  const configTypeName = name => {
    if (!name) return "-";
    if (name && name.includes("all")) return "全部";
    const nameList = name.split(",").reduce((cur, pre) => {
      const str = labelDataList.value[pre] ? labelDataList.value[pre] : sensitiveTypeList.value[pre].name;
      cur.push(`${str}`);
      return cur;
    }, []);
    return nameList.join(",");
  };
  const showDialog = (type, row, index) => {
    const TITLE_ENUM = {
      add: "添加",
      edit: "编辑",
      copy: "复制",
    };
    formType.value = type;
    dialog.value.title = `${TITLE_ENUM[type]}白名单`;
    const item = row;
    if (row) {
      const label = row.sensitiveType
        .split(",")
        .map(e => (labelDataList.value[e] ? e : `${sensitiveTypeList.value[e]?.id || e}`));
      form.value = {
        ...item,
        sensitiveType: label,
        userWhitelist: item.userWhitelist,
        ipWhitelist: Array.isArray(item.ipWhitelist) ? item.ipWhitelist : item.ipWhitelist.split("\n"),
      };
    }
    dialog.value.isShow = true;
    handelIndex.value = index;
    if (form.value.conditionType === 0) {
      //重置ip输入框
      urlTextAreaRef.value.resetErrorState();
    }
  };
  //删除单个
  const remove = (row, index) => {
    table.value.list.splice(index, 1);
  };
  const cancel = () => {
    dialog.value.isShow = false;
  };
  //弹框表单确定操作
  const handelData = () => {
    const items = deepClone({
      ...form.value,
      ipWhitelist: form.value.ipWhitelist.filter(e => e),
      sensitiveType: form.value.sensitiveType.join(","),
    });
    items.accountDisplay = form.value.account.reduce((acc, cur) => {
      const userItem = userAccountList.value.find(e => e.value === cur);
      if (userItem) {
        acc.push(userItem);
      }
      return acc;
    }, []);
    items.account = items.accountDisplay.map(e => e.value);
    if (formType.value === "add") {
      table.value.list.push({ ...items, enable: true });
    }
    if (formType.value === "edit") {
      table.value.list.splice(handelIndex.value, 1, items);
    }
    dialog.value.isShow = false;
  };

  const submit = () => {
    whiteListRef.value.validate(valid => {
      if (form.value.conditionType === 0) {
        const { isValid, message } = urlTextAreaRef.value.validateAll();
        if (!isValid || !valid) return;
      } else {
        if (!valid) return;
      }
      handelData();
    });
  };
  const featData = args => {
    const query = {
      page_num: args.page_num,
      name: args.searchName,
      page_size: 10,
    };
    return new Promise(resolve => {
      let list = [];
      getUserAccount(query).then(res => {
        const { results, row_count } = res.data;
        list = (results || []).map(e => {
          const isUserList = userAccountList.value.find(v => v.value === e.id);
          const item = {
            value: e.id,
            label: e.name,
          };
          if (!isUserList) {
            userAccountList.value.push(item);
          }
          return item;
        });
        resolve({ list, total: row_count });
      });
    });
  };
  const initData = () => {
    table.value.list = props.list;
    tmData.value = props.options;
  };
  initData();
</script>

<style scoped lang="scss">
  .whiteList {
    width: 100%;
  }
  .content-textarea ::v-deep.ouryun-textarea__inner {
    border-radius: 2px 2px 0px 0px;
  }
  .list-handle {
    height: 32px;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
  }
  :deep(.ou-opear-content) {
    .ouryun-form-item {
      margin-bottom: 20px !important;
    }
  }
</style>
