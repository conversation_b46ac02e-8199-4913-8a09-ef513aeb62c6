import { getVirtualServiceInfo } from "@/api/virtualService.js";

const useEngineStore = defineStore("engine", {
  state: () => ({
    _currentEngineId: "", // 响应式声明 内部参数：当前引擎ID，如需使用，请使用getters中的currentEngineId
    _currentOpera: "", // 响应式声明 内部参数： 当前操作的数据（虚拟服务-插件）如需使用，请使用getters中的currentOpera
    isExpired: false, //引擎是否过期，true：过期 false:未过期
    role: "operator", //引擎是否有操作权限 operator（有）
    routeList: [],
  }),
  getters: {
    currentEngineId: state => {
      //当前引擎ID
      return state._currentEngineId || sessionStorage.getItem("ENGINEID");
    },
    currentOpera: state => {
      //当前操作的数据（虚拟服务-插件）
      return state._currentOpera || sessionStorage.getItem("currentOpera");
    },
    isExpired: () => {
      // 引擎是否过期
      return JSON.parse(sessionStorage.getItem("ENGINE_EXPIRED") || null);
    },
    role: () => {
      // 引擎是否有操作权限 operator（有）
      return sessionStorage.getItem("ENGINE_ROLE");
    },
  },
  actions: {
    setCurrentEngineId(id) {
      this._currentEngineId = id;
      sessionStorage.setItem("ENGINEID", id);
    },
    setCurrentOpera(opera) {
      this._currentOpera = opera;
      sessionStorage.setItem("currentOpera", opera);
    },
    setCurrentRouteList() {
      if (!this.currentEngineId || !this.currentOpera) return;
      getVirtualServiceInfo(this.currentEngineId, this.currentOpera).then(res => {
        this.routeList = (res.data.spec.routes || []).map(item => {
          return {
            name: item.commonInfo.name,
            id: item.id,
            disabled: false,
          };
        });
      });
    },
    clearCurrentRouteList() {
      this.routeList = [];
    },
  },
});
export default useEngineStore;
