<template>
  <div class="ccAttack-pg-config add_conent_flex1">
    <ou-form :model="baseForm" ref="ccFormRef" labelPosition="right" :rules="baseRules" :label-width="labelWidth">
      <el-row class="flex" :gutter="80">
        <el-col :span="24">
          <ou-form-item label="防护模式：">
            <ou-radio-group v-model="baseForm.defendMode">
              <ou-radio label="0">自动</ou-radio>
              <ou-radio label="1">紧急</ou-radio>
              <ou-radio label="2">自定义</ou-radio>
            </ou-radio-group>
            <ou-tooltip-icon>
              <template #content>
                <div>根据不同场景可选择不同模式进行防护:</div>
                <div class="defend-tips">
                  <span class="defend-tips-label"></span>
                  <span class="defend-tips-text">
                    <span>自动：统计当前引擎下的请求速率，基于最近7天请求建立速率基线，</span>
                    <span>结合配置的限制等级，自动限制单个客户</span>
                    <span>端访问该域名的请求速率</span>
                  </span>
                </div>
                <div class="defend-tips">
                  <span class="defend-tips-label"></span>
                  <span class="defend-tips-text">
                    <span>紧急：当客户遭受攻击时，需要提高安全防护模式，</span>
                    <span>可使用此模式。由于该模式访问频率限制较为严格，可能存</span>
                    <span>在误杀风险，不建议长期使用</span>
                  </span>
                </div>
                <div class="defend-tips">
                  <span class="defend-tips-label"></span>
                  <span class="defend-tips-text">
                    <span>自定义：根据实际场景自定义防护策略，</span>
                    <span>当有个性化需求以及有明确防护路径时，可使用此模式</span>
                  </span>
                </div>
              </template>
            </ou-tooltip-icon>
          </ou-form-item>
        </el-col>
      </el-row>
      <el-row class="flex" :gutter="80">
        <el-col :span="12">
          <ou-form-item label="防护等级：" v-if="baseForm.defendMode === '0'">
            <ou-select v-model="baseForm.defendLevel" placeholder="请选择">
              <ou-option
                v-for="item in levelList"
                :key="item.value"
                v-show="item.normal"
                :label="item.label"
                :value="item.value"></ou-option>
            </ou-select>
          </ou-form-item>
        </el-col>
      </el-row>
      <template v-if="baseForm.defendMode === '1'">
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item label="人机验证：">
              <ou-select v-model="baseForm.verify" placeholder="请选择">
                <ou-option v-for="(v, k) in verifyList" :key="k" :label="v" :value="k"></ou-option>
              </ou-select>
            </ou-form-item>
          </el-col>
        </el-row>
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item label="触发条件：" v-if="baseForm.verify === 'auto'" key="trigger" prop="trigger.dateCount">
              <div class="pn-select-item">
                <ou-form-item prop="trigger.dateCount" style="margin-right: 4px; margin-bottom: 0">
                  <ou-input
                    placeholder="请输入"
                    class="apd-ipt"
                    v-model.number="baseForm.trigger.dateCount"
                    maxlength="5">
                    <template #append>
                      <ou-select
                        class="date-select"
                        :clearable="false"
                        v-model="baseForm.trigger.dateUnit"
                        placeholder="请选择">
                        <ou-option v-for="(v, k) in timeList" :key="k" :label="v" :value="k"></ou-option>
                      </ou-select>
                    </template>
                  </ou-input>
                </ou-form-item>
                <ou-form-item prop="trigger.count" style="margin-bottom: 0">
                  <ou-input v-model.number="baseForm.trigger.count" placeholder="请输入" maxlength="5">
                    <template #append>次</template>
                  </ou-input>
                </ou-form-item>
              </div>
            </ou-form-item>
          </el-col>
        </el-row>
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item label="验证失败频率：" key="verifyFail" prop="verifyFail.dateCount">
              <div class="pn-select-item">
                <ou-form-item prop="verifyFail.dateCount" style="margin-right: 4px; margin-bottom: 0">
                  <ou-input
                    placeholder="请输入"
                    class="apd-ipt"
                    v-model.number="baseForm.verifyFail.dateCount"
                    maxlength="5">
                    <template #append>
                      <ou-select
                        v-model="baseForm.verifyFail.dateUnit"
                        placeholder="请选择"
                        :clearable="false"
                        class="date-select">
                        <ou-option v-for="(v, k) in timeList" :key="k" :label="v" :value="k"></ou-option>
                      </ou-select>
                    </template>
                  </ou-input>
                </ou-form-item>
                <ou-form-item prop="verifyFail.count" style="margin-bottom: 0">
                  <ou-input v-model.number="baseForm.verifyFail.count" placeholder="请输入" maxlength="5">
                    <template #append>次</template>
                  </ou-input>
                </ou-form-item>
              </div>
            </ou-form-item>
          </el-col>
        </el-row>
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item :key="baseForm.defendMode" label="封锁时长：" prop="astrictTime.dateCount">
              <ou-input
                placeholder="请输入"
                class="apd-ipt"
                v-model.number="baseForm.astrictTime.dateCount"
                maxlength="5">
                <template #append>
                  <ou-select
                    v-model="baseForm.astrictTime.dateUnit"
                    placeholder="请选择"
                    :clearable="false"
                    class="date-select">
                    <ou-option v-for="(v, k) in timeList" :key="k" :label="v" :value="k"></ou-option>
                  </ou-select>
                </template>
              </ou-input>
            </ou-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-if="baseForm.defendMode === '2'">
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item label="访问频率：" prop="visitFrequency.dateCount" style="margin-bottom: 0px">
              <div class="pn-select-item">
                <ou-form-item prop="visitFrequency.dateCount" style="margin-right: 4px">
                  <ou-input
                    placeholder="请输入"
                    class="apd-ipt"
                    v-model.number="baseForm.visitFrequency.dateCount"
                    maxlength="5">
                    <template #append>
                      <ou-select
                        v-model="baseForm.visitFrequency.dateUnit"
                        placeholder="请选择"
                        :clearable="false"
                        class="date-select">
                        <ou-option v-for="(v, k) in timeList" :key="k" :label="v" :value="k"></ou-option>
                      </ou-select>
                    </template>
                  </ou-input>
                </ou-form-item>
                <ou-form-item prop="visitFrequency.count">
                  <ou-input v-model.number="baseForm.visitFrequency.count" placeholder="请输入" maxlength="5">
                    <template #append>次</template>
                  </ou-input>
                </ou-form-item>
              </div>
            </ou-form-item>
          </el-col>
        </el-row>
        <el-row class="flex" :gutter="80">
          <el-col :span="24">
            <ou-form-item label="动作：">
              <ou-radio-group class="action-radios" v-model="baseForm.action">
                <ou-radio label="0">拒绝</ou-radio>
                <ou-radio label="1">放行</ou-radio>
                <ou-radio label="2">
                  <template #default>
                    <div class="pn-form-item-label-group">
                      <span>人机验证</span>
                      <ou-tooltip-icon
                        content="仅用于浏览器访问场景，符合以上条件的请求将进入人机识别验证界面，验证成功,可正常访问，验证失败则对其IP进行封禁"
                        style="margin-left: 35px"></ou-tooltip-icon>
                    </div>
                  </template>
                </ou-radio>
              </ou-radio-group>
            </ou-form-item>
          </el-col>
        </el-row>
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item
              label="验证失败频率："
              key="verifyFail"
              style="margin-bottom: 0px"
              v-if="baseForm.action === '2'"
              prop="verifyFail.dateCount">
              <div class="pn-select-item">
                <ou-form-item prop="verifyFail.dateCount" style="margin-right: 4px">
                  <ou-input
                    placeholder="请输入"
                    class="apd-ipt"
                    :clearable="false"
                    v-model.number="baseForm.verifyFail.dateCount"
                    maxlength="5">
                    <template #append>
                      <ou-select
                        v-model="baseForm.verifyFail.dateUnit"
                        placeholder="请选择"
                        :clearable="false"
                        class="date-select">
                        <ou-option v-for="(v, k) in timeList" :key="k" :label="v" :value="k"></ou-option>
                      </ou-select>
                    </template>
                  </ou-input>
                </ou-form-item>
                <ou-form-item prop="verifyFail.count">
                  <ou-input v-model.number="baseForm.verifyFail.count" placeholder="请输入" maxlength="5">
                    <template #append>次</template>
                  </ou-input>
                </ou-form-item>
              </div>
            </ou-form-item>
          </el-col>
        </el-row>
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item
              :key="baseForm.defendMode"
              label="封锁时长："
              v-if="baseForm.action !== '1'"
              prop="astrictTime.dateCount">
              <ou-input
                placeholder="请输入"
                class="apd-ipt"
                v-model.number="baseForm.astrictTime.dateCount"
                maxlength="5">
                <template #append>
                  <ou-select
                    v-model="baseForm.astrictTime.dateUnit"
                    placeholder="请选择"
                    :clearable="false"
                    class="date-select">
                    <ou-option v-for="(v, k) in timeList" :key="k" :label="v" :value="k"></ou-option>
                  </ou-select>
                </template>
              </ou-input>
            </ou-form-item>
          </el-col>
        </el-row>
      </template>
      <el-row class="flex" :gutter="80">
        <el-col :span="24">
          <div class="pn-tips-container">
            <div class="exigency-tips pn-auto-tips" v-if="baseForm.defendMode === '0'">
              <div class="tips-item">
                <div class="tips-label">宽松：</div>
                <div class="default-tips-text">
                  <span>默认等级，可以防御一般的CC攻击，对于正常请求不会造成误杀，</span>
                  <span>网站无明显流量异常时建议采用此等级。当访问频率达到2000次/5秒时，</span>
                  <span>将拒绝请求并封禁10分钟，速率上限大于7000次/分。</span>
                </div>
              </div>
              <div class="tips-item">
                <div class="tips-label">中等：</div>
                <div class="default-tips-text">
                  <span>适用于页面内容较为简单，动态数据或动态加载内容较少的业务场景。</span>
                  <span>当访问频率达到1000次/5秒时，将拒绝请求并封禁20分钟，</span>
                  <span>速率上限在2400-7000次/分之间。</span>
                </div>
              </div>
              <div class="tips-item">
                <div class="tips-label">严格：</div>
                <div class="default-tips-text">
                  <span>可以防护更为复杂和精巧的CC攻击，但可能会对少部分正常请求造成误杀。</span>
                  <span>当发现网站响应、流量、CPU、内存等指标出现异常时，</span>
                  <span>可使用此等级。当访问频率达到200次/5秒时，将拒绝请求并封禁30分钟，</span>
                  <span>速率上限在1200-2400次/分之间。</span>
                </div>
              </div>
            </div>
            <div class="exigency-tips pn-auto-tips" v-if="baseForm.defendMode === '1'">
              <div class="tips-item">
                <div class="tips-label">自动开启：</div>
                <div class="tips-content">
                  <span>单IP</span>
                  <span class="pn-date-count">
                    {{ baseForm.trigger.dateCount }}{{ timeList[baseForm.trigger.dateUnit] }}
                  </span>
                  <span>
                    <span>内累计访问请求超过</span>
                    <span class="pn-date-count">{{ baseForm.trigger.count }}次</span>
                    <span>时，</span>
                  </span>
                  <span>此IP后续访问请求将进入人机识别验证页面，</span>
                  <span class="pn-date-count">
                    {{ baseForm.verifyFail.dateCount }}{{ timeList[baseForm.verifyFail.dateUnit] }}
                  </span>
                  <span>
                    <span>同IP用户连续验证失败</span>
                    <span class="pn-date-count">{{ baseForm.verifyFail.count }}次</span>
                    <span>之后，</span>
                  </span>
                  <span>
                    <span>将封禁</span>
                    <span class="pn-date-count">
                      {{ baseForm.astrictTime.dateCount }}{{ timeList[baseForm.astrictTime.dateUnit] }}
                    </span>
                  </span>
                  <span style="display: inline">，验证通过者允许继续访问网站。</span>
                </div>
              </div>
              <div class="tips-item">
                <div class="tips-label">一直开启：</div>
                <div class="tips-content">
                  <span>所有访问请求都将进入人机识别验证，</span>
                  <span class="pn-date-count">
                    {{ baseForm.verifyFail.dateCount }}{{ timeList[baseForm.verifyFail.dateUnit] }}
                  </span>
                  <span>
                    <span>同IP用户连续验证失败</span>
                    <span class="pn-date-count">{{ baseForm.verifyFail.count }}次</span>
                    <span>之后，</span>
                  </span>
                  <span>
                    <span>将封禁</span>
                    <span class="pn-date-count">
                      {{ baseForm.astrictTime.dateCount }}{{ timeList[baseForm.astrictTime.dateUnit] }}
                    </span>
                  </span>
                  <span>，验证通过者允许继续访问网站。</span>
                </div>
              </div>
            </div>
            <div class="pn-custom-tips" v-if="baseForm.defendMode === '2'">
              <span>
                <span>单IP</span>
                <span class="pn-date-count">
                  {{ baseForm.visitFrequency.dateCount }}{{ timeList[baseForm.visitFrequency.dateUnit] }}
                </span>
              </span>
              <span>
                <span>内累计访问所有请求超过</span>
                <span class="pn-date-count">{{ baseForm.visitFrequency.count }}次</span>
                <span v-if="baseForm.action === '2'">，触发CC防护人机验证，</span>
                <span v-else>，触发CC防护</span>
                <span v-if="baseForm.action === '0'">，</span>
                <span v-if="baseForm.action === '1'">。</span>
              </span>
              <span v-if="baseForm.action === '2'">
                <span class="pn-date-count">{{ baseForm.verifyFail.dateCount }}</span>
                <span class="pn-date-count">{{ timeList[baseForm.verifyFail.dateUnit] }}</span>
                <span>同IP用户连续验证失败</span>
                <span class="pn-date-count">{{ baseForm.verifyFail.count }}次</span>
                <span>之后，</span>
              </span>
              <span v-if="baseForm.action !== '1'">
                <span>封锁此IP</span>
                <span class="pn-date-count">
                  {{ baseForm.astrictTime.dateCount }}{{ timeList[baseForm.astrictTime.dateUnit] }}
                </span>
              </span>
              <span>
                <span v-if="baseForm.action === '2'">，</span>
                <span v-else-if="baseForm.action !== '1'">。</span>
              </span>
              <span v-if="baseForm.action === '2'">
                <span>验证通过者允许继续访问。</span>
              </span>
            </div>
          </div>
        </el-col>
      </el-row>
      <ou-form-item label="IP白名单：" style="margin-bottom: 0px">
        <whiteList v-model:list="baseForm.whiteData.list" ref="whiteRef"></whiteList>
      </ou-form-item>
    </ou-form>
  </div>
</template>
<script setup>
  import { ref, defineProps, defineExpose } from "vue";
  import whiteList from "@/views/plugins/components/whiteList";
  import { validateForm } from "@/utils/tool.js";
  import { useState } from "../hook";
  import { Notice } from "@ouryun-plus/icons-vue";
  import { defaultConfigHooks } from "../hook/config-default.js";

  const { submitData, timeMap, verifyMap, formRules, levelData } = useState();
  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
    caseLevel: {
      type: String,
      default: "",
    },
  });
  const isLoading = ref(false);
  const labelWidth = ref("140px");
  const ccFormRef = ref(null);
  const whiteRef = ref(null);
  const baseForm = ref({
    pgName: "",
    defendMode: "0",
    defendLevel: "L1",
    verify: "auto",
    trigger: {
      dateCount: 5,
      dateUnit: "S",
      count: 50,
    },
    verifyFail: {
      dateCount: 5,
      dateUnit: "M",
      count: 10,
    },
    visitFrequency: {
      dateCount: 60,
      dateUnit: "S",
      count: 120,
    },
    action: "0",
    astrictTime: {
      dateCount: 10,
      dateUnit: "M",
    },
    whiteData: {
      list: [],
      visitId: [],
    },
  });
  const verifyList = ref(verifyMap);
  const timeList = timeMap;
  const whiteData = ref([]);
  const baseRules = ref(formRules);
  const levelList = levelData;

  const { getDefaultConfig } = defaultConfigHooks();

  const initData = () => {
    if (props.global) {
      const { meta_data } = JSON.parse(props.global);
      baseForm.value = meta_data.form;
    } else {
      // 使用【方案等级】创建配置
      if (props.caseLevel) {
        let defaultConfig = getDefaultConfig(props.caseLevel);
        baseForm.value = Object.assign(baseForm.value, defaultConfig);
      }
    }
  };
  initData();
  const submit = async () => {
    const isValid = await validateForm(ccFormRef.value);
    if (!isValid) return;
    const whiteData = whiteRef.value.configIpList();
    const reqData = submitData(baseForm.value, whiteData);
    return {
      plugin_config: reqData,
      meta_data: {
        form: baseForm.value,
      },
    };
  };
  defineExpose({
    submit,
  });
</script>
<style lang="scss" scoped>
  :deep(.ouryun-radio-group) {
    .ouryun-radio-custom {
      margin-right: 40px;
    }
  }
  .suffix-text {
    font-style: normal;
    color: var(--ouryun-text-color-gray-1);
  }
  :deep(.ouryun-input__wrapper) {
    padding-top: 0;
    padding-bottom: 0;
  }
  .pn-select-item {
    width: 100%;
    display: flex;
    :deep(.ouryun-form-item-custom) {
      flex: 1;
    }
  }

  .date-select {
    width: 64px !important;
  }

  .pn-tips-container {
    margin-left: 140px;
    background: #f3f3f3;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 400;
    color: var(--ouryun-text-color-gray-1);
    line-height: 20px;
    display: flex;
    & span {
      display: inline-block;
    }
    .tips-item {
      margin-bottom: 8px;
      display: flex;
      .tips-label {
        white-space: nowrap;
      }
      .default-tips-text {
        & span {
          display: inline;
        }
      }
    }
  }
  .defend-tips {
    .defend-tips-label {
      display: inline-block;
      background: var(--ouryun-text-color-gray-1);
      border-radius: 50%;
      vertical-align: middle;
      width: 6px;
      margin: 0px 2px 3px 0px;
      height: 6px;
    }
  }
  .apd-ipt {
    ::v-deep .pn-input-group__append {
      color: var(--ouryun-text-color-gray-1);
    }
  }

  .pn-date-count {
    color: var(--ouryun-color-red-6);
  }

  .pn-auto-tips {
    padding: 12px 16px 4px 16px;
  }

  .pn-custom-tips {
    padding: 10px 20px;
  }
  .pn-form-item-label-group {
    display: flex;
    align-items: center;
    column-gap: 4px;
  }
  ::v-deep .ouryun-input__inner {
    height: 32px;
  }
</style>
