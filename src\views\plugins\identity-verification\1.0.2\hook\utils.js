import md5 from "js-md5";
import { OuModal } from "@ouryun/ouryun-plus";
export const bindoptions = [
  { label: "不绑定", value: false },
  { label: "绑定(首次认证IP)", value: true },
];
export const authpageOptions = [
  { label: "默认页面", value: true },
  { label: "无认证页面", value: false },
];
export const accountTransferOptions = [
  { label: "请求头", value: "HEADER" },
  { label: "查询参数", value: "PARAM" },
];
export const validateMatch = (rule, value, callback) => {
  if (!value.length) {
    return callback(new Error("请添加匹配条件"));
  }
  if (value.length > 10) {
    const message = "规则条数已超过上限10条，请进行清理后再提交";
    OuModal.success(message);
    return callback(new Error(message));
  }
  if (value.some(item => item.status === "edit")) {
    return callback(new Error("当前正处于编辑状态"));
  } else {
    callback();
  }
};

export const validateInfoMatch = (rule, value, callback) => {
  // if (!value.length) {
  //   return callback(new Error("请添加认证信息"));
  // }
  if (value.length > 1000) {
    const message = "规则条数已超过上限1000条，请进行清理后再提交";
    OuModal.success(message);
    return callback(new Error(message));
  }
  callback();
  // if (value.some(item => item.edit)) {
  //   return callback(new Error("当前正处于编辑状态"));
  // } else {
  //   callback();
  // }
};

export const validateTokenExp = (rule, value, callback) => {
  if (!value?.length) {
    return callback(new Error("请输入token有效期"));
  }
  if (value > 720 || value < 1) {
    return callback(new Error("输入范围1-720小时"));
  }
  callback();
};
export const rand = length => {
  let result = "";
  const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

export const initFormParams = {
  rule_name: "",
  matchingList: [],
  authInfo: [],
  account_transfer: "HEADER",
  is_signal_IP: false,
  has_verify_page: true,
  token_exp: "",
};

export const downloadFile = (content, filename, type) => {
  const blob = new Blob([content], { type });
  if ("download" in document.createElement("a")) {
    const elink = document.createElement("a");
    elink.download = filename;
    elink.style.display = "none";
    elink.href = URL.createObjectURL(blob);
    document.body.appendChild(elink);
    elink.click();
    URL.revokeObjectURL(elink.href);
    document.body.removeChild(elink);
  } else {
    navigator.msSaveBlob(blob, filename);
  }
};

// 用户信息格式化
export const getUserInfoParameter = array => {
  return array?.map(({ exp, user_name, password }) => ({ password: md5(password), user_name, exp }));
};

export const ExcelName = {
  Name: "账号（英文、数字、-和_，长度限制63个字符，账号名称不能重复）",
  Password: "密码（必须同时包含大写字母、小写字母、数字和特殊符号，四种字符，长度不少于8个字符，不多于63个字符）",
  Exp: "有效期（格式：yyyy-MM-dd，例如：2024-12-31）",
  Remark: "备注(只可输入中文、英文、数字、-和_，长度限制63个字符)",
};

export const ExcelHeader = Object.values(ExcelName);

export default {
  initFormParams,
  bindoptions,
  authpageOptions,
  accountTransferOptions,
  validateMatch,
  validateInfoMatch,
  downloadFile,
};
