<template>
  <div class="bot-protection">
    <ou-form labelPosition="right" ref="formRef" label-width="140px" :model="baseForm" :rules="rules">
      <div class="content-85">
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item label="防护模式：">
              <ou-radio-group v-model="baseForm.check_mode">
                <ou-radio :value="false">拦截模式</ou-radio>
                <ou-radio :value="true">检测模式</ou-radio>
              </ou-radio-group>
            </ou-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <ou-form-item label="防护等级：">
              <ou-select v-model="baseForm.protection_level" :options="LevelList" :clearable="false"></ou-select>
              <ou-tooltip-icon class="form-item-tooltip-icon">
                <template #content>
                  <div>
                    防护等级越高，开启的规则越多，同时可能存在一定误报风险；
                    防护等级越低，开启的规则越少，误报风险也越小。
                  </div>
                  <div class="bot-level-tips">
                    <div>
                      <span class="level-tip-label">宽松：</span>
                      <span>
                        只开启高危规则，主要拦截攻击特征比较明显的请求，当误报情况较多的场最下，建议选择此模式
                      </span>
                    </div>
                    <div>
                      <span class="level-tip-label">中等：</span>
                      <span>开启中危和高危规则，满足大多数场最下的bot防护需求</span>
                    </div>
                    <div>
                      <span class="level-tip-label">严格：</span>
                      <span>所有低危、中危和高危规则都将开启，高精度防护要求可选择此模式</span>
                    </div>
                  </div>
                </template>
              </ou-tooltip-icon>
            </ou-form-item> -->
          </el-col>
          <el-col :span="24">
            <ou-form-item label="防护类型：" prop="protection_type">
              <div class="protection-type-box">
                <div class="protection-type-selectCheck" style="border-bottom: 1px solid #d9d9d9">
                  <ou-checkbox v-model="checkAll" :indeterminate="isIndeterminate">全选</ou-checkbox>
                </div>
                <div style="padding: 5px 16px">
                  <ou-checkbox-group v-model="baseForm.protection_type">
                    <div class="type-container">
                      <div class="type-item-container" v-for="item in typeList">
                        <ou-checkbox :key="item.value" :label="item.label" :value="item.value">
                          {{ item.label }}
                        </ou-checkbox>
                      </div>
                    </div>
                  </ou-checkbox-group>
                </div>
              </div>
            </ou-form-item>
          </el-col>
        </el-row>
      </div>
    </ou-form>
  </div>
</template>

<script setup>
  import useState from "../hook/index";
  import { defaultConfigHooks } from "../hook/config-default.js";

  import { validateForm } from "@/utils/tool.js";
  const { baseForm, rules, LevelList, typeList, isIndeterminate, checkAll, formRef } = useState();
  const { getDefaultConfig } = defaultConfigHooks();

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
    caseLevel: {
      type: String,
      default: "",
    },
  });

  const onCreated = () => {
    if (props.content || props.global) {
      initForm();
    } else {
      if (props.caseLevel === "strict") {
        Object.assign(baseForm, getDefaultConfig());
      }
    }
  };

  const initForm = () => {
    const content = JSON.parse(props.content || props.global);

    const form = {
      ...content.plugin_config,
      protection_type: content.meta_data.protection_type,
    };

    Object.assign(baseForm, form);
  };

  const submit = async () => {
    const isValid = await validateForm(formRef.value);
    if (!isValid) return;
    const protection_type = baseForm.protection_type.reduce((acc, current) => acc + current, 0);
    const params = {
      plugin_config: {
        ...baseForm,
        protection_type,
      },
      meta_data: {
        protection_type: baseForm.protection_type,
      },
    };

    return params;
  };

  onCreated();

  defineExpose({
    submit,
  });
</script>

<style lang="scss" scoped>
  .protection-type-box {
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
  .protection-type-selectCheck {
    padding: 0px 16px;
  }
  .content-85 {
    width: calc(85% + 15px);
  }
  .type-container {
    flex-wrap: wrap;
    display: flex;
    .type-item-container {
      width: 20%;
      min-width: 230px;
    }
  }
  .level-tip-label {
    font-weight: 600;
  }
  .form-item-tooltip-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -24px;
    font-size: 16px;
  }
</style>
