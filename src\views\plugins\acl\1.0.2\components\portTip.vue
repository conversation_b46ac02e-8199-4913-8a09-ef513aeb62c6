<template>
  <ou-tooltip-icon class="tips-position">
    <template #content>
      <div>
        <div>取值范围为1~65535，支持以下格式：</div>
        <div>
          <div class="rule-list-tips">单个端口：22</div>
          <div class="rule-list-tips">端口范围：22-30</div>
          <div class="rule-list-tips">多个端口：22,24-30，一条规则最多支持20个值，且不能重复</div>
          <div class="rule-list-tips">全部端口：为空默认包含所有端口，或1-65535</div>
        </div>
      </div>
    </template>
  </ou-tooltip-icon>
</template>

<script setup name="IpTips"></script>

<style lang="scss" scoped>
  // 用于popover内的列表提示
  .plugin-tips-group {
    display: flex;
    flex-direction: column;
    line-height: 1.5;
    row-gap: 8px;
    .plugin-tips-group-item {
      .label,
      .value {
        display: inline;
      }
      .label::after {
        content: "：";
      }
    }
  }
</style>
