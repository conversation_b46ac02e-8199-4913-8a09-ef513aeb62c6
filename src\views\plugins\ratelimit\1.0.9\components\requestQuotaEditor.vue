<template>
  <div class="plugin-request-quota-editor" ref="editorRef">
    <div v-for="(item, index) in props.modelValue" :key="index" class="group-item">
      <div class="input-row">
        <ou-input
          @input="activateValidator(index, 'duration')"
          @blur="activateValidator(index, 'duration')"
          v-model.number="item.duration"
          oninput="if(value.trim() !=='' && value < 1) value = 1;value=value.replace(/[^0-9]/g,'')"
          style="width: 167px"
          placeholder="请输入时长"
          class="input-select-group duration"
          :class="{ 'is-error': inputErrorClass(item, index) }"
          :clearable="false">
          <template #append>
            <ou-select class="append-select" v-model="item.durationOption" style="width: 64px" :clearable="false">
              <ou-option v-for="item in durationOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ou-select>
          </template>
        </ou-input>
        <ou-input
          @input="activateValidator(index, 'limit')"
          @blur="activateValidator(index, 'limit')"
          v-model.number="item.limit"
          oninput="if(value.trim() !=='' && value < 1) value = 1;value=value.replace(/[^0-9]/g,'')"
          style="width: 128px"
          placeholder="请输入次数"
          class="input-select-group limit"
          :class="{ 'is-error': errorLimitText(item, index) }"
          :clearable="false">
          <template #append>次</template>
        </ou-input>
        <ou-button v-if="index >= 1" @click="delGroup(index)" class="request-quota-btn">
          <ou-icon color="#999"><Minus /></ou-icon>
        </ou-button>
        <ou-button
          v-else-if="index === 0"
          @click="addGroup(item, index)"
          class="request-quota-btn"
          :disabled="props.modelValue.length >= 2 || !item.duration || !item.limit">
          <ou-icon color="#999"><Plus /></ou-icon>
        </ou-button>
      </div>

      <div class="error-text-row">
        <div class="error-duration">{{ errorDurationText(item, index) }}</div>
        <div class="error-limit">{{ errorLimitText(item, index) }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { ref, nextTick, defineProps, defineEmits, defineExpose } from "vue";
  import { OuModal } from "@ouryun/ouryun-plus";

  const props = defineProps({
    modelValue: {
      type: Array,
      required: true,
    },
  });

  const emits = defineEmits(["update:modelValue"]);

  const durationOptions = [
    {
      label: "秒",
      value: 1,
    },
    {
      label: "分钟",
      value: 60,
    },
    {
      label: "小时",
      value: 3600,
    },
  ];

  const onCreated = () => {
    if (!props.modelValue || !props.modelValue.length) {
      emits("update:modelValue", [
        {
          duration: "",
          durationOption: 1,
          limit: "",
        },
      ]);
    }
  };
  onCreated();

  const addGroup = (item, index) => {
    if (props.modelValue.length >= 2) {
      OuModal.warning("最多添加两条配额");
      return;
    }
    if (!item.duration || !item.limit) {
      activateValidator(index, "duration");
      activateValidator(index, "limit");
      return;
    }
    props.modelValue?.push({
      duration: "",
      durationOption: 1,
      limit: "",
    });
  };
  const delGroup = index => {
    if (props.modelValue.length <= 1) return;
    props.modelValue.splice(index, 1);
  };

  const errorDurationText = (item, index) => {
    if (!item.duration) {
      return "请输入时长";
    } else if (!/^\d+$/.test(+item.duration)) {
      return "请输入大于0的正整数";
    } else if (+item.duration > 9999) {
      return "时长不能超过9999";
    } else if (
      index === 1 &&
      props.modelValue[0].duration === props.modelValue[1].duration &&
      props.modelValue[0].durationOption === props.modelValue[1].durationOption
    ) {
      return "不能添加相同时间的配额";
    }
    return "";
  };

  const inputErrorClass = (item, index) => {
    if (
      index === 1 &&
      props.modelValue[0].duration === props.modelValue[1].duration &&
      props.modelValue[0].limit === props.modelValue[1].limit &&
      props.modelValue[0].durationOption === props.modelValue[1].durationOption
    ) {
      return "";
    }
    return errorDurationText(item, index);
  };

  const errorLimitText = (item, index) => {
    if (!item.limit) {
      return "请输入次数";
    } else if (!/^\d+$/.test(+item.limit)) {
      return "请输入大于0的正整数";
    } else if (item.limit > 100000000) {
      return "次数不能超过1亿";
    }

    // ■计算公式：第一个频率配置的时间为a，次数为b，第二个配置的时间为c，次数为d
    // ●a>c：则d=(向上取整(b/a*c),b)，如果进行了向上取整，则值可取，若计算为整数，则值不可取，小括号表示端值不可取，中括号表示可取
    // ●a<c：则d=(b,向下取整(b/a*c))，如果进行了向下取整，则值可取，若计算为整数，则值不可取，小括号表示端值不可取，中括号表示可取

    if (
      index === 1 &&
      !(
        props.modelValue[0].duration === props.modelValue[1].duration &&
        props.modelValue[0].durationOption === props.modelValue[1].durationOption
      )
    ) {
      //请求配额数值转换
      const quotaChangeTime = item => {
        let stime = "";
        if (item.durationOption === 3600) {
          stime = Number(item.duration) * 3600;
        } else if (item.durationOption === 60) {
          stime = Number(item.duration) * 60;
        } else {
          stime = Number(item.duration);
        }
        return stime;
      };

      const data1 = props.modelValue[0];
      const data2 = props.modelValue[1];
      if (quotaChangeTime(data1) > quotaChangeTime(data2)) {
        let leftNum = (Number(data1.limit) / quotaChangeTime(data1)) * quotaChangeTime(data2);
        let rigtNum = data1.limit;
        let interval = leftNum % 1 == 0 ? `(${leftNum},${rigtNum})` : `[${Math.ceil(leftNum)},${rigtNum})`;
        if (leftNum % 1 == 0) {
          if (Number(data2.limit) >= rigtNum || Number(data2.limit) <= leftNum) {
            return `次数合法区间为${interval}`;
          }
        } else {
          if (Number(data2.limit) >= rigtNum || Number(data2.limit) < Math.ceil(leftNum)) {
            return `次数合法区间为${interval}`;
          }
        }
      } else {
        let leftNum = data1.limit;
        let rigtNum = (Number(data1.limit) / quotaChangeTime(data1)) * quotaChangeTime(data2);
        let interval = rigtNum % 1 == 0 ? `(${leftNum},${rigtNum})` : `(${leftNum},${Math.floor(rigtNum)}]`;
        if (rigtNum % 1 == 0) {
          if (Number(data2.limit) >= rigtNum || Number(data2.limit) <= leftNum) {
            return `次数合法区间为${interval}`;
          }
        } else {
          if (Number(data2.limit) > Math.floor(rigtNum) || Number(data2.limit) < leftNum) {
            return `次数合法区间为${interval}`;
          }
        }
      }
    }

    return "";
  };

  const editorRef = ref(null);
  const activateValidator = (index, className) => {
    const groupElements = editorRef.value.querySelectorAll(".group-item");
    const inputElement = groupElements[index].querySelector(`.input-select-group.${className}`);
    const errorTextElement = groupElements[index].querySelector(`.error-${className}`);
    nextTick(() => {
      inputElement.classList.add("validator");
      errorTextElement.classList.add("validator");
    });
  };
  const resetFields = () => {
    emits("update:modelValue", [
      {
        duration: "",
        durationOption: 1,
        limit: "",
      },
    ]);

    nextTick(() => {
      const groupElements = editorRef.value.querySelectorAll(".group-item");
      groupElements.forEach(groupElement => {
        const elelems = [
          groupElement.querySelector(".input-select-group.duration"),
          groupElement.querySelector(".input-select-group.limit"),
          groupElement.querySelector(".error-duration"),
          groupElement.querySelector(".error-limit"),
        ];

        elelems.forEach(element => {
          element && element.classList.remove("validator");
        });
      });
    });
  };

  const validate = () => {
    let result = true;
    props.modelValue.forEach((item, index) => {
      activateValidator(index, "duration");
      activateValidator(index, "limit");

      if (errorDurationText(item, index) || errorLimitText(item, index)) result = false;
    });
    return result;
  };

  defineExpose({
    resetFields,
    validate,
  });
</script>

<style lang="scss" scoped>
  .plugin-request-quota-editor {
    display: flex;
    flex-direction: column;

    .group-item {
      display: flex;
      flex-direction: column;
      row-gap: 3px;
      position: relative;
      margin-bottom: 12px;

      .input-row {
        display: flex;
        align-items: center;
        column-gap: 8px;
      }

      .error-text-row {
        display: flex;
        column-gap: 4px;
        position: absolute;
        bottom: 0;
        transform: translateY(calc(100% + 2px));

        & > div:nth-child(1) {
          width: 172px;
          &:empty {
            height: 4px;
          }
        }
        & > div:nth-child(2) {
          flex: 1;
          &:empty {
            height: 4px;
          }
        }
      }

      &:has(.error-text-row > div:not(:empty)) {
        margin-bottom: 20px;
      }
      &:last-child {
        margin-bottom: 12px !important;
      }

      .request-quota-btn {
        margin: 0;
        border-style: dashed;
        width: 32px;
        height: 32px;
        &.is-disabled {
          color: #999999;
          cursor: no-drop;
        }
      }

      @mixin errorText {
        color: var(--ouryun-color-danger);
        font-size: 12px;
        line-height: 1;
        top: 32px;
        height: 0;
        overflow: hidden;
      }
      .error-duration {
        @include errorText;
        left: 0;
      }
      .error-limit {
        @include errorText;
        left: 172px;
      }

      .validator.error-duration,
      .validator.error-limit {
        height: auto;
      }
    }
    ::v-deep {
      .ouryun-input-group__append,
      .ouryun-input-group__prepend {
        color: #333333;
      }
    }
  }
</style>
