<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  </head>
  <script>
 
 // 由于后端使用的Goja库不支持split方法，因此使用此函数替换split将字符串分割为数组
function customSplit(str, symbol) {
  let temp = "";
  const result = [];
  for (const char of str) {
    if (char === symbol) {
      result.push(temp);
      temp = "";
    } else {
      temp += char;
    }
  }
  result.push(temp);
  return result;
}

/**
 * 该函数用于判断是否需要重置匹配规则
 * @function isNeedReset
 * @param {array} matchingList - 匹配条件配置注解
 * @param {object} updateContent - 被修改的通用库
 * @return {boolean}  是否需要重置匹配规则
 */
function isNeedReset(matchingList, updateContent) {
  const needReset = matchingList.some(matchItem => {
    return (
      matchItem.field === "srcIp" &&
      ["belongToIpGroup", "notBelongToIpGroup"].includes(matchItem.logic) &&
      matchItem.content.includes(updateContent.id)
    );
  });
  return needReset;
}

/**
 * 该函数用于将旧匹配条件配置参数转换为新匹配条件配置参数
 * @function mergeMatchConfig
 * @param {array} match - 匹配条件配置参数
 * @param {object} updateContent - 被修改的通用库
 */
function mergeMatchConfig(match, updateContent) {
  match.forEach(newRulesItem => {
    if (newRulesItem.hasOwnProperty("ip_list")) {
      newRulesItem.ip_list.ip_groups.forEach(ipGroup => {
        if (ipGroup.id === updateContent.id) {
          const ipRangeAllList = updateContent.ip_ranges;
          const ipRangeList = ipRangeAllList.filter(item => item.includes("-"));
          const ipSetList = ipRangeAllList.filter(item => item && !item.includes("-"));

          ipGroup.ip_range = ipRangeList.map(item => {
            const rangeItem = customSplit(item, "-");
            return {
              start_ip: rangeItem[0],
              end_ip: rangeItem[1],
            };
          });

          ipGroup.ip_set.list = ipSetList.map(item => {
            if (item.includes("/")) {
              const rangeItem = customSplit(item, "/");
              return {
                address_prefix: rangeItem[0],
                prefix_len: +rangeItem[1],
              };
            } else {
              return {
                address_prefix: item,
                prefix_len: 32,
              };
            }
          });
        }
      });
    }
  });
}

// 应用级ACL插件
function applicationAcl(sourceName, content, globalContent, updateContent) {
  if (sourceName !== "ip_address_database") {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(newRules.match, updateContent);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 请求频率控制插件
function ratelimit(sourceName, content, globalContent, updateContent) {
  if (sourceName !== "ip_address_database") {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(newRules.matchers, updateContent);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}
// 流量编排插件
function trafficArrangement(sourceName, content, globalContent, updateContent) {
  if (sourceName !== "ip_address_database") {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data.match[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(newRules.matchers, updateContent);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 数据脱敏插件
function mergeMaskingData(sourceName, content, globalContent, updateContent) {
  if (!["ip_address_database", "date_label_rule"].includes(sourceName)) {
    return {
      content,
      globalContent,
    };
  }
  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rules.map((item, index) => {
    let rules = {};
    if (sourceName === "ip_address_database") {
      const needReset = isNeedReset(contentObj.meta_data[index].matchingList, updateContent);
      if (needReset) {
        const newRules = JSON.parse(JSON.stringify(item));
        mergeMatchConfig(newRules.match, updateContent);
        rules = newRules;
      }
    }
    if (sourceName === "date_label_rule") {
    }
    rules = item;
    return rules;
  });

  contentObj.plugin_config.rules = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

// 认证鉴权
function mergeIdentityVerification(sourceName, content, globalContent, updateContent) {
  if (sourceName !== "ip_address_database") {
    return {
      content,
      globalContent,
    };
  }

  const contentObj = Object.keys(content).length ? content : globalContent;

  const pluginConfigRules = contentObj.plugin_config.rule_config.map((item, index) => {
    const needReset = isNeedReset(contentObj.meta_data[index].matchingList, updateContent);

    let rules = {};

    if (needReset) {
      const newRules = JSON.parse(JSON.stringify(item));
      mergeMatchConfig(newRules.match_info, updateContent);
      rules = newRules;
    } else {
      rules = item;
    }

    return rules;
  });

  contentObj.plugin_config.rule_config = pluginConfigRules;

  const newContent = {
    content: {},
    globalContent: {},
  };
  if (Object.keys(content).length) newContent.content = contentObj;
  if (Object.keys(globalContent).length) newContent.globalContent = contentObj;

  return newContent;
}

function mergeConfig(sourceName, pluginName, content, globalContent, updateContent) {
  switch (pluginName) {
    case "www.srhino.com.application-acl":
      return applicationAcl(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.ratelimit":
      return ratelimit(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.traffic-arrangement":
      return trafficArrangement(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.data-masking":
      return mergeMaskingData(sourceName, content || {}, globalContent || {}, updateContent);
    case "www.srhino.com.identity-verification":
      return mergeIdentityVerification(sourceName, content || {}, globalContent || {}, updateContent);
    default:
      return {
        content,
        globalContent,
      };
  }
}


globalContent = ""
updateContent = {"id":1,"ns_name":"默认局域网（ABC类）","ns_type_name":"局域网","ns_type":1,"ip_ranges":["********"],"description":"通常使用私有IP地址（A类、B类、C类）","enabled_sign":1,"update_at":"2025-03-20 09:33:46","created_at":"-","creator_name":"admin","updater_name":"admin"}
content= {"plugin_config":{"rule_config":[{"rule_name":"规则1","match_info":[{"ip_list":{"ip_groups":[{"ip_range":[],"ip_set":{"list":[{"address_prefix":"*******","prefix_len":32}]},"id":1,"invert":false}]}}],"user_info":[{"password":"3973c16b31d0552eb2bda17e95188168","user_name":"jj","exp":""}],"token_exp":"18","account_transfer":"HEADER","is_signal_IP":false,"has_verify_page":true,"enable":true,"rule_id":"x0fivpfvmosdzyuw"}]},"meta_data":[{"matchingList":[{"content":[1],"field":"srcIp","logic":"belongToIpGroup","params":"","status":"view"}],"authInfo":[{"edit":false,"exp":"","password":"zha@S#DDD3","remark":"","showPassword":false,"user_name":"jj"}]}]}
    const res = mergeConfig(
      "ip_address_database",
      "www.srhino.com.identity-verification",
      content, globalContent, updateContent);

    console.log(res);
  </script>

  <body></body>
</html>
