//
let auto = {
  plugin_config: {
    auto_mode: true,
    buffer_limit: 1048576,
    custom_password: {
      rule_config: [{ part_rule: [] }, { part_rule: [] }],
      date_range: [],
      date_format: 0,
      filter_instance_id: "www.srhino.com.weak-password-detection1746503194108",
    },
  },
  meta_data: {
    highList: [],
    isHidden: false,
    rule: "1",
    astrictSize: { count: "", unit: "kb" },
    customConfig: { firstPart: "", secondPart: "", date: "", dateFormat: [] },
  },
};

// 高级匹配
let highMatch = {
  plugin_config: {
    auto_mode: false,
    buffer_limit: 1022976,
    custom_password: {
      rule_config: [
        {
          part_rule: ["rrr"],
        },
        {
          part_rule: ["rrr9"],
        },
      ],
      date_range: ["2025-05-15", "2025-05-23"],
      date_format: 16,
      filter_instance_id: "www.srhino.com.weak-password-detection1746517124541",
    },
    advance_config: [
      {
        password_name: ["e1"],
        user_name: ["e"],
        cluster_name: ["01jthmepwp6q1y9hkfrnwdd5cv"],
      },
      {
        password_name: [],
        user_name: [],
        cluster_name: ["01jthpkxpzjnkgha7ks8shr022"],
      },
    ],
  },
  meta_data: {
    highList: [
      {
        business: "01jthmepwp6q1y9hkfrnwdd5cv",
        rule: "1",
        userName: "e",
        pwdField: "e1",
      },
      {
        business: "01jthpkxpzjnkgha7ks8shr022",
        rule: "0",
        userName: "",
        pwdField: "",
      },
    ],
    isHidden: false,
    rule: "0",
    astrictSize: {
      count: 999,
      unit: "kb",
    },
    customConfig: {
      firstPart: "rrr",
      secondPart: "rrr9",
      date: ["2025-05-15", "2025-05-23"],
      dateFormat: ["mm"],
    },
  },
};

let highMatch1 = {
  plugin_config: {
    auto_mode: false,
    buffer_limit: 1022976,
    custom_password: {
      rule_config: [{ part_rule: ["rrr"] }, { part_rule: ["rrr9"] }],
      date_range: ["2025-05-15", "2025-05-23"],
      date_format: 16,
      filter_instance_id: "www.srhino.com.weak-password-detection1746579888127",
    },
    advance_config: [{ password_name: ["e1"], user_name: ["e"], cluster_name: ["01jthmepwp6q1y9hkfrnwdd5cv"] }],
  },
  meta_data: {
    highList: [{ business: "01jthmepwp6q1y9hkfrnwdd5cv", rule: "1", userName: "e", pwdField: "e1" }],
    isHidden: false,
    rule: "0",
    astrictSize: { count: 999, unit: "kb" },
    customConfig: { firstPart: "rrr", secondPart: "rrr9", date: ["2025-05-15", "2025-05-23"], dateFormat: ["mm"] },
  },
};

// 当前版本
let form = {
  plugin_config: { check_config: [{ enable: true, built_in_rules: true }], buffer_limit: 1024 },
  meta_data: {
    form: {
      ruleList: [
        {
          user_name: [
            {
              keys: [0, 1, 2, 3],
              values:
                "user,username,name,user_ID,login_name ,account,account_name,email,email_address, telephone,nickname",
            },
          ],
          password_name: [{ keys: [0, 1, 2, 3], values: "password, pw, code,key, PIN" }],
          path: "/",
          status_code: "200",
          token: [
            {
              keys: [4, 5, 6],
              values: "known_sign_in, token, session-id, cookie, session, login, signin, session_key",
            },
          ],
          rule_base: [],
          ruleEditData: [{ value: 1, is_default: true, name: "内置弱密码库" }],
          status: true,
          isDefault: true,
          rowId: "*************",
        },
      ],
      multipleList: [],
    },
  },
};

export const weakPasswordDetection = {
  auto,
  highMatch,
  highMatch1,
  form,
};
