<template>
  <div class="cc-pg-details">
    <BasicInfoContainer title="配置信息">
      <template #container>
        <OuBasicInfo :keyLabels="defendLevelEnums" :basicinfo="defendLevelObj"></OuBasicInfo>
        <OuBasicInfo :keyLabels="keyLabels" :basicinfo="baseData" style="margin-top: 20px">
          <template #content="{ msg }">
            <ou-table :data="configData.whiteData" style="width: 100%">
              <ou-table-column type="index" label="序号" width="44" align="center"></ou-table-column>
              <ou-table-column prop="companyName" label="名单名称">
                <template #default="scope">
                  <ou-text-ellipsis :content="scope.row.companyName"></ou-text-ellipsis>
                </template>
              </ou-table-column>
              <ou-table-column prop="status" label="状态">
                <template #default="scope">
                  <span :style="{ color: scope.row.status ? '#62bf78' : '#ff4f45' }">
                    {{ scope.row.status ? "启用" : "禁用" }}
                  </span>
                </template>
              </ou-table-column>
            </ou-table>
            <div class="pagination-container" style="margin-top: 12px" v-if="configData.whiteData.length > 10">
              <ou-pagination
                :total="configData.whiteData.length"
                v-model:page="whitePagination.currentPage"
                v-model:limit="whitePagination.PageSize"
                size="small"></ou-pagination>
            </div>
          </template>
        </OuBasicInfo>
      </template>
    </BasicInfoContainer>
    <BasicInfoContainer :title="`已绑定对象（${serviceList.length}个）`" style="flex: 1">
      <template #container>
        <ou-table :data="tableData" style="width: 100%">
          <ou-table-column type="index" label="序号" width="44" align="center"></ou-table-column>
          <ou-table-column prop="serviceName" label="虚拟服务名称"></ou-table-column>
          <ou-table-column prop="domainMatch" label="域名匹配"></ou-table-column>
        </ou-table>
        <div class="pagination-container" style="margin-top: 12px" v-if="serviceList.length > 10">
          <ou-pagination
            :total="serviceList.length"
            v-model:page="queryParams.currentPage"
            v-model:limit="queryParams.PageSize"
            size="small"></ou-pagination>
        </div>
      </template>
    </BasicInfoContainer>
  </div>
</template>
<script setup>
  import { computed } from "vue";
  import { useDetail } from "../hook/detail";
  import BasicInfoContainer from "@/components/BasicInfoContainer";
  const { baseInfo, boundList, baseData, keyLabels } = useDetail();
  const configData = baseInfo.value;
  const serviceList = boundList.value;
  const queryParams = ref({
    currentPage: 1,
    PageSize: 10,
  });
  const whitePagination = ref({
    currentPage: 1,
    PageSize: 10,
  });
  const defendLevelEnums = computed(() => {
    let a = Object.keys(baseInfo.value?.defendLevel).map(item => ({ label: item, key: item }));
    return [{ label: "防护模式", key: "defendMode" }].concat(a);
  });
  const defendLevelObj = computed(() => {
    return {
      ...baseInfo.value?.defendLevel,
      defendMode: baseInfo.value?.defendMode,
    };
  });
  //白名单
  const whiteTableData = computed(() => {
    const currentPage = whitePagination.value.currentPage;
    const pageSize = whitePagination.value.PageSize;
    return configData.whiteData.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  });
  //分页数据
  const tableData = computed(() => {
    const currentPage = queryParams.value.currentPage;
    const pageSize = queryParams.value.PageSize;
    return serviceList.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  });
</script>
<style lang="scss" scoped>
  :deep(.ouryun-basicinfo) {
    :has(.ouryun-table) {
      .item-left {
        line-height: 40px;
      }
    }
  }
</style>
