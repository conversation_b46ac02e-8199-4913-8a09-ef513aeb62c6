import { getPluginConfig, getUsedPlugins } from "@/api/plugin.js";
import { getIpConfigsSimpleListApi } from "@/api/ip.js";
import useEngineStore from "@/store/modules/engine";
const engineStore = useEngineStore();

let NoCreatPlugins = ["www.srhino.com.acl"];

const usePluginStore = defineStore("plugin", {
  state: () => ({
    usedPlugins: [], // 已使用的插件列表
    pluginConfig: null,
    ipGroupList: [],
  }),
  getters: {
    usedNoSpecialPlugins: state =>
      state.usedPlugins?.filter(item => !(item.special || NoCreatPlugins.includes(item.metadataName))),
    selectedConfig: state => {
      return name => state.usedPlugins.find(user => user.metadataName === name) || {};
    },
  },
  actions: {
    async setUsedPlugins() {
      await getUsedPlugins().then(res => {
        if (res.code == 200) {
          this.usedPlugins = res.data.items || [];
        }
      });

      // this.usedPlugins = [
      // { displayName: "认证鉴权", metadataName: "authentication", version: "1.0.1" },
      //   { displayName: "RL请求频率控制", metadataName: "ratelimit", version: "1.0.7" },
      //   { displayName: "4层ACL", metadataName: "acl", version: "1.0.0" },
      //   { displayName: "基础Web攻击防护", metadataName: "waf", version: "1.0.8" },
      //   { displayName: "弱密码检测", metadataName: "weak-password-check", version: "1.0.1" },
      //   { displayName: "数据脱敏", metadataName: "response-rewrite", version: "1.0.0" },
      //   { displayName: "CC攻击防护", metadataName: "anti-cc", version: "1.0.3" },
      //   { displayName: "AAC应用层访问控制", metadataName: "aac", version: "1.0.0" },
      //   { displayName: "TA流量编排", metadataName: "trafficArrangement", version: "1.0.2" },
      //   { displayName: "TM流量镜像", metadataName: "trafficMirror", version: "1.0.1" },
      //   // { displayName: "配置1", metadataName: "config1", version: "1.0.0" },
      //   // { displayName: "配置2", metadataName: "config2", version: "1.0.0" },
      // ];
    },
    async setPluginConfig(data) {
      await getPluginConfig(data).then(res => {
        if (res.code == 200) {
          const pluginConfig = res.data.data;

          if (pluginConfig) {
            pluginConfig.refAttachs = (pluginConfig.refAttachs || []).map(item => {
              return {
                displayName: item.displayName,
                domains: item.domains.join(","),
              };
            });
          }

          this.pluginConfig = pluginConfig;
        }
      });
    },
    async setIpGroupList() {
      const res = await getIpConfigsSimpleListApi({ page_num: 1, page_size: 999, with_ip_ranges: true });
      this.ipGroupList = (res.data.results || [])
        .filter(item => item.ip_ranges)
        .map(item => {
          return {
            label: item.name,
            value: item.id,
            ipRanges: item.ip_ranges,
            disabled: item.enabled_sign === 2,
          };
        });
    },
    clearPluginConfig() {
      this.pluginConfig = null;
    },
  },
});
export default usePluginStore;
