import { validateIpAll } from "@/utils/validate.js";
import { regex as REGEX } from "@ouryun/ouryun-plus";
export const protoOptions = [
  { label: "全部", value: 0 },
  { label: "TCP", value: 1 },
  { label: "UDP", value: 2 },
  { label: "ICMP", value: 3 },
];
export const actionOptions = [
  { label: "放行", value: 1 },
  { label: "拒绝", value: 2 },
];

export const getTableLabel = scope => {
  let res = scope.row[scope.item.prop];
  switch (scope.item.prop) {
    case "proto":
      res = protoOptions.find(i => i.value == res)?.label || "-";
      break;
    case "action":
      res = actionOptions.find(i => i.value == res)?.label || "-";
      break;
    case "sport":
    case "dport":
      if (scope.row?.proto == 3) {
        res = "-";
      } else {
        res = res || "全部";
      }
      break;
    case "saddr":
    case "daddr":
      res = res || "全部";
      break;
  }
  return res;
};

// 验证ip
export const validateIp = (rule, value, callback) => {
  if (value?.length) {
    const ipList = value;
    const newList = ipList.filter(item => item.trim() !== "");
    for (let index = 0; index < newList.length; index++) {
      const e = newList[index];
      if (!validateIpAll(e)) {
        callback(new Error(`第${index + 1}个IP不合法：${e}`));
      }
    }
    callback();
  }
  callback();
};

// 验证端口
export const validatePort = (rule, value, callback) => {
  if (value?.length) {
    const portList = value.split(",");
    const newList = portList.filter(item => item.trim() !== "");
    if (newList.length !== portList.length) {
      callback(new Error(`输入不合法`));
    }
    for (let index = 0; index < newList.length; index++) {
      const e = newList[index];
      let arr = e.split("-");
      let rest = newList.slice(0, index);
      if (arr.length == 1) {
        if (!REGEX.PORT.test(arr[0])) {
          callback(new Error(`第${index + 1}个端口验证失败：${e}`));
        }
        if (rest.findIndex(item => item === e) !== -1) {
          callback(new Error(`第${index + 1}个端口重复：${e}`));
        }
      } else if (arr.length == 2) {
        if (!REGEX.PORT.test(arr[0]) || !REGEX.PORT.test(arr[1])) {
          callback(new Error(`第${index + 1}个端口验证失败：${e}`));
        }
        if (Number(arr[0]) >= Number(arr[1])) {
          callback(new Error(`第${index + 1}个端口起始端口需要小于结束端口：${e}`));
        }
        if (rest.findIndex(item => item === e) !== -1) {
          callback(new Error(`第${index + 1}个端口重复：${e}`));
        }
      } else {
        callback(new Error(`第${index + 1}个端口验证失败：${e}`));
      }
    }
    callback();
  }
  callback();
};

export const validateTokenExp = (rule, value, callback) => {
  if (!value?.length) {
    return callback();
  }
  if (value > 65535 || value < 1) {
    return callback(new Error("输入范围1-65535"));
  }
  callback();
};

export const validateRemark = (rule, value, callback) => {
  if (value?.length) {
    if (value?.length > 150) {
      return callback(new Error("限制150字符"));
    }
  }
  callback();
};

export const findIp = (range, searchValue) => {
  if (range === "") return true;
  if (range.indexOf("0.0.0.0") !== -1) return true;
  if (range.indexOf(searchValue) !== -1) return true;
  return false;
};
export const initFormParams = {
  name: "",
  proto: 0,
  saddr: [],
  sport: "",
  daddr: [],
  dport: "",
  action: 2,
  remark: "",
  enable: false,
};

export const ExcelName = {
  name: "规则名称",
  proto: "协议类型",
  saddr: "源IP",
  sport: "源端口",
  daddr: "目的IP",
  dport: "目的端口",
  action: "动作",
  remark: "描述",
};

export const ExcelHeader = Object.values(ExcelName);

export const getNameById_proto = id => {
  return protoOptions.find(i => i.value == id)?.label || "";
};

export const getNameById_action = id => {
  return protoOptions.find(i => i.value == id)?.label || "";
};
export const getIdByName_proto = name => {
  return protoOptions.find(i => i.label == name)?.value;
};

export const getIdByName_action = name => {
  return actionOptions.find(i => i.label == name)?.value;
};

const getGroupIds = async () => {
  let res = {
    code: 200,
    data: [
      {
        id: 1,
        ns_name: "默认局域网（ABC类）",
        ns_type: 1,
        ip_ranges: "10.0.0.0-**************,**********-**************,***********-***************",
        description: "通常使用私有IP地址（A类、B类、C类）",
        enabled_sign: 1,
      },
    ],
  };
  if (res.code == 200) {
    groupIds.value = res.data?.map(item => {
      return {
        label: item.ns_name,
        value: item.ns_name,
        id: item.id,
      };
    });
  }
};

// op_type = 1 上 2 下 3 移动
export const updateTable = ({ op_type = 0, ids = [], rowIndex, direction = "forward" }, list) => {
  let newList = [],
    ids_first = null;
  let elements = list.filter(item => ids.includes(item.id));
  switch (op_type) {
    case 1:
      newList = list.slice();
      let haveStart = false;
      elements.forEach(element => {
        let index = newList.indexOf(element);
        if (index > 0) {
          let temp = newList[index - 1];
          newList[index - 1] = newList[index];
          newList[index] = temp;
        } else {
          haveStart = true;
        }
      });
      if (haveStart) {
        // 将第一项前置
        newList = newList.filter(item => item !== element[0]);
        newList.unshift(ids_first);
      }
      break;
    case 2:
      newList = list.slice();
      // 反向遍历进行重新排序，避免相邻元素索引变化导致排序出错
      const reverseElements = elements.slice().reverse();
      let haveEnd = false;
      reverseElements.forEach(element => {
        let index = newList.indexOf(element);
        if (index < newList.length - 1) {
          let temp = newList[index + 1];
          newList[index + 1] = newList[index];
          newList[index] = temp;
        } else {
          haveEnd = true;
        }
      });
      if (haveEnd) {
        // 将末尾项后置
        newList = newList.filter(item => item !== reverseElements[0]);
        newList.push(reverseElements[0]);
      }
      break;
    case 3:
      newList = list.map(item => (elements.includes(item) ? null : item));
      let index = 0;
      if (direction === "forward") {
        index = rowIndex - 1;
      } else {
        index = rowIndex;
      }
      if (index < 0) index = 0;
      newList.splice(index, 0, ...elements);
      newList = newList.filter(item => item !== null);
      break;
  }

  return newList;
};
