import { defineConfig } from "vite";
import path from "path";
import fs from "fs";
import vue from "@vitejs/plugin-vue";
import federation from "@originjs/vite-plugin-federation";
import { pluginExposeEntry, cfg_merge, pluginPaths, pluginBuildDir } from "./dynamic-path-config.mjs";
let srcPath, targetPath;

if (!process.env.SRC) {
  if (process.env.RESOURCE) {
    srcPath = process.env.RESOURCE;
    targetPath = pluginBuildDir(process.env.TARGET);
    console.log("自定义构建插件及其版本信息：", srcPath, targetPath);
  } else {
    // 默认构建插件第一个插件, 避免程序报错
    srcPath = pluginPaths()?.[0];
    targetPath = pluginBuildDir(srcPath);
    console.log("默认构建插件及其版本信息：", srcPath, targetPath);
  }
} else {
  srcPath = process.env.SRC;
  targetPath = process.env.TARGET;
  console.log("当前构建插件及其版本信息：", srcPath, targetPath);
}

// https://vitejs.dev/config/
export default defineConfig(async ({ mode, command }) => {
  return {
    base: "/",
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "./src"),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    // vite 相关配置
    server: {
      origin: "http://localhost:8030",
      port: 8030,
      host: true,
      open: true,
    },
    plugins: [
      vue(),
      federation({
        name: "plugin",
        filename: "/remoteEntry.js",
        exposes: { ...pluginExposeEntry(srcPath) },
        // exposes: {
        //   "./index": `./src/${srcPath}/${pluginVersion}/config/index.vue`, // 插件配置页面
        //   "./detail": `./src/${srcPath}/${pluginVersion}/config/detail.vue`, // 插件配置详情页面
        // },
        shared: {
          vue: {
            import: false,
            generate: false,
          },
          pinia: {
            generate: false,
          },
          "vue-router": {
            import: false,
            generate: false,
          },
          "@ouryun/ouryun-plus": {
            // todo
            // packagePath: 'node_modules/@ouryun/ouryun-plus/dist/index.umd.js',
            import: true,
            generate: false,
          },
          baseRequest: {
            packagePath: "./src/utils/request.js",
            import: true,
            generate: false,
          },
          basePluginStore: {
            packagePath: "./src/store/modules/plugin.js",
            import: true,
            generate: false,
          },
        },
      }),
      {
        name: "copy-js-plugin",
        writeBundle() {
          const { srcFilePath, distFilePath } = cfg_merge(srcPath, targetPath);
          // const srcPath = path.resolve(__dirname, `./src/views/plugins/${srcPath}/cfg_merge.js`);
          if (fs.existsSync(srcFilePath)) {
            // const distPath = path.resolve(__dirname, `./dist/${targetPath}/cfg_merge.js`);
            fs.copyFileSync(srcFilePath, distFilePath);
          }
        },
      },
      // AutoImport({
      //   imports: ['vue', 'vue-router','pinia'],
      //   dts: false,
      // }),
    ],
    build: {
      assetsDir: targetPath, // 插件打包目录
      target: "esnext",
      modulePreload: false,
      assetsInlineLimit: 40960,
      minify: true,
      cssCodeSplit: false,
      sourcemap: false,
      rollupOptions: {
        output: {
          minifyInternalExports: false,
        },
      },
      commonjsOptions: {
        transformMixedEsModules: true,
      },
    },
  };
});
