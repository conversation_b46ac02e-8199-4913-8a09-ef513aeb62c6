<template>
  <!--  列表移动组件 -->
  <div class="plugin-move-group">
    <el-dropdown @visible-change="visibleChange" trigger="click" :disabled="props.disabled">
      <div class="dropdown-btn">
        <ou-button type="primary" text :disabled="props.disabled">
          <span>移动</span>
          <ou-icon><CaretBottom /></ou-icon>
        </ou-button>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="moveUpClick">
            <div class="move-dropdown-item">
              <ou-icon><OrderAscending /></ou-icon>
              <span>上移</span>
            </div>
          </el-dropdown-item>
          <el-dropdown-item @click="moveDownClick">
            <div class="move-dropdown-item">
              <ou-icon><OrderDescending /></ou-icon>
              <span>下移</span>
            </div>
          </el-dropdown-item>
          <el-dropdown-item @click="moveClick">
            <div class="move-dropdown-item">
              <ou-icon><Sort /></ou-icon>
              <span>移动</span>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <ou-opera-dialog
      title="移动"
      v-model="moveDialogShow"
      @close="moveClose"
      @cancel="moveClose"
      @confirm="moveConfirm"
      :close-on-click-modal="false"
      class="move-form"
      width="590px"
      draggable>
      <template #content>
        <ou-form ref="moveFormRef" class="-mb-20" :rules="formRules" :model="moveForm" label-width="140px">
          <ou-form-item label="移动到：" prop="index" class="select-input-group-row">
            <template v-slot:label>
              <span>移动到：</span>
            </template>
            <div class="select-input-group">
              <ou-select v-model="moveForm.option" placeholder="请选择" :clearable="false">
                <ou-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"></ou-option>
              </ou-select>
              <ou-input
                v-model.number="moveForm.index"
                oninput="value=value.replace(/[^0-9]/g,'')"
                maxlength="3"
                style="width: 156px"
                placeholder="请输入序号"></ou-input>
            </div>
          </ou-form-item>
        </ou-form>
      </template>
      <template v-slot:footer>
        <ou-button @click="moveClose">取消</ou-button>
        <ou-button @click="moveConfirm" type="primary">确定</ou-button>
      </template>
    </ou-opera-dialog>
  </div>
</template>
<script setup>
  import { ref, reactive, defineEmits } from "vue";
  import { CaretBottom } from "@ouryun-plus/icons-vue";

  const props = defineProps({
    list: {
      type: Array,
      default() {
        return [];
      },
    },
    selections: {
      type: Array,
      default() {
        return [];
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    fixedCount: {
      type: Number,
      default: 0,
    },
  });

  const emits = defineEmits(["visibleChange", "update:list", "moved"]);

  const visibleChange = status => {
    emits("visibleChange", status);
  };

  // 对selections重新排序
  const reorderSubset = (listFull, listSubset) => {
    return listFull.filter(item => listSubset.includes(item));
  };

  // 上移/下移
  const stepMoveElements = (arr, elements, direction) => {
    let newArr = arr.slice();
    if (direction === "forward") {
      let haveStart = false;
      elements.forEach(element => {
        let index = newArr.indexOf(element);
        if (index > 0) {
          let temp = newArr[index - 1];
          newArr[index - 1] = newArr[index];
          newArr[index] = temp;
        } else {
          haveStart = true;
        }
      });
      if (haveStart) {
        // 将第一项前置
        newArr = newArr.filter(item => item !== elements[0]);
        newArr.unshift(elements[0]);
      }
    } else if (direction === "backward") {
      // 反向遍历进行重新排序，避免相邻元素索引变化导致排序出错
      const reverseElements = elements.slice().reverse();
      let haveEnd = false;
      reverseElements.forEach(element => {
        let index = newArr.indexOf(element);
        if (index < newArr.length - 1) {
          let temp = newArr[index + 1];
          newArr[index + 1] = newArr[index];
          newArr[index] = temp;
        } else {
          haveEnd = true;
        }
      });
      if (haveEnd) {
        // 将末尾项后置
        newArr = newArr.filter(item => item !== reverseElements[0]);
        newArr.push(reverseElements[0]);
      }
    }
    return newArr;
  };

  // 移动到指定行的前面或后面
  const moveElements = (list, selections, direction, rowIndex) => {
    let newList = list.map(item => (selections.includes(item) ? null : item));
    let index = 0;
    if (direction === "forward") {
      index = rowIndex - 1;
    } else {
      index = rowIndex;
    }
    if (index < 0) index = 0;
    newList.splice(index, 0, ...selections);
    newList = newList.filter(item => item !== null);

    return newList;
  };

  const moveUpClick = () => {
    let selections = reorderSubset(props.list, props.selections);
    const newList = stepMoveElements(props.list, selections, "forward");

    emits("update:list", newList);
    emits("moved", newList);
  };
  const moveDownClick = () => {
    let selections = reorderSubset(props.list, props.selections);
    const newList = stepMoveElements(props.list, selections, "backward");
    emits("update:list", newList);
    emits("moved", newList);
  };

  const validatorIndex = (rule, value, callback) => {
    if (value === "") {
      return callback(new Error("请输入序号"));
    }

    let startRestrict = 1;
    let endRestrict = props.list.length;

    if (props.fixedCount >= 1) {
      if (moveForm.option === "after") {
        startRestrict += props.fixedCount - 1;
      } else {
        startRestrict += props.fixedCount;
      }
    } else if (props.fixedCount <= -1) {
      if (moveForm.option === "after") {
        endRestrict += props.fixedCount;
      } else {
        endRestrict += props.fixedCount + 1;
      }
    }

    if (value < startRestrict || value > endRestrict) {
      return callback(new Error(`请输入${startRestrict}-${endRestrict}`));
    }
    callback();
  };

  const formRules = {
    index: [{ required: true, trigger: ["change", "blur"], validator: validatorIndex }],
  };

  const moveForm = reactive({
    option: "after",
    index: "",
  });

  const options = [
    {
      value: "before",
      label: "指定行之前",
    },
    {
      value: "after",
      label: "指定行之后",
    },
  ];

  const moveDialogShow = ref(false);
  const moveFormRef = ref(null);
  const moveClick = () => {
    moveDialogShow.value = true;
  };
  const moveClose = () => {
    moveDialogShow.value = false;
    setTimeout(() => {
      moveFormRef.value.resetFields();
      moveForm.option = "after";
      moveForm.index = "";
    }, 500);
  };
  const moveConfirm = () => {
    moveFormRef.value.validate(valid => {
      if (valid) {
        let selections = reorderSubset(props.list, props.selections);
        if (moveForm.option === "before") {
          const newList = moveElements(props.list, selections, "forward", +moveForm.index);
          emits("update:list", newList);
          emits("moved", newList);
        } else if (moveForm.option === "after") {
          const newList = moveElements(props.list, selections, "backward", +moveForm.index);
          emits("update:list", newList);
          emits("moved", newList);
        }
        moveClose();
      }
    });
  };
</script>
<style lang="scss" scoped>
  .plugin-move-group {
    display: inline-flex;
    .dropdown-btn {
      display: flex;
      .dropdown-btn-icon {
        margin-left: 2px;
        font-size: 16px;
      }
    }

    ::v-deep .move-form {
      .ouryun-form-item.select-input-group-row {
        .ouryun-form-item__label {
          width: 140px;
        }
        .select-input-group {
          width: 100%;
          display: flex;
          align-items: center;
          column-gap: 8px;
          .ouryun-select-custom {
            width: 200px;
            .ouryun-select__wrapper {
              box-shadow: 0 0 0 1px var(--ouryun-border-color) inset !important;
              &.is-hovering {
                box-shadow: 0 0 0 1px var(--ouryun-border-color-hover) inset !important;
              }

              &.is-focused {
                box-shadow: 0 0 0 1px var(--ouryun-color-primary) inset !important;
              }
            }
          }
          .ouryun-select-custom {
            flex: 1;
          }
        }
        &.is-error .ouryun-select .ouryun-input__wrapper {
          box-shadow: 0 0 0 1px var(--ouryun-input-border-color) inset;
        }
        .ouryun-form-item__error {
          margin-left: 216px;
        }
      }
    }
  }

  .move-dropdown-item {
    display: flex;
    align-items: center;
    column-gap: 4px;
    color: #333333;
  }
</style>
