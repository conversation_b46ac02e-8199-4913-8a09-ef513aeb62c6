<template>
  <!-- DDoS -->
  <div class="ddos-four-level ouryun-box-base">
    <div class="creat-configuration">
      <div class="add_title">
        <div class="add_titleName">基础信息</div>
      </div>
      <div class="add_conent">
        <div class="form-container">
          <ou-form :model="baseForm" :rules="baseRules" label-width="140px">
            <el-row class="flex" :gutter="80">
              <el-col :span="12">
                <ou-form-item label="实例名称：" prop="display_name">
                  <ou-input
                    :modelValue="baseForm.display_name"
                    placeholder="请输入"
                    maxlength="63"
                    show-word-limit
                    clearable
                    disabled />
                </ou-form-item>
              </el-col>
              <el-col :span="12">
                <ou-form-item label="选择插件：" prop="plugins">
                  <ou-select v-model="baseForm.plugins" placeholder="请选择" :clearable="false" disabled>
                    <ou-option
                      v-for="(item, index) in pluginsOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"></ou-option>
                  </ou-select>
                </ou-form-item>
              </el-col>
            </el-row>
            <el-row class="flex" :gutter="80">
              <el-col :span="24">
                <ou-form-item label="备注：">
                  <ou-input v-model="baseForm.notes" placeholder="请输入" maxlength="150" show-word-limit clearable />
                </ou-form-item>
              </el-col>
            </el-row>
          </ou-form>
        </div>
      </div>
      <div class="add_title">
        <div class="add_titleName">详细配置</div>
      </div>
      <div class="add_conent">
        <div class="form-container">
          <ou-form ref="baseFormRef" :model="form" :rules="rules" label-width="140px">
            <el-row class="flex" :gutter="80">
              <el-col :span="12">
                <ou-form-item label="SYN Flood防护：" prop="syn.enable">
                  <ou-switch
                    v-model="form.syn.enable"
                    active-text="启用"
                    inactive-text="禁用"
                    class="switch-offside"></ou-switch>
                </ou-form-item>
              </el-col>
              <template v-if="form.syn.enable">
                <el-col :span="12">
                  <ou-form-item label="动作：" prop="syn.action">
                    <ou-radio-group v-model="form.syn.action">
                      <ou-radio v-for="item in actionOptions" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </ou-radio>
                    </ou-radio-group>
                  </ou-form-item>
                </el-col>
                <el-col :span="12">
                  <ou-form-item label="每目的IP阈值：" prop="syn.dstMax">
                    <ou-input v-model.number="form.syn.dstMax" :maxlength="9">
                      <template #append>包/秒</template>
                    </ou-input>
                  </ou-form-item>
                </el-col>
                <el-col :span="12">
                  <ou-form-item label="每源IP阈值：" prop="syn.srcMax">
                    <ou-input v-model.number="form.syn.srcMax" :maxlength="9">
                      <template #append>包/秒</template>
                    </ou-input>
                  </ou-form-item>
                </el-col>
              </template>
            </el-row>

            <!-- 引擎暂不支持，先隐藏UDP Flood防护、DNS Flood防护 -->
            <!-- <el-row class="flex" :gutter="80">
              <el-col :span="12">
                <ou-form-item label="UDP Flood防护：" prop="udp.enable">
                  <ou-switch
                    v-model="form.udp.enable"
                    active-text="启用"
                    inactive-text="禁用"
                    class="switch-offside"></ou-switch>
                </ou-form-item>
              </el-col>
              <template v-if="form.udp.enable">
                <el-col :span="12">
                  <ou-form-item label="动作：" prop="udp.action">
                    <ou-radio-group v-model="form.udp.action">
                      <ou-radio v-for="item in actionOptions" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </ou-radio>
                    </ou-radio-group>
                  </ou-form-item>
                </el-col>
                <el-col :span="12">
                  <ou-form-item label="阈值：" prop="udp.max">
                    <ou-input v-model.number="form.udp.max" :maxlength="9">
                      <template #append>包/秒</template>
                    </ou-input>
                  </ou-form-item>
                </el-col>
              </template>
            </el-row>

            <el-row class="flex" :gutter="80">
              <el-col :span="12">
                <ou-form-item label="DNS Flood防护：" prop="dns.enable">
                  <ou-switch
                    v-model="form.dns.enable"
                    active-text="启用"
                    inactive-text="禁用"
                    class="switch-offside"></ou-switch>
                </ou-form-item>
              </el-col>
              <template v-if="form.dns.enable">
                <el-col :span="12">
                  <ou-form-item label="动作：" prop="dns.action">
                    <ou-radio-group v-model="form.dns.action">
                      <ou-radio v-for="item in actionOptions" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </ou-radio>
                    </ou-radio-group>
                  </ou-form-item>
                </el-col>
                <el-col :span="12">
                  <ou-form-item label="阈值：" prop="dns.max">
                    <ou-input v-model.number="form.dns.max" :maxlength="9">
                      <template #append>包/秒</template>
                    </ou-input>
                  </ou-form-item>
                </el-col>
              </template>
            </el-row> -->
            <el-row class="flex" :gutter="80">
              <el-col :span="12">
                <ou-form-item label="ICMP Flood防护：" prop="icmp.enable">
                  <ou-switch
                    v-model="form.icmp.enable"
                    active-text="启用"
                    inactive-text="禁用"
                    class="switch-offside"></ou-switch>
                </ou-form-item>
              </el-col>
              <template v-if="form.icmp.enable">
                <el-col :span="12">
                  <ou-form-item label="动作：" prop="icmp.action">
                    <ou-radio-group v-model="form.icmp.action">
                      <ou-radio v-for="item in actionOptions" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </ou-radio>
                    </ou-radio-group>
                  </ou-form-item>
                </el-col>
                <el-col :span="12">
                  <ou-form-item label="阈值：" prop="icmp.max">
                    <ou-input v-model.number="form.icmp.max" :maxlength="9">
                      <template #append>包/秒</template>
                    </ou-input>
                  </ou-form-item>
                </el-col>
              </template>
            </el-row>
          </ou-form>
        </div>
      </div>

      <div class="FooterBtn" v-fixed>
        <ou-button @click="back">取消</ou-button>
        <ou-button @click="submit" type="primary" :loading="isSubmitLoading">提交</ou-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { defineProps } from "vue";
  import { useRouter } from "vue-router";
  import { saveLayer4PluginInstance } from "@/api/plugin.js";
  import hookStates from "../hook/index.js";
  import { OuModal } from "@ouryun/ouryun-plus";

  const router = useRouter();

  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    global: {
      type: String,
      default: "",
    },
    matedata: {
      type: Object,
      default: () => ({}),
    },
  });

  const { isSubmitLoading, baseForm, baseFormRef, baseRules, Form, form, rules, pluginsOptions, actionOptions } =
    hookStates();

  const initForm = () => {
    const { display_name, notes } = props.matedata;
    baseForm.display_name = display_name;
    baseForm.notes = notes;
    if (!props.global) return;
    const { configs } = JSON.parse(JSON.parse(props.global).plugin_config.rules);
    const synSrcInfo = configs.find(item => item.type === "syn_src");
    const synDstInfo = configs.find(item => item.type === "syn_dst");

    form.syn = {
      enable: synSrcInfo.enable,
      action: synSrcInfo.action,
      srcMax: synSrcInfo.max,
      dstMax: synDstInfo.max,
    };
    ["udp", "dns", "icmp"].forEach(field => {
      const info = configs.find(item => item.type === field);
      form[field] = {
        enable: info.enable,
        action: info.action,
        max: info.max,
      };
    });
  };

  initForm();

  const back = () => {
    router.go(-1);
  };

  const submit = async () => {
    baseFormRef.value.validate(valid => {
      if (!valid) return;
      const formBackup = new Form();
      const configsParams = [
        {
          type: "syn_src",
          enable: form.syn.enable,
          action: form.syn.enable ? form.syn.action : formBackup.syn.action,
          max: form.syn.enable ? +form.syn.srcMax : +formBackup.syn.srcMax,
        },
        {
          type: "syn_dst",
          enable: form.syn.enable,
          action: form.syn.enable ? form.syn.action : formBackup.syn.action,
          max: form.syn.enable ? +form.syn.dstMax : +formBackup.syn.dstMax,
        },
      ];
      configsParams.push(
        ...["udp", "dns", "icmp"].map(item => {
          return {
            type: item,
            enable: form[item].enable,
            action: form[item].enable ? form[item].action : formBackup[item].action,
            max: form[item].enable ? +form[item].max : +formBackup[item].max,
          };
        }),
      );
      const params = {
        plugin_config: {
          type: 2,
          rules: JSON.stringify({
            configs: configsParams,
          }),
        },
        meta_data: [],
      };
      const { id, display_name } = props.matedata;
      const body = {
        display_name,
        id,
        globalContent: JSON.stringify(params),
        notes: baseForm.notes,
      };

      isSubmitLoading.value = true;

      saveLayer4PluginInstance(body)
        .then(res => {
          const { message, code } = res;
          if (code === 200) {
            console.log(message);
            OuModal.success("提交成功");
            back();
          } else {
            OuModal.warning(message);
          }
        })
        .finally(() => {
          isSubmitLoading.value = false;
        });
    });
  };
</script>
<style lang="scss" scoped>
  .ddos-four-level {
    min-height: 100%;
  }
</style>
