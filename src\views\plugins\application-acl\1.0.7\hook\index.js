import { reactive, ref } from "vue";

export default function () {
  const actionOptions = [
    {
      label: "放行",
      value: 0,
    },
    {
      label: "拒绝",
      value: 1,
    },
    {
      label: "仅记录",
      value: 2,
    },
  ];

  const typeOptions = [
    {
      label: "文本",
      value: "text",
    },
    {
      label: "HTML",
      value: "html",
    },
    {
      label: "JSON",
      value: "json",
    },
  ];

  const validateMatch = (rule, value, callback) => {
    if (!value.length && !dialogForm.isDefault) {
      return callback(new Error("请添加匹配条件"));
    }
    if (value.length > 10) {
      const message = "规则条数已超过上限10条，请进行清理后再提交";
      ElMessage.success(message);
      return callback(new Error(message));
    }
    if (value.some(item => item.status === "edit")) {
      return callback(new Error("当前正处于编辑状态"));
    } else {
      callback();
    }
  };

  const dialogFormRules = {
    ruleName: [{ required: true, trigger: ["change", "blur"], message: "请输入规则名称" }],
    matchingList: [{ required: true, trigger: [], validator: validateMatch }],
    type: [{ required: true, trigger: ["change", "blur"], message: "请选择类型" }],
  };

  class DialogForm {
    ruleName = "";
    matchingList = [];
    act = 1;
    type = "text";
    denyContext = "";
    enable = true;
    isDefault = false;
  }

  const dialogForm = reactive(new DialogForm());
  const matchConditionCollapse = ref(true);

  return {
    actionOptions,
    typeOptions,
    dialogFormRules,
    DialogForm,
    dialogForm,
    matchConditionCollapse,
  };
}
