// // 颜色定义规范(参照蓝湖)
// // 1.品牌色
// $--brand-color: var(--ouryun-color-brand-base);

// // 2.文字颜色
// $--text-color-main: #333; // 主要
// $--text-color-secondary: #666; // 次要
// $--text-color-tips: #999; // 提示
// $--text-color-placeholder: $--text-color-tips; // 输入框提示

// 3.功能色
$--color-main-bg: #edeff4; //主体的背景色
$--color-primary: $--brand-color; // 沿用品牌色
$--color-primary-disabled: #99c2ff; // 蓝色底禁用颜色
$--color-success: #52c41a; // 成功
$--color-danger: #f56c6c; // 失败
$--color-warning: #faad15; // 警告
$--color-info: #909399; // 信息

// 4.中性色
$--border-color-default: #d9d9d9; // 边框基础色
$--border-color-dialog: #eeeeee;
$--divider-color-default: #e5e5e5; // 分割线基础色

// // 5.字体
// $--font-family-default: "思源黑体"; //公共字体

// // 6.边距

// 有需要使用其他颜色变量再补充抛出
:export {
  borderColor: $--border-color-default;
  textTipsColor: $--text-color-tips;
  textSecondaryColor: $--text-color-secondary;
  primaryColor: $--brand-color;
  dangerColor: $--color-danger;
}
