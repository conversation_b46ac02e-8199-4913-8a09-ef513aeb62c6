import { ref, reactive, computed } from "vue";
import { deepClone } from "@/utils/index.js";

const positionSelect = [
  { label: "Request-Header", value: 0 },
  { label: "Request-Cookie", value: 2 },
  { label: "Request-Body", value: 1 },
  { label: "Request-Param", value: 3 },
];

const tokenSelect = [
  { label: "Response-Header", value: 4 },
  { label: "Response-Body", value: 5 },
  { label: "Response-SetCookie", value: 6 },
];

const positionMap = {
  0: "Request-Header",
  2: "Request-Cookie",
  1: "Request-Body",
  3: "Request-Param",
  4: "Response-Header",
  5: "Response-Body",
  6: "Response-SetCookie",
};

const userParmaSelect = [
  { label: "user", value: "user" },
  { label: "username", value: "username" },
  { label: "name", value: "name" },
  { label: "user_ID", value: "user_ID" },
  { label: "login_name", value: "login_name" },
  { label: "account", value: "account" },
  { label: "account_name", value: "account_name" },
  { label: "email", value: "email" },
  { label: "email_address", value: "email_address" },
  { label: "telephone", value: "telephone" },
  { label: "nickname", value: "nickname" },
];

const pwdParmaSelect = [
  { label: "password", value: "password" },
  { label: "pw", value: "pw" },
  { label: "code", value: "code" },
  { label: "key", value: "key" },
  { label: "PIN", value: "PIN" },
];

const tokenParmaSelect = [
  { label: "known_sign_in", value: "known_sign_in" },
  { label: "token", value: "token" },
  { label: "session-id", value: "session-id" },
  { label: "cookie", value: "cookie" },
  { label: "session", value: "session" },
  { label: "login", value: "login" },
  { label: "signin", value: "signin" },
  { label: "session_key", value: "session_key" },
];

let initRuleItem = {
  user_name: [
    {
      keys: [0, 1, 2, 3],
      values: "user,username,name,user_ID,login_name,account,account_name,email,email_address,telephone,nickname",
    },
  ],
  password_name: [
    {
      keys: [0, 1, 2, 3],
      values: "password,pw,code,key,PIN",
    },
  ],
  path: ["-"],
  status_code: ["200"],
  token: [
    {
      keys: [4, 5, 6],
      values: "known_sign_in,token,session-id,cookie,session,login,signin,session_key",
    },
  ],
  rule_base: [],
  ruleEditData: [
    {
      value: 1,
      is_default: true,
      name: "内置弱密码库",
    },
  ],
  status: true,
  isDefault: true,
  rowId: `${Date.now()}`,
};

// 兼容历史数据
export const compateData = (highList, rule, baseSelect) => {
  let form = {};
  if (highList) {
    let ruleList = [];
    if (highList.length === 0) {
      ruleList.push(initRuleItem);
    } else {
      console.log("highList", "ddddddd");
      let is_default = false;
      let num = 0;
      highList.forEach(e => {
        let a = deepClone(initRuleItem);
        if (e.rule == "0") {
          // 如果含有自动的数据,表示含有默认规则
          is_default = true;
        }
        if (rule == "1" || e.rule == "0") {
          a.isDefault = true;
          // if (a.rule_base.length === 0) {
          //   a.ruleEditData = [];
          // }
        } else {
          num++;
          a.isDefault = false;
          a.user_name[0].values = e.userName;
          a.password_name[0].values = e.pwdField;
          const defaultSelect = baseSelect.find(i => i.is_default);
          const restSelect = baseSelect.filter(i => !i.is_default).slice(0, 5) || [];
          console.log("restSelect", restSelect);
          restSelect.push(defaultSelect);
          a.rule_base = restSelect?.map(i => i.value);
          a.ruleEditData = restSelect?.map(i => ({
            value: i.value,
            name: i.name,
          }));
        }
        ruleList.push(a);
      });
      console.log("is_default", is_default, num);
      if (!is_default && num > 0) {
        // 如果没有默认规则，则添加一条默认规则
        ruleList.push(initRuleItem);
      }
    }
    form.ruleList = ruleList;
    form.multipleList = [];
  }
  return form;
};

const baseForm = ref({
  ruleList: [deepClone(initRuleItem)],
  multipleList: [],
});
const ruleForm = ref({
  userName: [
    {
      keys: [],
      values: "",
    },
  ],
  passwordName: [
    {
      keys: [],
      values: "",
    },
  ],
  path: [],
  status_code: ["200"],
  tokenList: [
    {
      keys: [],
      values: "",
    },
  ],
  rule_base: [],
});
const dialog = ref({
  show: false,
  title: {
    add: "添加",
    edit: "编辑",
  },
  type: "add",
});

const validateStatus = (rule, value, callback) => {
  if (value) {
    const codeList = value.split("\n");
    const isTrim = codeList.some(e => /\s/.test(e));
    const isCode = codeList.some(e => !/^(2[0-9]{2}|3[0-9]{2}|4[0-9]{2}|5[0-9]{2})$/.test(e));
    if (isTrim || isCode) {
      callback(new Error("请输入正确的状态码"));
    }
  } else {
    callback(new Error("请输入状态码"));
  }
  callback();
};

const validatePath = (rule, value, callback) => {
  if (value) {
    const codeList = value.split("\n");
    const isTrim = codeList.some(e => /\s/.test(e));
    const isCode = codeList.some(e => !/^[A-Za-z0-9!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]+$/.test(e));
    if (isTrim || isCode) {
      callback(new Error("请输入正确的路径"));
    }
  } else {
    callback(new Error("请输入路径"));
  }
  callback();
};

const validateParma = (rule, value, callback) => {
  if (value) {
    const codeList = value.split("\n");
    const isTrim = codeList.some(e => /\s/.test(e));
    const isCode = codeList.some(e => !/^[A-Za-z0-9!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]+$/.test(e));
    if (isTrim || isCode) {
      callback(new Error("请输入正确的识别参数"));
    }
  } else {
    callback(new Error("请输入识别参数"));
  }
  callback();
};

const validateBase = (rule, value, callback) => {
  if (value && value.length) {
    if (value.length > 5) {
      callback(new Error("最多选择5条弱密码库"));
    }
  } else {
    callback(new Error("请选择弱密码库"));
  }
  callback();
};

const codeTextAreaRef = ref(null);
const pathTextAreaRef = ref(null);
const ruleBaseList = ref([]);
const formRef = ref(null);
const rules = {};
const keysRules = { required: true, message: "请选择识别位置", trigger: "change" };
const valuesRules = { required: true, trigger: "change", validator: validateParma };
const formRules = {
  rule_base: [{ required: true, trigger: "change", validator: validateBase }],
  path: [{ required: true, message: "", trigger: "change" }],
  status_code: [{ required: true, message: "", trigger: "change" }],
};

const config_position = data => {
  const position = (data || []).map(e => e.keys).flat();
  const mapPosition = position.map(e => positionMap[e]);
  const params = (data || []).map(e => e.values);
  const setPosition = [...new Set([...mapPosition])];
  const setParams = [...new Set([...params])];
  return `[${setPosition.join(",")};${setParams.join(",")}]`;
};

const mapTableContent = (row, fieldType) => {
  const isPositionList = ["user_name", "password_name", "token"];
  const spitList = ["status_code", "path"];
  if (isPositionList.includes(fieldType)) {
    return config_position(row[fieldType]);
  } else if (spitList.includes(fieldType)) {
    return Array.isArray(row[fieldType]) ? row[fieldType].join(",") : row[fieldType].split("\n").join(",");
  } else if (fieldType === "rule_base") {
    return row["ruleEditData"].map(e => e.name).join(",") || "-";
  } else {
    return row[fieldType] ? row[fieldType].split("\n").join(",") : "-";
  }
};

const validateCode = value => {
  const regex = /^\d+$/;
  const isCode = regex.test(value) && Number(value) >= 200 && Number(value) <= 599;
  if (!value) return { isValid: true, message: "" };
  if (isCode) {
    return { isValid: true, message: "" };
  } else {
    return { isValid: false, message: `检验不通过，应输入200-599的整数` };
  }
};

const validatePaths = value => {
  const isPath = /^[A-Za-z0-9!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]+$/.test(value);
  if (!value) return { isValid: true, message: "" };
  if (isPath) {
    return { isValid: true, message: "" };
  } else {
    return { isValid: false, message: `请输入英文、数字、英文标点符号` };
  }
};
export default function () {
  return {
    baseForm,
    ruleForm,
    dialog,
    ruleBaseList,
    formRef,
    keysRules,
    valuesRules,
    rules,
    formRules,
    positionSelect,
    userParmaSelect,
    pwdParmaSelect,
    tokenParmaSelect,
    tokenSelect,
    positionMap,
    mapTableContent,
    validateCode,
    validatePaths,
    codeTextAreaRef,
    pathTextAreaRef,
  };
}
