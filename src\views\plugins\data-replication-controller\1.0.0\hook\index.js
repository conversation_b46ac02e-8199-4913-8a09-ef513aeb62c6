import { reactive, ref } from "vue";
import { OuModal } from "@ouryun/ouryun-plus";

export default function () {
  const protectionModeOptions = [
    {
      label: "拒绝",
      value: 0,
    },
    {
      label: "放行",
      value: 1,
    },
  ];

  const configRuleDefault = {
    ruleName: "默认规则",
    matchingList: [],
    protectionMode: 0,
    type: "text",
    copyWatermark: "",
    isCollapse: true,
    enable: true,
  };

  const validateMatch = (rule, value, callback) => {
    if (!value.length) {
      return callback(new Error("请添加匹配条件"));
    } else {
      const allValid = value.every(
        item =>
          item.field && item.logic && (Array.isArray(item.content) ? item.content.length > 0 : item.content !== ""),
      );
      if (!allValid) {
        return callback(new Error("请输入完成的匹配条件"));
      } else {
        callback();
      }
      callback();
    }
  };
  const ruleNameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9.。:：\-_\@\（\）\(\)#]+$/;

  const dialogFormRules = {
    ruleName: [
      { required: true, trigger: ["change", "blur"], message: "请输入规则名称" },
      {
        required: true,
        pattern: ruleNameRegex,
        message: "规则名称有误",
        trigger: "change",
      },
    ],
    matchingList: [{ required: true, trigger: [], validator: validateMatch }],
    type: [{ required: true, trigger: ["change", "blur"], message: "请选择类型" }],
  };

  class DialogForm {
    ruleName = "";
    matchingList = [];
    protectionMode = 0;
    type = "text";
    enable = true;
    copyWatermark = "";
    isDefault = false;
  }

  const dialogForm = reactive(new DialogForm());

  const copyWatermarkPreviewShow = ref(false);

  return {
    protectionModeOptions,
    configRuleDefault,
    dialogFormRules,
    DialogForm,
    dialogForm,
    copyWatermarkPreviewShow,
  };
}
