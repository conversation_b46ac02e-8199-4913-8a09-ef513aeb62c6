import { getMatchParameter } from "@/utils/MatchedCondition.js";
import { parseIpEntry } from "@/utils/tool";
import { getTmData } from "@/views/plugins/data-masking/1.0.3/hook/apiData";
import { ref } from "vue";
//脱敏相关所有数据
const tmData = ref(null);
//初始化脱敏策略数据
export const initMaskingStrategy = tmData => {
  //初始加载隐藏初始默认的策略
  const filterId = [100040, 100041];
  const data = tmData.desensitization.filter(e => (e.id ? !filterId.includes(e.id) : e.name));
  return data
    .filter(e => e.source === 1)
    .map(e => ({
      ruleName: `内置规则_${e.ruleName}`,
      rule: e.id ?? e.name,
      matchRule: [],
      Match: "任意",
      sensitiveTypeName: e.data_label_id ?? identity.name,
      enable: true,
      result: e.outcome,
      sample: e.testData,
    }));
};

//配置每个脱敏类型的接口提交结构
export const configRewriteRule = key => {
  const { desensitization, samples } = tmData.value;
  const item = desensitization.find(e => e.id === key || e.name === key);
  switch (item.type) {
    case 1:
      const { cover } = item;
      const cover_rewrite = {
        cover_mode: cover.mode,
        cover_character: cover.character,
      };
      if (cover.mode === 5) {
        cover_rewrite.rules = cover.customValue;
        cover_rewrite.cover_type = cover.customRule;
      }
      return {
        cover_rewrite,
      };
    case 2:
      const { replace } = item;
      const VALUE_ENUM = {
        1: replace.fixedValue,
        2: replace.randomValue,
        3: "",
      };
      const replace_rewrite = {
        rule_type: replace.rule,
        replace_value: VALUE_ENUM[replace.type],
        value_type: replace.type,
      };
      if (replace.type === 3) {
        const { id, name, sampleName } = replace.sample;
        let item = {};
        if (name) {
          // 兼容历史数据
          item = samples.find(e => e.name === name || e.sampleName === sampleName);
        } else {
          item = samples.find(e => e.sampleName === sampleName || e.id === id);
        }
        const content = item?.content ? item.content.replaceAll("\n", ",") : "";
        // console.log("replace", replace, samples, content);
        replace_rewrite.sampleValue = {
          unique_sample_id: name,
          sample_content: content,
        };
      }
      if (replace.rule >= 0 && replace.rule < 4) {
        replace_rewrite.replace = {};
      } else if (replace.rule === 4) {
        replace_rewrite.regexMatch = { regex: replace.regex };
      } else if (replace.rule === 5) {
        replace_rewrite.custom = {
          rules: replace.customValue,
          cover_type: replace.customRule,
        };
      }
      return {
        replace_rewrite,
      };
    case 3:
      const { transformation } = item;
      const ENUM_DATA = {
        1: {
          numeric_round: {
            decimal_places: transformation.decimalPrecision,
          },
        },
        2: {
          date_round: {
            level: transformation.roundedDateLevel,
          },
        },
        3: {
          character_shift: {
            direction: transformation.direction,
            shift_amount: transformation.offset,
          },
        },
      };
      return {
        transform_rewrite: {
          transform_type: transformation.type,
          ...ENUM_DATA[transformation.type],
        },
      };
    case 4:
      return {
        shuffleRewrite: {},
      };
    case 5:
      const { hashEncryption } = item;
      return {
        hash_encrypt_rewrite: {
          encryption_algorithm: hashEncryption.algorithms,
          salt_value: hashEncryption.saltValue,
        },
      };
    case 6:
      const { encryption } = item;
      return {
        encrypt_algorithm_rewrite: {
          encryption_algorithm: encryption.algorithms,
          encryption_key: encryption.key,
        },
      };
  }
};

//配置白名单数据
export const configWhiteList = data => {
  const ipWhitelist = [];
  const userWhitelist = [];
  return data.reduce((acc, cur) => {
    const discern = tmData.value.identityRule.find(e => e.name === cur.sensitiveType);
    const sensitiveNameList = tmData.value.identityRule.reduce((acc, cur) => {
      acc[cur.id] = cur.name;
      return acc;
    }, {});
    if (cur.conditionType === 1) {
      const userList =
        !cur.userWhitelist && cur.accountDisplay
          ? cur.accountDisplay.map(e => e.label)
          : cur.userWhitelist.split("\n").filter(e => e);
      userWhitelist.push({
        user: userList.map(e => ({
          user_name: e,
        })),
        enable: cur.enable,
        sensitive_type: configLabelName(sensitiveNameList, cur.sensitiveType, discern),
        sensitive_type_all: cur.sensitiveType.includes("all"),
      });
    } else {
      ipWhitelist.push({
        ip_whitelist: parseIpEntry(cur.ipWhitelist),
        enable: cur.enable,
        sensitive_type: configLabelName(sensitiveNameList, cur.sensitiveType, discern),
        sensitive_type_all: cur.sensitiveType.includes("all"),
      });
    }
    acc["ip_whitelist"] = ipWhitelist;
    acc["user_whitelist"] = userWhitelist;
    return acc;
  }, {});
};
const configLabelName = (labelMap, sensitiveType, discern) => {
  if (discern) return discern.name;
  const str = sensitiveType
    .split(",")
    .map(e => labelMap[e] || e)
    .join(",");
  return str;
};
//配置策策略表
export const configRule = tacticsData => {
  return tacticsData.reduce((acc, cur, index) => {
    const item = {
      id: index + 1,
      enable: cur.enable,
      rewrite_strategy_name: cur.ruleName,
      count: cur.maskingData && cur.maskingData.length ? cur.maskingData.length : 1,
      rewrite_data: configMaskingItem(cur),
      ...getMatchParameter(cur.matchRule),
    };
    acc.push(item);
    return acc;
  }, []);
};

//配置提交单条脱敏结构
const configMaskingItem = tacticsItem => {
  const list = tacticsItem.maskingData ? tacticsItem.maskingData : [tacticsItem];
  return list.reduce((acc, cur, index) => {
    const discern = tmData.value.identityRule.find(e => e.id == cur.sensitiveTypeName);
    const matchRule = (discern.rule.identification_rules || []).map(e => e.match_rules).flat();
    const ignoreRule = (discern.rule.ignore_rules || []).map(e => e.match_rules).flat();
    const match_rule = matchRule.map((v, i) => ({
      rule_content: v.Content,
      id: i + 1,
      type: v.match_method,
    }));
    const ignore_rule = ignoreRule.map((v, i) => ({
      rule_content: v.Content,
      id: i + 1,
      type: v.match_method,
    }));
    const item = {
      id: index + 1,
      rewrite_rule: {
        sensitive_type: discern.name,
        unique_rewrite_id: `${cur.rule}`,
        ...configRewriteRule(cur.rule),
      },
      identify_rule: {
        unique_identify_id: discern.id,
        sensitive_type: discern.name,
        match_rule,
        ignore_rule,
      },
    };
    acc.push(item);
    return acc;
  }, []);
};

//配置插件提交数据
export const configSubmitData = form => {
  const { tacticsList, whiteList, statusCode, status_codes: statusCode1, compress, astrictSize } = form;
  const { count: c, unit = "kb" } = astrictSize;
  let count = c === "" || typeof c === "undefined" || c === null ? 1024 : c;
  const buffer = unit === "mb" ? count * 1024 * 1024 : count * 1024;
  const data = {
    // status_codes: (Array.isArray(statusCode) ? statusCode : statusCode.split("\n"))
    //   .filter(item => item)
    //   .map(item => +item),
    status_codes: statusCode1.filter(item => item).map(item => +item),
    compressor_enable: compress === "1",
    rules: configRule(tacticsList),
    whitelist: configWhiteList(whiteList),
    buffer,
  };
  return data;
};

//获取脱敏数据
export const getTmList = () => {
  return getTmData().then(res => {
    const dataList = res && res.length ? res : [];
    tmData.value = dataList.reduce((acc, cur) => {
      const { type, data } = cur;
      acc[type] = data.list ? data.list || data.identityRule : [];
      return acc;
    }, {});
  });
};

export function useState() {
  const baseForm = ref({
    isHidden: true,
    tacticsList: [],
    whiteList: [],
    compress: "1",
    statusCode: "",
    status_codes: [],
    astrictSize: {
      count: "",
      unit: "kb",
    },
  });
  const flowList = {
    kb: "KB",
    mb: "MB",
  };
  //校验表单状态码
  // const validateCode = (rule, value, callback) => {
  //   if (value) {
  //     const codeList = value;
  //     const isTrim = codeList.some(e => /\s/.test(e));
  //     const isCode = codeList.some(e => !/^(100|[1-5]\d{2}|600)$/.test(e));
  //     if (isTrim || isCode) {
  //       callback(new Error("请输入正确的状态码"));
  //     }
  //   }
  //   callback();
  // };
  //校验表单缓冲区大小
  const validateSize = (rule, value, callback) => {
    const unit = baseForm.value.astrictSize.unit;
    const MSG = "范围限制256KB到50MB";
    const MAX = 50 * 1024;
    const MIN = 256;
    if (value) {
      const buffer = unit === "kb" ? value : value * 1024;
      if (buffer > MAX || buffer < MIN) {
        return callback(new Error(MSG));
      }
    }
    callback();
  };

  const rules = {
    statusCode: [{ required: false }],
    "astrictSize.count": { validator: validateSize, trigger: "change" },
  };
  return {
    baseForm,
    flowList,
    tmData,
    rules,
  };
}
