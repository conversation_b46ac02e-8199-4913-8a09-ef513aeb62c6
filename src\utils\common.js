/**
 * 通用js方法封装处理
 */
import { onMounted, onUnmounted } from "vue";
// import useEngineStore from "@/store/modules/engine.js";
import { ulid } from "ulid";
// input number限制
export function inputNum(proxy, listREF) {
  function onKeyDown(event) {
    // 阻止除数字和退格键之外的所有按键
    const keyCode = event.keyCode;
    const isNumberKey = !isNaN(Number(event.key)) || keyCode === 8; // 8 是 Backspace 键的 keyCode
    if (!isNumberKey) {
      event.preventDefault();
    }
  }
  onMounted(() => {
    // 在组件挂载后聚焦输入框并添加键盘监听器
    listREF.forEach(element => {
      if (proxy.$refs[element]) {
        proxy.$refs[element].$el.addEventListener("keydown", onKeyDown);
      } else {
        console.log("未找到元素" + element);
      }
    });
  });
  // 在组件卸载前移除键盘监听器
  onUnmounted(() => {
    listREF.forEach(element => {
      if (proxy.$refs[element]) {
        proxy.$refs[element].$el.removeEventListener("keydown", onKeyDown);
      }
    });
  });
}
// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === "string") {
      time = time
        .replace(new RegExp(/-/gm), "/")
        .replace("T", " ")
        .replace(new RegExp(/\.[\d]{3}/gm), "");
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

export function ulidEncrypt(val) {
  return ulid().toLowerCase();
}

//权限检测
export const CheckOperator = () => {
  return true;
  const engineStore = useEngineStore();
  let message = "";
  if (engineStore.role != "operator") {
    message = "当前用户暂无操作权限";
  } else if (engineStore.isExpired) {
    message = "引擎已到期，无法继续操作";
  }
  if (message) {
    ElMessage({
      message,
      type: "warning",
    });
    return false;
  } else {
    return true;
  }
};
