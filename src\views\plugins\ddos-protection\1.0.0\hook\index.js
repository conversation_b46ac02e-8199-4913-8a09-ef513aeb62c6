import { reactive, ref } from "vue";

export default function () {
  const isSubmitLoading = ref(false);

  const baseForm = reactive({
    display_name: "DDoS防护",
    plugins: "www.srhino.com.ddos-protection",
    notes: "",
  });

  const baseFormRef = ref(null);
  const baseRules = [];

  class Form {
    syn = {
      enable: false,
      action: "logging",
      srcMax: 1024,
      dstMax: 4096,
    };
    udp = {
      enable: false,
      action: "logging",
      max: 20480,
    };
    dns = {
      enable: false,
      action: "logging",
      max: 4096,
    };
    icmp = {
      enable: false,
      action: "logging",
      max: 2048,
    };
  }

  const form = reactive(new Form());

  const validatorThreshold = (rule, value, callback) => {
    if (!/^(100000000|[1-9]\d{0,7})$/.test(value)) {
      return callback(new Error("请输入1-100000000的整数"));
    }
    callback();
  };

  const rules = {
    "syn.dstMax": [
      {
        required: true,
        message: "请输入每目的IP阈值",
        trigger: ["change", "blur"],
      },
      {
        trigger: ["change", "blur"],
        validator: validatorThreshold,
      },
    ],
    "syn.srcMax": [
      {
        required: true,
        message: "请输入每源IP阈值",
        trigger: ["change", "blur"],
      },
      {
        trigger: ["change", "blur"],
        validator: validatorThreshold,
      },
    ],
    "udp.max": [
      {
        required: true,
        message: "请输入阈值",
        trigger: ["change", "blur"],
      },
      {
        trigger: ["change", "blur"],
        validator: validatorThreshold,
      },
    ],
    "dns.max": [
      {
        required: true,
        message: "请输入阈值",
        trigger: ["change", "blur"],
      },
      {
        trigger: ["change", "blur"],
        validator: validatorThreshold,
      },
    ],
    "icmp.max": [
      {
        required: true,
        message: "请输入阈值",
        trigger: ["change", "blur"],
      },
      {
        trigger: ["change", "blur"],
        validator: validatorThreshold,
      },
    ],
  };

  const pluginsOptions = [
    {
      label: "DDoS防护",
      value: "www.srhino.com.ddos-protection",
    },
  ];
  const actionOptions = [
    {
      label: "丢弃",
      value: "discard",
    },
    {
      label: "仅记录",
      value: "logging",
    },
  ];

  return {
    isSubmitLoading,
    baseForm,
    baseFormRef,
    baseRules,
    Form,
    form,
    rules,
    pluginsOptions,
    actionOptions,
  };
}
