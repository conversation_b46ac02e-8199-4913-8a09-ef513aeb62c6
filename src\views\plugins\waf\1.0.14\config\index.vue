<template>
  <div class="waf-pg-config" v-loading="isLoading">
    <ou-form labelPosition="right" ref="formRef" label-width="140px" :model="baseForm" :rules="rules">
      <div class="content-85">
        <el-row class="flex" :gutter="80">
          <el-col :span="12">
            <ou-form-item label="防护模式：">
              <ou-radio-group v-model="baseForm.defendMode">
                <ou-radio value="1">正常模式</ou-radio>
                <ou-radio value="0">仅检测模式</ou-radio>
              </ou-radio-group>
              <ou-tooltip-icon
                style="margin-left: 40px; position: relative; font-size: 16px"
                content="当匹配上防护规则时，正常模式会进行拦截，仅检测模式只会检测风险，不会阻止继续访问"></ou-tooltip-icon>
            </ou-form-item>
          </el-col>
          <el-col :span="12">
            <ou-form-item label="防护等级：">
              <ou-select v-model="baseForm.defendLevel" placeholder="请选择" :clearable="false">
                <ou-option
                  v-for="item in selects.level"
                  :key="item.value"
                  v-show="item.normal"
                  :label="item.label"
                  :value="item.value"></ou-option>
              </ou-select>
              <ou-tooltip-icon
                class="waf-tooltip tips-position"
                style="position: absolute; top: 16px; right: -24px; font-size: 16px">
                <template #content>
                  <div>
                    防护等级越高，拦截的攻击类型越多，但也存在误报的风险。
                    防护等级越低，误报风险越小，但不能拦截隐蔽的攻击。
                  </div>
                  <div class="quota-group-tips">
                    <div v-for="item in selects.level" class="quota-group-tips-item">
                      <span class="label" style="font-weight: 600">{{ item.label }}：</span>
                      <span class="value">{{ item.desc }}</span>
                    </div>
                  </div>
                </template>
              </ou-tooltip-icon>
            </ou-form-item>
          </el-col>
          <el-col :span="24">
            <ou-form-item label="防护策略：" prop="filenames">
              <div class="rule-list">
                <div class="listBox">
                  <div class="l">
                    <div
                      :class="{ active: leftListIndex == i }"
                      @click="clickFirstLevel(li, i)"
                      v-for="(li, i) in rule.level1Data"
                      :key="i">
                      {{ li.ClassName }}
                    </div>
                  </div>
                  <div class="r">
                    <ou-checkbox-group v-model="checkList" @change="changeType">
                      <div class="type-container">
                        <div class="type-item-container" v-for="(item, index) in rule.level2Data" :key="item.TypeId">
                          <ou-checkbox :label="item.TypeId">{{ item.TypeName }}</ou-checkbox>
                          <label class="modified" v-if="isShowEdit(item)" @click="showEditRule(item, index)">
                            （修改{{ item.editedList.count }}项）
                          </label>
                        </div>
                      </div>
                    </ou-checkbox-group>
                  </div>
                </div>
              </div>
            </ou-form-item>
          </el-col>
          <el-col :span="24">
            <ou-form-item label="语法分析引擎：">
              <ou-switch
                v-model="baseForm.grammar"
                active-text="开启"
                inactive-text="关闭"
                class="switch-offside on-off"></ou-switch>
              <ou-tooltip-icon
                style="margin-left: 8px; position: relative; font-size: 16px"
                content="开启语法分析引擎，可优化WAF的防护功能，降低误报率。"></ou-tooltip-icon>
            </ou-form-item>
          </el-col>
          <el-col :span="24">
            <ou-form-item label="机器学习引擎：">
              <ou-switch
                v-model="baseForm.semantics_enable"
                active-text="开启"
                inactive-text="关闭"
                class="switch-offside on-off"></ou-switch>
              <ou-tooltip-icon
                style="margin-left: 8px; position: relative; font-size: 16px"
                content="开启机器学习，可提升检测准确率，降低误报率；开启后将占用约200M引擎内存。"></ou-tooltip-icon>
            </ou-form-item>
          </el-col>
          <el-col :span="24">
            <ou-form-item style="margin-bottom: 0px">
              <ou-button type="primary" text @click="baseForm.isHidden = !baseForm.isHidden">
                {{ baseForm.isHidden ? "展开更多配置" : "隐藏以下配置" }}
                <ou-icon
                  :style="{
                    marginLeft: '4px',
                    fontSize: '20px',
                    position: 'relative',
                    top: baseForm.isHidden ? '-1px' : '-2px',
                  }">
                  <CaretBottom v-if="baseForm.isHidden" />
                  <CaretTop v-else />
                </ou-icon>
              </ou-button>
            </ou-form-item>
          </el-col>
        </el-row>
      </div>
      <div v-if="!baseForm.isHidden" style="margin-top: 20px">
        <div class="content-85">
          <el-row :gutter="80">
            <el-col :span="12">
              <ou-form-item label="缓冲区大小限制：" prop="astrictSize.count">
                <ou-input
                  placeholder="不填默认为100MB"
                  oninput="value=value.replace(/^\.+|[^\d.]/g,'')"
                  v-model.number="baseForm.astrictSize.count"
                  class="input-with-select">
                  <template #append>
                    <ou-select
                      :clearable="false"
                      style="width: 70px"
                      v-model="baseForm.astrictSize.unit"
                      class="select-unit-width">
                      <ou-option v-for="(v, k) in flowList" :key="k" :label="v" :value="k"></ou-option>
                    </ou-select>
                  </template>
                </ou-input>
                <ou-tooltip-icon
                  style="position: absolute; right: -24px; font-size: 16px"
                  content="包含发送缓冲区和接收缓冲区的大小，合理设置缓冲区大小，可以提高网络数据的传输效率和可靠性，避免不必要的资源浪费和损失。默认值为100MB，最小值为256KB，最大值为100MB"></ou-tooltip-icon>
              </ou-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <ou-form-item label="访问频率限制：" :style="{ marginBottom: baseForm.astrict ? '10px' : '0px' }">
                <ou-switch
                  v-model="baseForm.astrict"
                  active-text="开启"
                  inactive-text="关闭"
                  class="switch-offside on-off"></ou-switch>
                <ou-tooltip-icon
                  style="margin-left: 8px; position: relative; font-size: 16px"
                  content="同一IP在一定时间内累计请求同一URL超过一定次数，则会对此IP进行封锁限制"></ou-tooltip-icon>
              </ou-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="80">
            <el-col :span="12">
              <ou-form-item
                label="访问频率："
                v-if="baseForm.astrict"
                prop="visitFrequency.dateCount"
                style="margin-bottom: 0px">
                <div class="pn-select-item">
                  <ou-form-item prop="visitFrequency.dateCount">
                    <ou-input
                      placeholder="请输入时长"
                      v-model.number="baseForm.visitFrequency.dateCount"
                      :clearable="false"
                      maxlength="5">
                      <template #append>
                        <ou-select
                          :clearable="false"
                          class="select-unit-width"
                          v-model="baseForm.visitFrequency.dateUnit"
                          placeholder="请选择">
                          <ou-option v-for="(v, k) in selectDateList" :key="k" :label="v" :value="k"></ou-option>
                        </ou-select>
                      </template>
                    </ou-input>
                  </ou-form-item>
                  <ou-form-item prop="visitFrequency.count" style="margin-left: 4px">
                    <ou-input v-model.number="baseForm.visitFrequency.count" placeholder="请输入次数" maxlength="9">
                      <template #append>次</template>
                    </ou-input>
                  </ou-form-item>
                </div>
              </ou-form-item>
            </el-col>
          </el-row>
        </div>
        <el-row :gutter="80">
          <el-col :span="24">
            <ou-form-item v-if="baseForm.astrict">
              <div class="pn-date-detail">
                <span>单IP</span>
                <span class="pn-date-detail-count">
                  {{ baseForm.visitFrequency.dateCount }}{{ selectDateList[baseForm.visitFrequency.dateUnit] }}
                </span>
                <span>内累计请求超过</span>
                <span class="pn-date-detail-count">{{ baseForm.visitFrequency.count }}次</span>
                <span>，触发访问频率限制，</span>
                <span>即在触发规则后到</span>
                <span class="pn-date-detail-count">
                  {{ baseForm.visitFrequency.dateCount }}{{ selectDateList[baseForm.visitFrequency.dateUnit] }}
                </span>
                <span>结束期间均拒绝该IP的请求</span>
              </div>
            </ou-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="80">
          <el-col :span="24">
            <ou-form-item label="IP白名单：" style="margin-bottom: 0px; margin-top: 3px">
              <whiteList
                v-model:list="baseForm.whiteData.list"
                :visitId="baseForm.whiteData.visitId"
                ref="whiteRef"></whiteList>
            </ou-form-item>
          </el-col>
        </el-row>
      </div>
    </ou-form>
    <ou-opera-dialog :draggable="true" hideFooter width="900px" title="已修改规则" v-model="isShowRuleConfig">
      <template #content>
        <ou-table
          class="editor"
          :data="ruleListItem"
          :height="ruleListItem.length >= 5 ? 240 : ruleListItem.length <= 4 ? 200 : 'auto'"
          style="width: 100%; --table-empty-size: 120px">
          <ou-table-column type="index" label="序号" width="44" aling=""></ou-table-column>
          <ou-table-column prop="rule_id" label="ID">
            <template #default="scope">
              <ou-text-ellipsis :content="scope.row.rule_id" />
            </template>
          </ou-table-column>
          <ou-table-column prop="name" label="名称">
            <template #default="scope">
              <ou-text-ellipsis :content="scope.row.name" />
            </template>
          </ou-table-column>
          <ou-table-column prop="Severity" label="危险等级" width="80px">
            <template #default="scope">
              <span
                :style="{
                  color: mapLevel[scope.row.level]['color'],
                }">
                {{ mapLevel[scope.row.level]["label"] }}
              </span>
            </template>
          </ou-table-column>
          <ou-table-column prop="status" label="状态" width="100px">
            <template #default="scope">
              <ou-switch
                v-model="scope.row.status"
                active-text="启用"
                size="small"
                inactive-text="禁用"
                class="switch-offside"></ou-switch>
            </template>
          </ou-table-column>
          <ou-table-column prop="action" label="仅检测" width="100px">
            <template #default="scope">
              <ou-switch
                v-model="scope.row.action"
                active-text="是"
                :disabled="baseForm.detection_only"
                size="small"
                inactive-text="否"
                class="switch-offside"></ou-switch>
            </template>
          </ou-table-column>
        </ou-table>
      </template>
      <template #footer>
        <div class="dialog-btn">
          <el-button @click="isShowRuleConfig = false">取消</el-button>
          <el-button type="primary" @click="confirmRule">确定</el-button>
        </div>
      </template>
    </ou-opera-dialog>
  </div>
</template>
<script setup jsx>
  import { uniqueFunc, deepClone } from "@/utils/tool.js";
  import whiteList from "@/views/plugins/waf/components/whiteList";
  import { validateForm } from "@/utils/tool.js";
  import {
    useState,
    configWhite,
    wafTypeData,
    isShowEdit,
    configTypeMap,
    virtualServiceConfig,
    configDisableRules,
    configPassRules,
    configNoteRule,
    configRulesData,
  } from "../hook";
  import { defaultConfigHooks } from "../hook/config-default.js";
  const {
    isLoading,
    isShowRuleConfig,
    ruleListItem,
    ruleData,
    leftListIndex,
    checkList,
    editedList,
    level1Id,
    rightListIndex,
    selectTypeItem,
    flowList,
    selectDateList,
    formRef,
    rules,
    whiteRef,
    baseForm,
    passRuleId,
    rule,
    selects,
    typeListMap,
    mapLevel,
  } = useState();
  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    matedata: {
      type: Object,
      default: {},
    },
    global: {
      type: String,
      default: "",
    },
    caseLevel: {
      type: String,
      default: "",
    },
  });

  //防护类型
  const changeType = val => {
    checkList.value = val;
    rule.value.level1Data[rule.value.level1Data.findIndex(v => v.ClassId === level1Id.value)].typeList = val;
  };

  //左边防护类目点击
  const clickFirstLevel = (item, i) => {
    //筛选出左侧防护类目
    checkList.value = rule.value.level1Data.find(v => v.ClassId === item.ClassId).typeList;
    const flData = ruleData.value.filter(v => v.ClassId === item.ClassId);
    //防护类型去重
    const level2Data = uniqueFunc(flData, "TypeId");
    //过滤掉不显示的防护类型
    const level2DataShow = level2Data.filter(e => e.ShowCheckbox === "1");
    rule.value.level2Data = level2DataShow;
    level1Id.value = item.ClassId;
    leftListIndex.value = i || 0;
    configEditRule();
  };

  //配置显示已修改
  const configEditRule = () => {
    if (!editedList.value.length) return;
    rule.value.level2Data.forEach(item => {
      const list = editedList.value.filter(v => v.type_id === item.TypeId);
      if (list.length) {
        item.editedList = {
          count: list.length,
          list,
        };
      }
    });
  };

  let { getDefaultConfig } = defaultConfigHooks();
  //页面加载初始化
  const initData = async () => {
    const typeData = await wafTypeData();
    typeListMap.value = configTypeMap(typeData);
    ruleData.value = deepClone(typeData);
    if (props.global) {
      configEditInit();
    } else {
      configCreateInit();
      // 如果使用【方案等级】创建配置，使用【默认】方案等级配置
      if (props.caseLevel) {
        let defaultConfig = getDefaultConfig(props.caseLevel);
        Object.assign(baseForm, defaultConfig);
      }
    }
    //初始加载高亮tab栏
    clickFirstLevel(rule.value.level1Data[leftListIndex.value]);
  };
  initData();
  //配置创建逻辑
  const configCreateInit = () => {
    const level1Data = uniqueFunc(ruleData.value, "ClassId");
    level1Data.sort((a, b) => a.ClassId - b.ClassId);
    rule.value.level1Data = level1Data.map(v => {
      const flData = ruleData.value.filter(k => k.ClassId === v.ClassId);
      //根据防护分类筛选出对应的防护类型，过滤掉默认不启用的类型
      const level2Data = uniqueFunc(flData, "TypeId")
        .filter(e => e.EnableDetection === "1")
        .map(k => k.TypeId);
      return {
        ...v,
        typeList: level2Data,
      };
    });
  };
  //配置初始编辑状态回显
  const configEditInit = () => {
    const global = JSON.parse(props.global);
    const { form, rules, edited } = global.meta_data;
    const { pass_rules, disable_rules } = global.plugin_config;
    passRuleId.value = pass_rules;
    editedList.value = edited || [];
    rule.value.level1Data = rules;
    Object.keys(form).forEach(e => {
      if (e === "whiteData") {
        baseForm.whiteData = configWhite(form.whiteData);
      } else {
        baseForm[e] = form[e];
      }
    });
  };

  const configEditedList = () => {
    const pgEditedList = editedList.value || [];
    const list = configRulesData(ruleListItem.value, baseForm);
    list.forEach(e => {
      const isExistIndex = pgEditedList.findIndex(v => v.rule_id === e.rule_id);
      if (isExistIndex >= 0 && !e.is_default) {
        editedList.value.splice(isExistIndex, 1, e);
      }
      if (isExistIndex >= 0 && e.is_default) {
        editedList.value.splice(isExistIndex, 1);
      }
      if (isExistIndex < 0 && !e.is_default) {
        editedList.value.push(e);
      }
    });
  };

  //编辑规则确定操作
  const confirmRule = () => {
    configEditedList();
    isShowRuleConfig.value = false;
  };
  //显示已修改规则
  const showEditRule = (item, index) => {
    selectTypeItem.value = deepClone(item);
    const listData = deepClone(item.editedList.list);
    const ruleList = listData.map(e => ({
      ...e,
      status: Boolean(e.status),
      action: e.action === 2,
    }));
    ruleListItem.value = ruleList;
    isShowRuleConfig.value = true;
  };

  //全局配置数据结构
  const configData = () => {
    const MODEL_MAP = {
      0: "PERFORMANCE",
      1: "PRECISE",
    };
    const { defendLevel, performanceMode, defendMode, isHidden, semantics_enable, astrictSize } = baseForm;
    // 白名单数据
    const whiteData = isHidden ? [] : whiteRef.value?.configIpList() || [];
    const ip_whitelist = whiteData.filter(item => item.enable);
    // 缓冲区大小（默认 100MB）
    const limitCount = astrictSize.count || 1024 * 100;
    const limitUnit = astrictSize.unit;
    const bufferSize = limitUnit === "mb" ? limitCount * 1024 * 1024 : limitCount * 1024;
    // 提取规则类型列表并计算 rule_types 值
    const typeData = rule.value.level1Data.map(v => v.typeList).flat();
    const rule_types = typeData.reduce((acc, cur) => {
      const ruleEnum = typeListMap.value.find(v => v.typeId === cur);
      acc += ruleEnum.RuleType;
      return acc;
    }, 0);
    // 是否处于仅检测模式
    const isDetectionOnly = !Boolean(Number(defendMode));
    const subData = {
      paranoia_level: defendLevel,
      detection_only: isDetectionOnly,
      mode: MODEL_MAP[performanceMode] || "PERFORMANCE",
      rule_types,
      disable_rules: configDisableRules(editedList.value),
      pass_rules: isDetectionOnly ? [] : configPassRules(editedList.value, baseForm, passRuleId.value),
      plugin_buffer_limit: bufferSize,
      ip_whitelist,
      config_syntax: {
        enable: baseForm.grammar,
        semantics_enable,
      },
    };
    return subData;
  };
  const submit = async () => {
    const isValid = await validateForm(formRef.value);
    if (!isValid) return;
    return {
      service: {
        plugin_config: virtualServiceConfig(baseForm),
      },
      gateway: {
        plugin_config: configData(),
        meta_data: {
          form: {
            ...baseForm,
            detection_only: !Boolean(Number(baseForm.defendMode)),
          },
          rules: configNoteRule(rule.value.level1Data),
          edited: editedList.value,
        },
      },
    };
  };
  defineExpose({
    submit,
  });
</script>
<style lang="scss" scoped>
  .rule-list {
    display: flex;
    width: 100%;
  }
  .listBox {
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;
    .l {
      width: 240px;
      font-size: 14px;
      text-align: center;
      border-right: 1px solid #d9d9d9;
      div {
        width: 100%;
        padding: 0 20px;
        padding: 4px 0px;
        color: var(--ouryun-text-color-gray-1);
        cursor: pointer;
        &:hover {
          background: #f3f3f3;
          color: var(--ouryun-text-color-gray-1);
        }
        &.active {
          color: var(--ouryun-color-primary);
          background: #f2f5ff;
          cursor: pointer;
          border-right: 3px solid var(--ouryun-color-primary);
        }
      }
    }

    .r {
      display: flex;
      flex-wrap: wrap;
      padding: 15px 17px;
      width: 100%;
      overflow: hidden;
    }
  }

  :deep(.ouryun-input__wrapper) {
    padding-top: 0;
    padding-bottom: 0;
  }

  .pn-select-item {
    width: 100%;
    display: flex;
    :deep(.ouryun-form-item-custom) {
      flex: 1;
    }
  }
  .type-container {
    flex-wrap: wrap;
    display: flex;
  }
  .type-item-container {
    min-width: 230px;
    overflow: auto;
    display: flex;
    width: 20%;
    .modified {
      cursor: pointer;
      font-size: 14px;
      margin-left: -30px;
      display: flex;
      align-items: center;
      color: var(--ouryun-color-brand-base) !important;
    }
  }

  .pn-date-detail {
    .pn-date-detail-count {
      color: var(--ouryun-color-danger);
    }
    width: 100%;
    padding-left: 20px;
    display: flex;
    background: #f3f3f3;
    color: var(--ouryun-text-color-gray-1);
    border-radius: 4px;
  }
  .waf-tooltip {
    margin-left: 8px;
  }
  .ouryun-form-item-label-group {
    display: flex;
    align-items: center;
    column-gap: 4px;
    position: relative;
  }
  .tips-position {
    position: absolute;
    top: 50%; /* 垂直居中 */
    right: -20px; /* 向右偏移一定距离，超出父容器 */
    transform: translateY(-50%); /* 修正垂直居中 */
  }
  ::v-deep .ouryun-input__inner {
    height: 32px;
  }
  .content-85 {
    width: calc(85% + 15px);
  }
  .select-unit-width {
    width: 64px !important;
  }
</style>
