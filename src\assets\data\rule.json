[{"ClassId": "3", "ClassName": "机器人检测", "TypeId": "1", "TypeName": "机器人扫描", "Id": "Srhino-10191300", "Name": "恶意扫描", "describe": "sqlmap这些工具扫描进行拦截", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291000", "Name": "无效的http请求", "describe": "检测http请求是否符合标准", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291002", "Name": "Attemptedmultipart/form-databypass", "describe": "防止文件上传multipart/form-data绕过", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291006", "Name": "Content-LengthHTTP标头不是数字", "describe": "消息主体大小是不是数字", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291007", "Name": "GET或HEAD请求含有Body", "describe": "GET或者HEAD检测请求中是否含有Body", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291017", "Name": "带有传输编码的GET或者HEAD请求", "describe": "GET与HEAD请求是否使用了chunked,compress等编码方法", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291008", "Name": "缺少Content-Length或者Transfer-Encoding编码", "describe": "需要提供内容长度或传输编码", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291018", "Name": "存在内容长度和传输编码标头", "describe": "发送者不得发送内容长度", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291009", "Name": "无效的最后字节", "describe": "如果最后一个字节位置值存在,则字节范围规范无效", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292001", "Name": "连接标头异常", "describe": "检测是否存在多个连接标头", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292002", "Name": "URL编码滥用工艺1", "describe": "检测请求是否使用多次URL编码", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292004", "Name": "URL编码滥用攻击2", "describe": "检测当Content-Type为application/x-www-form-urlencoded时是否使用的多次URL编码", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292005", "Name": "UTF-8编码滥用攻击", "describe": "检测请求中的UTF-8字符串是否合法", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292006", "Name": "Unicode全/半宽度滥用", "describe": "检测请求中是否存在Unicode全/半宽度滥用风险", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292007", "Name": "请求中含有无效字符", "describe": "检测请求中的字符是否在允许字符集范围", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292008", "Name": "请求缺失标头", "describe": "不接受没有公共标头的请求", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292009", "Name": "空主机头", "describe": "检测主机头是否为空", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293001", "Name": "请求的Accept头为空1", "describe": "检测请求是否缺少Accept头且是否为OPTIONS请求", "Severity": "NOTICE", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293011", "Name": "请求的Accept头为空2", "describe": "检测请求是否缺少Accept头且是否为OPTIONS请求且User-Agent不为空", "Severity": "NOTICE", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293003", "Name": "空的UserAgent头", "describe": "检测User-Agent标头是否为空", "Severity": "NOTICE", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293004", "Name": "缺少Content-Type标头", "describe": "包含内容但是缺少Content-Type标头的请求", "Severity": "NOTICE", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293005", "Name": "Host是ip地址", "describe": "检查主机头是否不是IP地址", "Severity": "WARNING", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293008", "Name": "请求参数过多", "describe": "请求中的参数数量过多", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293006", "Name": "参数名称过长", "describe": "参数名超过长度限制", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293007", "Name": "参数值过长", "describe": "参数值超过了长度限制", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293009", "Name": "参数大小超过限制", "describe": "参数名称与值的总大小超过限制", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294000", "Name": "上传文件过大1", "describe": "限制单文件上传大小", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294001", "Name": "上传文件过大2", "describe": "限制多文件上传大小", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294007", "Name": "非法的Content-Type头部", "describe": "限制接受的内容类型", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294002", "Name": "内容类型不被允许", "describe": "检测内容类型是否在允许范围内", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294008", "Name": "内容类型字符集不被策略所允许", "describe": "限制内容类型标头内的字符集参数", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10295003", "Name": "内容类型头中多个字符集", "describe": "限制内容类型标头内的字符集参数最多出现一次", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294003", "Name": "不允许的HTTP协议版本", "describe": "检测HTTP协议版本在不在范围内", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294004", "Name": "URL文件扩展名限制", "describe": "限制一些文件后缀访问", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10295000", "Name": "限制备份文件访问", "describe": "限制对一些备份后缀进行访问", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294005", "Name": "受限制的请求头", "describe": "检测请求头是否在黑名单中", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10295002", "Name": "Accept-Encoding", "describe": "检测Accept-Encoding头是否正常", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10296000", "Name": "非法的charset参数", "describe": "检测字符集是否在允许范围", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10295004", "Name": "Unicode字符绕过检查", "describe": "针对非JSON请求的Unicode字符绕过检测", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10296001", "Name": "请求URI中存在原始(未编码)片段", "describe": "不允许使用任何原始URL片段", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10296002", "Name": "存在多个Content-Type请求头", "describe": "请求中Content-Type存在多个", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292000", "Name": "文件头限制", "describe": "对于PDF以外的所有文件名，限制5个范围头字段", "Severity": "WARNING", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292010", "Name": "pdf头范围限制", "describe": "对于PDF文件限制62个范围头字段", "Severity": "WARNING", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292003", "Name": "URL编码", "describe": "检测请求是否经过多次URL编码", "Severity": "WARNING", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292017", "Name": "请求中存在无效字符", "describe": "检测请求中的字符是否在允许字符集范围", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293002", "Name": "缺少UserAgent头部", "describe": "检测是否存在UserAgent头", "Severity": "NOTICE", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10291012", "Name": "multipart/form-data绕过", "describe": "检测multipart/form-data头是否正常", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293014", "Name": "需要Content-Type头部", "describe": "请求体不为空的情况下是否存在Content-Type头", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292027", "Name": "请求中存在无效字符", "describe": "检测请求中的字符是否在ASCII127以下的可打印字符之外", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10293000", "Name": "缺少Accept头部", "describe": "检测请求头中是否存在Accept头", "Severity": "NOTICE", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294009", "Name": "x-up-devcap-post-charset绕过WAF", "describe": "检测请求中x-up-devcap-post-charset是否与User-Agent的前缀UP结合使用", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10295001", "Name": "无效的Cache-Control请求头", "describe": "检测Cache-Control请求头是否正常", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10295012", "Name": "非法的Accept-Encoding头部", "describe": "检测Accpet-Encoding头部内容是否存在异常", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292020", "Name": "PDF请求的字段过多", "describe": "对于PDF文件，限制5个范围头字段", "Severity": "WARNING", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292037", "Name": "存在无效字符", "describe": "检测请求参数中的字符是否在允许字符集范围", "Severity": "CRITICAL", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292047", "Name": "存在无效字符", "describe": "检测请求中的字符是否在字符集范围", "Severity": "CRITICAL", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10292057", "Name": "无效字符(非常严格的字符集之外)", "describe": "检测请求中是否存在无效字符", "Severity": "CRITICAL", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "2", "TypeName": "协议规范", "Id": "Srhino-10294006", "Name": "存在异常的字符转义", "describe": "检测请求中的\\是否被转义", "Severity": "CRITICAL", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291101", "Name": "HTTP请求走私攻击", "describe": "防止http请求走私攻击", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291102", "Name": "HTTP响应拆分攻击", "describe": "检测头部是否存在\\r\\n", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291103", "Name": "HTTP响应拆分攻击", "describe": "检测头部是否存在异常参数", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291104", "Name": "HTTP头注入攻击1", "describe": "检测输入参数是否存在(\\n\\r)", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291105", "Name": "HTTP头注入攻击2", "describe": "检测输入参数是否存在(CR/LF)注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291106", "Name": "HTTP头注入攻击3", "describe": "检测GET参数与头部名称是否存在(CR/LF)", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291109", "Name": "HTTP拆分攻击", "describe": "检测请求文件名中是否存在CR/LF", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10292100", "Name": "LDAP注入", "describe": "检测请求中是否含有LDAP注入风险", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10294112", "Name": "Content-Type危险头部1", "describe": "内容类型超出了MIME类型声明", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10292104", "Name": "mod_proxy攻击", "describe": "CVE-2021-40438", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10291115", "Name": "HTTP头注入攻击", "describe": "通过有效载荷进行进行注入CR/LF", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10294122", "Name": "Content-Type危险头部2", "describe": "内容类型超出了MIME类型声明", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10292103", "Name": "HTTPRange头部", "describe": "有可能滥用HTTP请求范围标头来泄漏错误页面", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10292101", "Name": "HTTP参数污染", "describe": "参数数组后的虚假字符后的参数污染", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "4", "TypeName": "协议攻击拦截", "Id": "Srhino-10292102", "Name": "HTTP参数污染", "describe": "可能通过数组表示法进行", "Severity": "CRITICAL", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "8", "TypeName": "表单攻击拦截", "Id": "Srhino-10291200", "Name": "全局charset内容类型限制", "describe": "全局charset时仅允许特定字符集", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "8", "TypeName": "表单攻击拦截", "Id": "Srhino-10291201", "Name": "非法的MIME头", "describe": "头部content-type：charset参数", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "8", "TypeName": "表单攻击拦截", "Id": "Srhino-10291202", "Name": "Content-Transfer-Encoding", "describe": "已被弃用,不应使用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "10", "TypeName": "本地文件包含拦截", "Id": "Srhino-10391000", "Name": "路径遍历攻击(/../或/.../)", "describe": "是否存在../", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "10", "TypeName": "本地文件包含拦截", "Id": "Srhino-10391001", "Name": "路径遍历攻击(/../或/.../)", "describe": "是否存在..\\与../", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "10", "TypeName": "本地文件包含拦截", "Id": "Srhino-10391002", "Name": "操作系统文件访问尝试", "describe": "常见文件路径例如:/etc/passwd", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "10", "TypeName": "本地文件包含拦截", "Id": "Srhino-10391003", "Name": "受限文件访问尝试", "describe": "是否访问.git一类敏感文件", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "10", "TypeName": "本地文件包含拦截", "Id": "Srhino-10391012", "Name": "请求头中的操作系统文件访问尝试", "describe": "请求头中是否包含对系统文件的探测", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "20", "TypeName": "远程文件包含拦截", "Id": "Srhino-10391100", "Name": "远程文件包含(RFI)攻击1", "describe": "使用IP地址的URL参数", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "20", "TypeName": "远程文件包含拦截", "Id": "Srhino-10391101", "Name": "远程文件包含(RFI)攻击2", "describe": "使用URL载荷的常见RFI易受攻击的参数名称", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "20", "TypeName": "远程文件包含拦截", "Id": "Srhino-10391102", "Name": "远程文件包含(RFI)攻击3", "describe": "使用带有尾随问号字符(?)的URL载荷", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "20", "TypeName": "远程文件包含拦截", "Id": "Srhino-10391103", "Name": "远程文件包含(RFI)攻击4", "describe": "离域引用/链接", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "20", "TypeName": "远程文件包含拦截", "Id": "Srhino-10391113", "Name": "远程文件包含(RFI)攻击5", "describe": "离域引用/链接", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392203", "Name": "远程命令执行", "describe": "Unix命令注入(2-3个字符)", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392253", "Name": "远程命令执行", "describe": "Unix命令注入(无规避的命令)", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391251", "Name": "远程命令执行", "describe": "Windows命令注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391202", "Name": "远程命令执行", "describe": "WindowsPowerShell命令注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391252", "Name": "远程命令执行", "describe": "WindowsPowerShell别名命令注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391203", "Name": "远程命令执行", "describe": "UnixShell表达式注入", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391204", "Name": "远程命令执行", "describe": "发现WindowsFOR/IF批处理命令", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392205", "Name": "远程命令执行", "describe": "直接的Unix命令执行", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392206", "Name": "远程命令执行", "describe": "直接的Unix命令执行", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393203", "Name": "远程命令执行", "describe": "Unixshell历史调用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391206", "Name": "远程命令执行：发现UnixShell代码", "describe": "是否存在UnixShell命令例如/bin/ls", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391207", "Name": "远程命令执行：Shellshock(CVE-2014-6271)", "describe": "请求头中是否存在破壳漏洞威胁", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391217", "Name": "远程命令执行：Shellshock(CVE-2014-6271)", "describe": "参数中是否存在破壳漏洞威胁", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391257", "Name": "远程命令执行", "describe": "Unixshell别名调用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391208", "Name": "受限文件上传尝试", "describe": "限制一些配置文件被上传例如.htaccess", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393207", "Name": "远程命令执行", "describe": "Windows命令注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393208", "Name": "远程命令执行", "describe": "Windows命令执行注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392213", "Name": "远程命令执行", "describe": "UnixShell命令注入", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391213", "Name": "远程命令执行", "describe": "UnixShell表达式注入", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392200", "Name": "远程命令执行", "describe": "RCE绕过Bypass", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392260", "Name": "远程命令执行", "describe": "RCE绕过检测", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392250", "Name": "远程命令执行", "describe": "命令执行绕过", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392202", "Name": "远程命令执行", "describe": "Unix命令注入(使用管道)", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392204", "Name": "远程命令执行", "describe": "Unix命令注入规避尝试", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392201", "Name": "远程命令执行", "describe": "SQLite系统命令执行", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393200", "Name": "远程命令执行", "describe": "SMTP命令执行", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393201", "Name": "远程命令执行", "describe": "IMAP命令执行", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393202", "Name": "远程命令执行", "describe": "POP3命令执行", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392263", "Name": "远程命令执行", "describe": "Unix命令注入(无bypass防护操作)", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391216", "Name": "远程命令执行", "describe": "在请求头部中是否存在UnixShell命令", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392223", "Name": "远程命令执行", "describe": "Unix命令注入", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392273", "Name": "远程命令执行", "describe": "在请求头部中发现UnixShell的命令执行操作", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392283", "Name": "远程命令执行", "describe": "请求头发现UnixShell的命令执行操作", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10392293", "Name": "远程命令执行", "describe": "在user-agent或referer标头中发现Unix命令注入", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10391209", "Name": "远程命令执行", "describe": "通配符绕过防护尝试", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393210", "Name": "远程命令执行", "describe": "SMTP命令执行", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393211", "Name": "远程命令执行", "describe": "IMAP命令执行", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393212", "Name": "远程命令执行", "describe": "POP3命令执行", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "40", "TypeName": "远程命令执行拦截", "Id": "Srhino-10393213", "Name": "远程命令执", "describe": "Unixshell历史调用", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391300", "Name": "PHP注入攻击", "describe": "检测请求中是否存在php文件开始标签", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391301", "Name": "PHP注入攻击", "describe": "请求文件是否是上传php脚本文件", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391302", "Name": "PHP注入攻击", "describe": "检测请求中是否存在php配置指令", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391303", "Name": "PHP注入攻击", "describe": "检测请求中是否存在PHP超级变量例如$_POST等", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391304", "Name": "PHP注入攻击", "describe": "检测请求中是否存在php流操作例如php://input等", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10392300", "Name": "PHP注入攻击", "describe": "检测请求中是否存在php伪协议存在", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391305", "Name": "PHP注入攻击", "describe": "检测请求中是否存在高危函数名称", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391306", "Name": "PHP注入攻击", "describe": "检测请求中是否存在高危函数调用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391307", "Name": "PHP注入攻击", "describe": "检测请求中是否存在php反序列化对象", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391308", "Name": "PHP变量函数调用1", "describe": "检测请求中是否含有变量函数调用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10392301", "Name": "PHP变量函数调用2", "describe": "检测请求中是否含有变量函数调用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391315", "Name": "PHP敏感函数名称", "describe": "检测请求中是否含有php敏感函数名称", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391313", "Name": "PHP基础变量", "describe": "检测请求中是否存在php基础变量", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391316", "Name": "PHP函数调用", "describe": "检测请求中是否存在PHP函数调用", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391311", "Name": "PHP上传", "describe": "检测请求中是否存在php文件上传", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10391309", "Name": "PHP标签", "describe": "检测请求中是否含有php短标签", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80", "TypeName": "php常见漏洞拦截", "Id": "Srhino-10392311", "Name": "PHP变量函数调用3", "describe": "检测请求中是否含有变量函数调用", "Severity": "ERROR", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391400", "Name": "Node.js注入攻击1/2", "describe": "防止Node反序列化漏洞", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391410", "Name": "Node.js注入攻击2/2", "describe": "防止Node反序列化漏洞", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391401", "Name": "可能的服务器端请求伪造(SSRF)", "describe": "请求中参数存在危险的URL", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391403", "Name": "JavaScript原型污染1", "describe": "检测请求中是否存在原型污染攻击", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391405", "Name": "Ruby注入攻击", "describe": "检测请求中是否存在", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391406", "Name": "Node.jsDoS攻击", "describe": "防止原型污染造成的Dos攻击", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391407", "Name": "PHP数据方案攻击", "describe": "检测请求中是否存在data://协议攻击", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391402", "Name": "可能的服务器端请求伪造(SSRF)攻击", "describe": "检测请求中是否存在使用IP地址的URL参数", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391413", "Name": "JavaScript原型污染2", "describe": "检测请求中是否存在原型污染攻击", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "100", "TypeName": "通用性漏洞拦截", "Id": "Srhino-10391404", "Name": "Perl攻击", "describe": "检测请求中是否有Perl注入攻击特征", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491100", "Name": "XSS过滤器", "describe": "通过语义分析检测", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491101", "Name": "XSS过滤器-类别1", "describe": "检测请求中是否存在JavaScript脚本标签", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491103", "Name": "XSS过滤器-类别3", "describe": "检测请求中是否含有黑名单中的属性", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491104", "Name": "XSS过滤器-类别4", "describe": "检测请求中是否存在恶意的URI向量", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491106", "Name": "NoScriptXSSInjectionCheckerHtml注入", "describe": "不允许的HTML注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491107", "Name": "NoScriptXSSInjectionChecker属性注入", "describe": "不允许的属性注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491108", "Name": "Node-Validator", "describe": "黑名单中的关键词", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491109", "Name": "IEXSS过滤器1", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492100", "Name": "IEXSS过滤器2", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492101", "Name": "IEXSS过滤器3", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492102", "Name": "IEXSS过滤器4", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492103", "Name": "IEXSS过滤器5", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492104", "Name": "IEXSS过滤器6", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492105", "Name": "IEXSS过滤器7", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492106", "Name": "IEXSS过滤器8", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492107", "Name": "IEXSS过滤器9", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492108", "Name": "IEXSS过滤器10", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10492109", "Name": "IEXSS过滤器11", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493100", "Name": "IEXSS过滤器12", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493101", "Name": "格式不正确的编码XSS过滤器", "describe": "检测是否存在利用非常规编码进行绕过", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493105", "Name": "UTF-7编码IEXSS", "describe": "检测是否使用utf=7编码绕过", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493106", "Name": "JSFuck/Hieroglyphy混淆", "describe": "请求参数内容是否被混淆", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493107", "Name": "JavaScript全局变量", "describe": "请求包中是否含有可能存在危险的全局变量调用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493109", "Name": "JavaScript方法", "describe": "请求包中是否含有危险方法", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10494100", "Name": "XSSJavaScript函数缺少括号", "describe": "非正常格式的JavaScript函数调用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491110", "Name": "XSS攻击", "describe": "通过语义分析检测", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491102", "Name": "XSS事件过滤器", "describe": "危险事件", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491105", "Name": "XSS属性过滤器", "describe": "不允许的HTML属性", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10491118", "Name": "Node-Validator", "describe": "请求中存在黑名单中的关键词", "Severity": "NOTICE", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493102", "Name": "可能XSS攻击", "describe": "请求中存在危险标签", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493103", "Name": "IEXSS过滤器13", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493104", "Name": "IEXSS过滤器14", "describe": "检测基于IE浏览器特性的XSS", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "200", "TypeName": "Xss漏洞拦截", "Id": "Srhino-10493108", "Name": "AngularJS客户端模板注入攻击", "describe": "请求包是否存在模板注入的行为", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491200", "Name": "SQL注入攻击", "describe": "通过语义分析检测", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491204", "Name": "防止查询敏感库信息", "describe": "检测请求中是否有常见数据库名称", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491215", "Name": "防止函数被恶意使用", "describe": "检测请求中是否含有SQL函数名", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491206", "Name": "防止基于时间的注入", "describe": "基于sleep()或benchmark()的盲注入测试", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491207", "Name": "防止条件查询注入", "describe": "包括条件查询在内的SQL基准和延迟注入尝试", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491209", "Name": "防止Mssqlxp_cmdshell", "describe": "MSSQL代码执行和信息收集尝试", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492202", "Name": "SQL溢出攻击", "describe": "检测请求中是否存在查找整数溢出风险", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492203", "Name": "防止SQL条件查询尝试", "describe": "防止like等模糊查询", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492204", "Name": "字符集切换和MSSQLDoS尝试", "describe": "检测请求是否存在字符集操作或MSSQLDos攻击风险", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492205", "Name": "ORacle批量操作", "describe": "检测请求中是否含有", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492207", "Name": "防止联合查询", "describe": "请求是否存在联合查询操作", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492208", "Name": "Postgres延时注入", "describe": "防止基于pg_sleep的延时注入", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492209", "Name": "MongoDBSQL注入", "describe": "检测是否存在MongoDB注入尝试", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493202", "Name": "MySQL和PostgreSQL防护", "describe": "检测是否存在存储过程/函数注入尝试", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493205", "Name": "MySQLUDF", "describe": "检测是否存在注入和其他数据/结构操纵尝试", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493206", "Name": "基本SQL注入和SQLLFI尝试", "describe": "检测是否存在sql敏感操作", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495200", "Name": "防止MySQL内联注释", "describe": "检测请求中是否使用内联注释", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495204", "Name": "防止SQL堆叠注入", "describe": "请求是否存在堆叠注入风险", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495205", "Name": "防止json_extract函数利用", "describe": "检测请求中是否存在json_extract函数恶意利用风险", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495206", "Name": "Sql注入拦截", "describe": "检测MYSQL注入攻击中的科学计数法绕过", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491201", "Name": "SQL注入攻击", "describe": "常见注入测试", "Severity": "NOTICE", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491202", "Name": "SQL注入攻击", "describe": "SQL运算符", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491203", "Name": "基于and的复杂方式注入", "describe": "基于等式的布尔值攻击", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491213", "Name": "基于or,xor的复杂方式注入", "describe": "基于异或操作攻击", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491205", "Name": "SQL注入攻击", "describe": "SQL函数名", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491208", "Name": "基本的SQL身份验证绕过尝试1/3", "describe": "万能密码尝试", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492200", "Name": "MySQL注入混淆", "describe": "检测请求中是否存在注释/空格混淆和反引号终止", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492201", "Name": "SQL注入尝试1/2", "describe": "检测请求中是否存在与或非，增删改查等敏感操作", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492206", "Name": "基本的SQL身份验证绕过尝试2/3", "describe": "万能密码尝试", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493200", "Name": "MySQL注释", "describe": "检测请求中是否存在Mysql注释符", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493201", "Name": "SQL注入尝试2/2", "describe": "检测请求中是否存在select,orderby等查询或排序操作", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493203", "Name": "经典的SQL注入测试1/3", "describe": "是否在使用Sql语句探测", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493204", "Name": "SQL身份验证绕过尝试3/3", "describe": "语句中是否含有or等关键字构造语句尝试绕过登录", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493216", "Name": "基于关键字的基本SQL注入", "describe": "检测请求中是否含有Union等关键字", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493226", "Name": "SQL注入和SQLLFI尝试", "describe": "基础的Sql注入尝试", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493207", "Name": "经典的SQL注入测试2/3", "describe": "是否在使用Sql语句探测", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493208", "Name": "SQL注入攻击", "describe": "检测请求中是否含有like,top等查询与排序操作", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493209", "Name": "SQL注入攻击", "describe": "检测请求中是否含有or1=1等操作", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494200", "Name": "SQL注入攻击", "describe": "检测请求中是否含有and1=1等操作", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494201", "Name": "SQL注入攻击", "describe": "检测请求中是否含有AScii与Countue等转换与计数等操作", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494207", "Name": "SQL注入攻击", "describe": "检测请求中是否含有获取SqlServer基础信息操作", "Severity": "ERROR", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494208", "Name": "SQL注入攻击", "describe": "检测请求中是否含有Sql分组查询操作", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494203", "Name": "受限的SQL字符异常", "describe": "请求中特殊字符数是否超过限制12", "Severity": "WARNING", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494204", "Name": "SQL内联注释", "describe": "检测请求中是否含有/*!*/内联注释", "Severity": "NOTICE", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494205", "Name": "SQL十六进制编码", "describe": "检测请求中是否含有16进制编码", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495201", "Name": "SQL单引号或反引号拦截", "describe": "检测是否使用单引号或反引号的SQLi绕过拦截", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495202", "Name": "万能密码尝试1", "describe": "是否存在万能密码", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495212", "Name": "万能密码尝试2", "describe": "规则Srhino-10495212加强", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495222", "Name": "万能密码尝试3", "describe": "规则Srhino-10495222加强", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491210", "Name": "SQL注入攻击", "describe": "使用语义分析", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10491225", "Name": "SQL注入攻击SQL函数", "describe": "检测请求中是否含有SQL常用函数", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10493212", "Name": "Sql函数注入", "describe": "检测请求中是否存在危险的Sql函数", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10492215", "Name": "HAVING注入攻击", "describe": "是否使用HAVING函数", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494209", "Name": "经典的SQL注入测试3/3", "describe": "字符型注入探测", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494202", "Name": "受限的SQL字符异常(<PERSON><PERSON>)", "describe": "限制Cookie中特殊字符不超过8个", "Severity": "WARNING", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494213", "Name": "受限的SQL字符异常(参数)", "describe": "限制参数中的特殊字符不超过6个", "Severity": "WARNING", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494206", "Name": "元字符异常警报", "describe": "检测请求中是否存在重复的非单词字符", "Severity": "WARNING", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495211", "Name": "使用单引号的SQLi绕过尝试", "describe": "检测是否存在使用单引号包裹的字符串", "Severity": "NOTICE", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10495203", "Name": "SQLi查询终止符", "describe": "检测请求中是否存在字符\\`;\\`", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494212", "Name": "受限的SQL字符异常(<PERSON><PERSON>)", "describe": "特殊字符数超过限制(3)", "Severity": "WARNING", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "400", "TypeName": "Sql注入拦截", "Id": "Srhino-10494223", "Name": "受限的SQL字符异常(参数)", "describe": "特殊字符数超过限制(2)", "Severity": "WARNING", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "800", "TypeName": "会话固定拦截", "Id": "Srhino-10491300", "Name": "可能的会话固定攻击", "describe": "在HTML中设置Cookie值", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "800", "TypeName": "会话固定拦截", "Id": "Srhino-10491301", "Name": "可能的会话固定攻击2", "describe": "具有来自其他域的SessionID参数名称", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "800", "TypeName": "会话固定拦截", "Id": "Srhino-10491302", "Name": "可能的会话固定攻击3", "describe": "没有引用页面的SessionID参数名称", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491400", "Name": "可疑的Java类", "describe": "检测请求中是否存在java.lang.runtime等可以类", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491401", "Name": "Java进程生成(CVE-2017-9805)的远程命令执行", "describe": "是否存在S2-052漏洞利用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491402", "Name": "Java序列化(CVE-2015-4852)的远程命令执行", "describe": "请求参数是否存在远程命令执行操作", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491403", "Name": "可疑的Java类", "describe": "检测请求中是否含有java.io.InputStream等可疑类", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491404", "Name": "Java注入攻击：Java脚本文件上传", "describe": "上传文件是否是jsp脚本", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491405", "Name": "潜在的远程命令执行：Log4j/Log4shell", "describe": "是否存在log4j漏洞利用", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491415", "Name": "潜在的远程命令执行：Log4j/Log4shell", "describe": "这条规则是log4j漏洞利用规则Srhino-10491405的加强版", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10492400", "Name": "魔术字节", "describe": "序列化编码头部特征", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10492401", "Name": "经Base64编码的魔术字节", "describe": "经过base64编码的序列化头部特征", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10492404", "Name": "Java序列化(CVE-2015-4852)的远程命令执行", "describe": "是否存在weglogicT3反序列化漏洞", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10492405", "Name": "可疑的Java方法", "describe": "是否调用了危险方法如java.runtime", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10492406", "Name": "恶意的类加载载荷的远程命令执行", "describe": "恶意类的远程加载", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10493400", "Name": "匹配经Base64编码的字符串的可疑关键词", "describe": "一些关键字符串的base64例如:runtime", "Severity": "CRITICAL", "Level": "3", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "1000", "TypeName": "常见java漏洞拦截", "Id": "Srhino-10491425", "Name": "潜在的远程命令执行：Log4j/Log4shell", "describe": "防止log4j命令执行升级版", "Severity": "CRITICAL", "Level": "4", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "4000", "TypeName": "路径泄露拦截", "Id": "Srhino-10591003", "Name": "目录信息泄露", "describe": "目录信息被列出", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "4000", "TypeName": "路径泄露拦截", "Id": "Srhino-10591004", "Name": "CGI源代码泄露", "describe": "检测响应中是否存在疑似CGI源码内容", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "4000", "TypeName": "路径泄露拦截", "Id": "Srhino-10591000", "Name": "应用程序返回了500级别的状态码", "describe": "隐藏5xx错误", "Severity": "ERROR", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591101", "Name": "MicrosoftAccessSQL信息泄露", "describe": "返回包中是否含有ODBCMicrosoftAccessDriver等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591102", "Name": "OracleSQL信息泄露", "describe": "返回包中是否含有java\\.sql\\.SQLException等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591103", "Name": "DB2SQL信息泄露", "describe": "返回包中是否含有DB2SQLerror等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591104", "Name": "EMCSQL信息泄露", "describe": "返回包中是否含有EMCSQL的敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591105", "Name": "firebirdSQL信息泄露", "describe": "返回包中是否含有firebirdSQL的敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591106", "Name": "FrontbaseSQL信息泄露", "describe": "返回包中是否含有FrontbaseSQL的敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591107", "Name": "hsqldbSQL信息泄露", "describe": "返回包中是否含有org\\.hsqldb\\.jdbc\"等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591108", "Name": "informixSQL信息泄露", "describe": "返回包中是否含有com\\.informix\\.jdbc等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10591109", "Name": "ingresSQL信息泄露", "describe": "返回包中是否含有IngresSQLSTATE等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10592100", "Name": "interbaseSQL信息泄露", "describe": "返回包中是否含有Unexpectedendofcommandinstatement等敏感信息泄露", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10592101", "Name": "maxDBSQL信息泄露", "describe": "返回包中是否含有SQLerror.xxxxPOS等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10592102", "Name": "mssqlSQL信息泄露", "describe": "返回包中是否含有System.Data.OleDb.OleDbException等敏感信息", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10592103", "Name": "mysqlSQL信息泄露", "describe": "返回包中是否含有suppliedargumentisnotavalid等敏感信息泄露", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10592104", "Name": "postgresSQL信息泄露", "describe": "返回包中是否含有UnabletoconnecttoPostgreSQLserver等敏感信息泄露", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10592105", "Name": "sqliteSQL信息泄露", "describe": "返回包中是否含有SQLite/JDBCDriver等敏感信息泄露", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "8000", "TypeName": "Sql信息泄露拦截", "Id": "Srhino-10592106", "Name": "SybaseSQL信息泄露", "describe": "返回包中是否含有SybaseSql数据库的敏感信息Sybase.*Servermessage.等泄露", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "10000", "TypeName": "JAVA信息泄露拦截", "Id": "Srhino-10591200", "Name": "Java源代码泄露", "describe": "返回包中是否含有response.write等敏感关键词", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "10000", "TypeName": "JAVA信息泄露拦截", "Id": "Srhino-10591201", "Name": "Java错误", "describe": "返回包中是否含有java.lang.等错误信息泄露", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "20000", "TypeName": "Php信息泄露拦截", "Id": "Srhino-10591300", "Name": "PHP信息泄露", "describe": "返回包中是否含有Pathcannotbeempty等信息泄露", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "20000", "TypeName": "Php信息泄露拦截", "Id": "Srhino-10591301", "Name": "PHP源代码泄露", "describe": "返回包中是否含有源代码特征例如$_GET,$_POST等", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "20000", "TypeName": "Php信息泄露拦截", "Id": "Srhino-10591302", "Name": "PHP源代码泄露", "describe": "返回包中是否含有php标签<?php", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "20000", "TypeName": "Php信息泄露拦截", "Id": "Srhino-10591310", "Name": "PHP信息泄露", "describe": "返回包中是否含有Invaliddate等信息泄露", "Severity": "ERROR", "Level": "2", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "40000", "TypeName": "IIS信息泄露拦截", "Id": "Srhino-10591400", "Name": "IIS安装位置泄露", "describe": "返回包中是否含有IIS的安装路径", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "40000", "TypeName": "IIS信息泄露拦截", "Id": "Srhino-10591401", "Name": "应用程序可用性错误", "describe": "返回包中是否含有MicrosoftOLEDBProviderforSQLServerxxx等错误信息", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "40000", "TypeName": "IIS信息泄露拦截", "Id": "Srhino-10591402", "Name": "IIS敏感信息泄露", "describe": "返回包中是否含有IIS暴露导致泄露一些敏感信息", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "2", "ClassName": "WEB信息泄露防护", "TypeId": "40000", "TypeName": "IIS信息泄露拦截", "Id": "Srhino-10591403", "Name": "IIS错误信息泄露", "describe": "返回包中是否含有ServerErrorin.xxxxApplication的信息泄露", "Severity": "ERROR", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591500", "Name": "Webshell", "describe": "返回包中是否含有Webshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591501", "Name": "r57Webshell", "describe": "返回包中是否含有r57Webshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591502", "Name": "WSOWebshell", "describe": "返回包中是否含有WSOWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591503", "Name": "b4tm4nWebshell", "describe": "返回包中是否含有b4tm4nWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591504", "Name": "MiniShellWebshell", "describe": "返回包中是否含有MiniShellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591505", "Name": "<PERSON><PERSON><PERSON>", "describe": "返回包中是否含有AshiyaneWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591506", "Name": "Symlink_SaWebshell", "describe": "返回包中是否含有Symlink_SaWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591507", "Name": "CasuSWebshell", "describe": "返回包中是否含有CasuSWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591508", "Name": "GRPWebShell", "describe": "返回包中是否含有GRPWebShell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10591509", "Name": "NGHshellWebshell", "describe": "返回包中是否含有NGHshellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592500", "Name": "SimAttackerWebshell", "describe": "返回包中是否含有SimAttackerWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592501", "Name": "未知Webshell1", "describe": "返回包中是否含有未知Webshell1特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592502", "Name": "lamashellWeb<PERSON>ll", "describe": "返回包中是否含有lamashellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592503", "Name": "lostDCWebshell", "describe": "返回包中是否含有lostDCWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592504", "Name": "未知Webshell2", "describe": "返回包中是否含有未知Webshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592505", "Name": "未知Webshell3", "describe": "返回包中是否含有未知Webshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592506", "Name": "Ru24PostWebShellWebshell", "describe": "返回包中是否含有Ru24PostWebShellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592507", "Name": "s72ShellWebshell", "describe": "返回包中是否含有s72ShellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592508", "Name": "PhpSpyWebshell", "describe": "返回包中是否含有PhpSpyWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10592509", "Name": "g00nshellWebshell", "describe": "返回包中是否含有g00nshellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10593500", "Name": "PuNkHoLicShellWebshell", "describe": "返回包中是否含有PuNkHoLicShellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10593501", "Name": "azrailWebshell", "describe": "返回包中是否含有azrailWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10593502", "Name": "SmEvK_PaThAnShellWebshell", "describe": "返回包中是否含有SmEvK_PaThAnShellWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10593503", "Name": "ShellIWebshell", "describe": "返回包中是否含有ShellIWebshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10593504", "Name": "b374km1n1Webshell", "describe": "返回包中是否含有b374km1n1Webshell特征", "Severity": "CRITICAL", "Level": "1", "Status": "1", "ActionStatus": "0"}, {"ClassId": "1", "ClassName": "WEB应用安全防护", "TypeId": "80000", "TypeName": "WebShell检测", "Id": "Srhino-10593505", "Name": "webadmin.php文件管理器", "describe": "返回包中是否含有webadmin.php的特征", "Severity": "CRITICAL", "Level": "2", "Status": "1", "ActionStatus": "0"}]