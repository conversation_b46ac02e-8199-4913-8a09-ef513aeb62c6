export default class CountDate {
  /**
   *interValDate:['20230501','20240103']
   *formatList: ["yyyy-mm-dd", "yyyy-mm"]
   *joinFormat: -
   * @param {Object} data
   */
  constructor(data) {
    const { interValDate, formatList, joinFormat } = data;
    const initDate = [this.normalizeDateString(interValDate[0], "-"), this.normalizeDateString(interValDate[1], "-")];
    this.dateList = initDate;
    this.startDate = new Date(initDate[0]);
    this.endDate = new Date(initDate[1]);
    this.formatList = formatList || ["yyyy-mm-dd"];
    this.joinFormat = joinFormat ? joinFormat : "";
    this.FORMAT_ENUM = {
      dd: "AllDays",
      mm: "AllMonths",
      yyyy: "AllYears",
      "mm-dd": "MonthDays",
      "yyyy-mm": "YearsMonth",
      "yyyy-mm-dd": "YearsMonthDay",
    };
  }
  normalizeDateString(inputDate, format) {
    let dateObject;
    if (/^\d{8}$/.test(inputDate)) {
      const year = parseInt(inputDate.substring(0, 4), 10);
      const month = parseInt(inputDate.substring(4, 6), 10) - 1;
      const day = parseInt(inputDate.substring(6, 8), 10);
      dateObject = new Date(year, month, day);
    } else {
      dateObject = new Date(inputDate);
    }
    if (!isNaN(dateObject.getTime())) {
      const year = dateObject.getFullYear();
      const month = (dateObject.getMonth() + 1).toString().padStart(2, "0");
      const day = dateObject.getDate().toString().padStart(2, "0");
      const result = format === "-" ? `${year}-${month}-${day}` : `${year}${month}${day}`;
      return result;
    }
    return null;
  }
  getYearsMonthDay() {
    const uniqueYearMonthDays = [];
    const startDate = new Date(this.startDate);
    const join = this.joinFormat;
    while (startDate <= this.endDate) {
      const year = startDate.getFullYear();
      const month = startDate.getMonth() + 1;
      const day = startDate.getDate();
      const key = `${year.toString()}${join}${(month < 10 ? "0" : "") + month.toString()}${join}${
        (day < 10 ? "0" : "") + day.toString()
      }`;
      uniqueYearMonthDays.push(key);
      startDate.setDate(startDate.getDate() + 1);
    }
    return this.configResult(uniqueYearMonthDays);
  }
  getYearsMonth() {
    const join = this.joinFormat;
    const result = [];
    const startDate = new Date(this.startDate);
    while (startDate <= this.endDate) {
      const year = startDate.getFullYear();
      const month = (startDate.getMonth() + 1).toString().padStart(2, "0");
      const formattedDate = `${year}${join}${month}`;
      result.push(formattedDate);
      startDate.setDate(startDate.getDate() + 1);
    }
    return this.configResult(result);
  }
  getMonthDays() {
    const result = [];
    const startDate = new Date(this.startDate);
    const join = this.joinFormat;
    while (startDate <= this.endDate) {
      const month = (startDate.getMonth() + 1).toString().padStart(2, "0");
      const day = startDate.getDate().toString().padStart(2, "0");
      const formattedDate = `${month}${join}${day}`;
      result.push(formattedDate);
      startDate.setDate(startDate.getDate() + 1);
    }
    return this.configResult(result);
  }
  getAllDays() {
    const result = [];
    const startDate = new Date(this.startDate);
    while (startDate <= this.endDate) {
      const day = startDate.getDate();
      result.push(day);
      startDate.setDate(startDate.getDate() + 1);
    }
    return this.configResult(result);
  }
  getAllMonths() {
    const result = [];
    const startDate = new Date(this.startDate);
    while (startDate <= this.endDate) {
      const month = startDate.getMonth() + 1;
      result.push(month);
      startDate.setDate(startDate.getDate() + 1);
    }
    return this.configResult(result);
  }
  getAllYears() {
    const result = [];
    const startDate = new Date(this.startDate);
    while (startDate <= this.endDate) {
      const year = startDate.getFullYear();
      result.push(year);
      startDate.setDate(startDate.getDate() + 1);
    }
    return this.configResult(result);
  }
  configResult(data) {
    const list = [...new Set(data)];
    return { list, total: list.length };
  }
  countTotal() {
    return this.formatList.reduce((acc, cur) => {
      const funName = `get${this.FORMAT_ENUM[cur]}`;
      const result = this[funName]();
      acc += result.total;
      return acc;
    }, 0);
  }
}
