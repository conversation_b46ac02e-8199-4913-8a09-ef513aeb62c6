<template>
  <div class="plugin-list-input">
    <div class="input-row">
      <ou-input
        @input="validateInput"
        @blur="validateInput"
        :class="{ error: errorMessage }"
        :placeholder="placeholder"
        v-model.trim="inputValue"
        @keyup.enter="addItem"></ou-input>
      <ou-button @click="addItem">添加</ou-button>
    </div>
    <div class="error-message">{{ errorMessage }}</div>
    <el-scrollbar
      class="list-content"
      :style="`--list-row: ${
        modelValue.length < minRow ? minRow : modelValue.length > maxRow ? maxRow : modelValue.length
      }`">
      <ul>
        <li v-for="(item, index) in modelValue" :key="index">
          <span>{{ item }}</span>
          <ou-icon @click="deleteItem(index)"><Close /></ou-icon>
        </li>
      </ul>
    </el-scrollbar>
  </div>
</template>
<script setup>
  import { ref } from "vue";
  import { OuModal } from "@ouryun/ouryun-plus";
  const props = defineProps({
    // 输入框占位符
    modelValue: {
      required: true,
      type: Array,
    },
    // 输入框占位符
    placeholder: {
      type: String,
      default: "请输入",
    },
    // 是否允许重复添加
    allowRepeat: {
      type: Boolean,
      default: false,
    },
    // 当重复添加时的提示语
    repeatWarning: {
      type: String,
      default: `{value}已存在`,
    },
    // 列表显示的最小行数
    minRow: {
      type: Number,
      default: 2,
    },
    // 列表显示的最大行数
    maxRow: {
      type: Number,
      default: 5,
    },
    /**
     * 验证规则
     * 示例：
        [
          {
            pattern: /^\d+$/,
            message: "请输入数字",
          },
          {
            validator: (value, callback) => {
              if (!/^\d+$/.test(value)) {
                return callback(new Error("请输入数字"));
              }
              callback();
            },
          }
        ]
    */
    rule: {
      type: Array,
    },
  });

  const inputValue = ref("");
  const errorMessage = ref("");

  const validateInput = () => {
    if (!inputValue.value) {
      return (errorMessage.value = props.placeholder);
    } else {
      errorMessage.value = "";
    }
    if (props.rule) {
      props.rule
        .slice()
        .reverse()
        .forEach(item => {
          if (item.hasOwnProperty("validator")) {
            item.validator(inputValue.value, (res = null) => {
              if (res instanceof Error) {
                // 校验失败
                errorMessage.value = res?.message || item.message || "校验失败";
              } else {
                // 校验成功
                errorMessage.value = "";
              }
            });
          } else if (item.hasOwnProperty("pattern")) {
            if (item.pattern.test(inputValue.value)) {
              // 校验成功
              errorMessage.value = "";
            } else {
              // 校验失败
              errorMessage.value = item.message || "校验失败";
            }
          }
        });
      return !errorMessage.value;
    }
  };

  const addItem = () => {
    if (!validateInput() || !inputValue.value) return;
    if (!props.allowRepeat && props.modelValue.includes(inputValue.value)) {
      const repeatWarning = props.repeatWarning.replace("{value}", `“${inputValue.value}”`);
      return OuModal.warning(repeatWarning);
    }
    props.modelValue.unshift(inputValue.value);
    inputValue.value = "";
  };
  const deleteItem = index => {
    props.modelValue.splice(index, 1);
  };
</script>
<style lang="scss" scoped>
  .plugin-list-input {
    width: 100%;
    display: flex;
    flex-direction: column;

    .input-row {
      display: flex;
      column-gap: 4px;
      ::v-deep .ouryun-input {
        flex: 1;
        .ouryun-input__wrapper {
          border-radius: var(--ouryun-border-radius-base);
          box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color)) inset !important;

          &:hover {
            box-shadow: 0 0 0 1px var(--ouryun-input-hover-border-color) inset !important;
          }
          &.is-focus {
            box-shadow: 0 0 0 1px var(--ouryun-input-focus-border-color) inset !important;
          }
        }

        &.error .ouryun-input__wrapper {
          box-shadow: 0 0 0 1px var(--ouryun-color-danger) inset !important;
        }
      }
      .ouryun-button {
        color: #333333;
        background: #eeeeee;
        border-color: #d7dbe0;
        &:hover {
          color: #333333;
          border-color: #d7dbe0;
          background: #e7e7e7;
        }
        &:active {
          background: #d9d9d9;
          color: #333333;
        }
      }
    }

    .error-message {
      color: var(--ouryun-color-danger);
      font-size: 12px;
      line-height: 1;
      margin-top: 2px;
      &:empty {
        display: none;
      }
    }

    $list-row: var(--list-row);

    .list-content {
      width: calc(100% - 2px);
      height: calc(28px * $list-row);
      margin: 4px auto 0;
      box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color));
      border-radius: var(--ouryun-input-border-radius, 2px);
      box-sizing: border-box;
      ul {
        margin: 0;
        padding: 0;
        color: #333;
        li {
          list-style: none;
          width: 100%;
          height: 28px;
          overflow: hidden;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 12px 0 10px;
          line-height: 1;

          &:hover {
            background-color: #f5f7fa;
          }

          & > span {
            flex: 1;
          }

          & > .pn-icon {
            cursor: pointer;
          }
        }
      }
    }
  }
</style>
