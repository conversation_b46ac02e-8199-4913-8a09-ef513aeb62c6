<template>
  <div class="auth-info-continer">
    <!-- <div class="handle-btns">
      <div class="handle-right">
        <ou-button @click="exportAdd" :disabled="authInfo.length === 0">导出</ou-button>
        <ou-button @click="importAdd">导入</ou-button>
      </div>
    </div> -->
    <multilineEditor
      ref="multilineEditorRef"
      v-model="authInfo"
      @importAdd="importAdd"
      @exportAdd="exportAdd"></multilineEditor>
    <ou-opera-dialog
      title="导入"
      v-model="isShowUploadDialog"
      :z-index="996"
      :close-on-click-modal="false"
      draggable
      hideFooter
      :width="step === 'importing' ? '550px' : '528px'">
      <template #content>
        <ou-form
          class="-mb-20"
          v-if="step === 'importing'"
          :model="fileForm"
          labelPosition="left"
          ref="whiteItemFormRef"
          :rules="whiteItemRules"
          :label-width="labelWidth">
          <ou-form-item prop="file" class="pn_required_label" label="">
            <span class="content-tip">请先下载导入模板，并严格按模板要求填写信息后导入，否则信息可能导入失败；</span>
            <ou-button class="upload-text" type="primary" text @click="downTemplate">点击下载模板</ou-button>
            <Upload @getData="getFileData" @on-change="fileChange" ref="myUpload" placeholder="仅限上传 xlsx、xls 文件">
              <ou-button type="primary">上传</ou-button>
            </Upload>
          </ou-form-item>
        </ou-form>
        <div class="import_result" v-else>
          <div v-if="result.state === 1">
            <ou-icon class="icon" color="#62BF78"><SuccessFilled /></ou-icon>
            <div class="tip">导入成功</div>
            <div class="desc">{{ result.msg }}</div>
          </div>
          <div v-else-if="result.state === 2">
            <ou-icon class="icon" color="#ffc53d"><WarningFilled /></ou-icon>
            <div class="tip">部分导入失败</div>
            <div class="desc">{{ result.msg }}</div>
          </div>
          <div v-else>
            <ou-icon class="icon" color="#ff0b0b"><CircleCloseFilled /></ou-icon>
            <div class="tip">导入失败</div>
            <div class="desc">{{ result.msg }}</div>
          </div>
        </div>
      </template>
      <template #footer>
        <div v-if="step === 'importing'">
          <ou-button @click="isShowUploadDialog = false">取消</ou-button>
          <ou-button type="primary" @click="uploadAuthInfo" style="margin-left: 10px">确定</ou-button>
        </div>
        <div v-else>
          <ou-button v-if="result.state === 3" @click="isShowUploadDialog = false">结束</ou-button>
          <ou-button v-else @click="isShowUploadDialog = false">完成</ou-button>
          <ou-button type="primary" v-if="result.state === 2" @click="downFail">下载失败数据</ou-button>
          <ou-button type="primary" v-else @click="step = 'importing'">继续导入</ou-button>
        </div>
      </template>
    </ou-opera-dialog>
  </div>
</template>

<script setup>
  import { computed, ref, nextTick, defineProps, defineEmits, defineExpose, watch } from "vue";
  import { exportExcelFile } from "@/utils/readfile.js";
  import { authUploadFile } from "@/api/plugin.js";
  import { deepClone } from "@/utils/tool.js";
  import Upload from "./LocalUpload";
  import { OuModal, dayjs } from "@ouryun/ouryun-plus";
  import { ExcelHeader } from "../hook/utils";
  import multilineEditor from "./multilineEditor.vue";
  const tableRef = ref(null);
  const multilineEditorRef = ref(null);

  const isShowUploadDialog = ref(false);
  const authInfo = ref([]);
  const step = ref("importing");
  const result = ref({});
  const props = defineProps({
    modelValue: {
      type: Array,
      required: true,
    },
    // 可添加条数
    count: {
      type: Number,
      default: 1000,
    },
  });
  const emits = defineEmits(["update:modelValue"]);
  defineExpose({
    multilineEditorRef,
  });
  authInfo.value = deepClone(props.modelValue) || [];

  watch(
    () => authInfo.value,
    () => {
      emits("update:modelValue", JSON.parse(JSON.stringify(authInfo.value)));
    },
    { deep: true, immediate: true },
  );

  let FailItems = [],
    SuccessItems = [];
  const fileForm = ref({ file: null });

  const returnResult = (sussess, fail) => {
    SuccessItems = sussess?.map(e => {
      const row = {
        user_name: e[0],
        password: e[1],
        exp: e[2] === "长期有效" ? "" : e[2],
        remark: e[3],
      };
      // let edit = confirmBtnDisabled(row);
      return {
        ...row,
        // edit,
      };
    });
    FailItems = fail || [];
  };

  const renderImportResult = data => {
    const { state, success_total = 0, success_items, fail_total = 0, fail_items, fail_reason } = data;
    if (state === 1) {
      // 1：全部成功 2：部分成功 3：全部失败
      result.value = {
        state,
        msg: `全部导入成功，共计${success_total}条`,
      };
      returnResult(success_items);
      // let temp = JSON.parse(JSON.stringify(authInfo.value));
      // SuccessItems && (authInfo.value = temp);
      // SuccessItems && authInfo.value.push(...SuccessItems);
      SuccessItems && multilineEditorRef.value.addmultilineHander(SuccessItems);
    } else if (state === 2) {
      result.value = {
        state,
        msg: `导入成功${success_total}条，导入失败${fail_total}条，点击下载查看失败数据`,
      };
      returnResult(success_items, fail_items);
      // SuccessItems && authInfo.value.push(...SuccessItems);
      SuccessItems && multilineEditorRef.value.addmultilineHander(SuccessItems);
    } else {
      result.value = {
        state,
        msg: `失败原因：“${fail_reason}”`,
      };
    }
  };

  const myUpload = ref(null);
  const uploadAuthInfo = async files => {
    const file = myUpload.value?.getFileData();
    const data = authInfo.value?.map(e => [e.user_name, e.password, e.exp, e.remark]) || [];
    const fd = new FormData();
    fd.append("file", file[0]);
    fd.append("rules", JSON.stringify(data));
    await authUploadFile(fd).then(res => {
      const { data, message, code } = res;
      if (code === 200) {
        renderImportResult(data);
      } else {
        OuModal.success("上传失败");
      }
      step.value = "import_result";
    });
  };

  const downFail = () => {
    const data = FailItems.map(e => ({
      [ExcelHeader[0]]: e[0],
      [ExcelHeader[1]]: e[1],
      [ExcelHeader[2]]: e[2],
      [ExcelHeader[3]]: e[3],
      失败原因: e[4],
    }));
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const config = {
      header: [...ExcelHeader, "失败原因"],
      fileName: `导入失败认证鉴权_${formatTime}`,
      data,
    };
    exportExcelFile(config);
  };

  const downTemplate = () => {
    const formatTime = dayjs().format("YYYYMMDDHHmmss");
    const config = {
      header: ExcelHeader,
      fileName: `认证信息模板${formatTime}`,
      data: [
        ["Aa123456", "Aa@123456", "2024-12-31", "总经办-赵总"],
        ["123132_CN", "Ouryun_10.zq", "长期有效", "测试账号"],
      ].map(e => ({
        [ExcelHeader[0]]: e[0],
        [ExcelHeader[1]]: e[1],
        [ExcelHeader[2]]: e[2],
        [ExcelHeader[3]]: e[3],
      })),
    };
    exportExcelFile(config);
  };

  const searchResult = ref(true);
  const searchAuthInfo = searchname => {
    if (searchname) {
      let c = 0;
      authInfo.value = authInfo.value?.map(item => {
        const user_name = item.user_name?.toUpperCase();
        const remark = item.remark ? item.remark.toUpperCase() : "";
        let show =
          user_name.indexOf(searchname.toUpperCase()) !== -1 || remark.indexOf(searchname.toUpperCase()) !== -1;
        c += show ? 1 : 0;
        return { ...item, show };
      });
      searchResult.value = c > 0;
    } else {
      authInfo.value = authInfo.value?.map(item => ({ ...item, show: true }));
      searchResult.value = true;
    }
  };

  // 导入弹框
  const importAdd = () => {
    if (authInfo.value?.length > 1000) {
      OuModal.warning("规则条数已超过上限1000条，请进行清理后再提交");
      return;
    }
    isShowUploadDialog.value = true;
    step.value = "importing";
  };
  // 导出弹框
  const exportAdd = () => {
    const content = {
      content: `确认导出${authInfo.value.length}条数据吗？`,
      moreContent: `数据将以Excel格式导出，导出范围为：已查询出并在列表内显示的数据。`,
    };
    OuModal.confirm(content).then(() => {
      const data = authInfo.value.map(e => ({
        [ExcelHeader[0]]: e.user_name,
        [ExcelHeader[1]]: e.password,
        [ExcelHeader[2]]: e.exp === "" || e.exp === undefined || e.exp === null ? "长期有效" : e.exp,
        [ExcelHeader[3]]: e.remark,
      }));
      const formatTime = dayjs().format("YYYYMMDDHHmmss");
      const config = {
        header: ExcelHeader,
        fileName: `认证鉴权_${formatTime}`,
        data,
      };
      exportExcelFile(config);
    });
  };

  const form = ref([
    {
      type: "input",
      prop: "user_name",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "请输入账号名称",
      },
    },
    {
      type: "input",
      prop: "password",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "请输入密码",
      },
    },
    {
      type: "date",
      prop: "exp",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "长期有效",
      },
    },
    {
      name: "备注",
      prop: "remark",
      type: "input",
      width: "200px",
      defaultValue: "",
      attrs: {
        placeholder: "请输入备注信息",
      },
    },
  ]);

  // 校验字段
  const verifyContent = (property, row) => {
    if (!property || !row) {
      return;
    }
    let verifyText = "";
    const user_nameCheck = /^[0-9a-zA-Z_-]{1,63}$/;
    const passwordCheck = /^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*?_-]).{8,63}$/;
    const remarkCheck = /^[\u4e00-\u9fa5_a-zA-Z0-9-]+$/;
    const content = row[property];

    switch (property) {
      case "user_name":
        if (!content.length) {
          verifyText = "请输入账号名称";
        } else if (content.length > 63) {
          verifyText = "长度不多于63个字符   ";
        } else if (!user_nameCheck.test(content)) {
          verifyText = "请输入英文、数字、-和_";
        } else {
          if (authInfo.value.length > 1) {
            const newArr = authInfo.value.filter(item => item.edit === false && item.user_name === content);
            if (newArr.length) {
              verifyText = `账号名称不能重复`;
            }
          }
        }
        break;
      case "password":
        if (!content.length) {
          verifyText = "请输入密码";
        } else if (content.length < 8) {
          verifyText = "长度不少于8个字符";
        } else if (content.length > 63) {
          verifyText = "长度不多于63个字符";
        } else if (!passwordCheck.test(content)) {
          verifyText = "同时包含大写字母、小写字母、数字和特殊符号";
        }
        break;
      case "remark":
        if (content.length > 63) {
          verifyText = "长度限制为63个字符";
        } else if (content.length && !remarkCheck.test(content)) {
          verifyText = "请输入中文、英文、数字、-和_";
        }
        break;
    }
    return verifyText;
  };
</script>

<style scoped lang="scss">
  .auth-info-continer {
    width: 100%;

    // :deep(.ouryun-form-item){
    //   margin-bottom: 20px;
    // }

    .handle-btns {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
    }
    .auth-info-content {
      box-shadow: 0 0 0 1px #dedede;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 1px;

      .editor {
        border-bottom: 1px solid var(--ouryun-table-border-color);
      }
      :deep(.ouryun-table) {
        margin: -1px 0;
        border-radius: 2px 2px 0 0;

        .ouryun-table__header-wrapper {
          height: 40px;
          border-bottom: 1px solid var(--ouryun-table-border-color);
          box-sizing: border-box;
        }

        .ouryun-table__cell .cell-show-content {
          width: 100%;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
          padding-left: 11px;
        }
      }
    }
  }

  .import_result {
    text-align: center;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    margin-top: 8px;
    .icon {
      font-size: 50px;
    }

    .tip {
      margin: 20px 0 12px;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
    }

    .desc {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 21px;
    }
  }

  .auth-info-footer {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    color: #666;
  }

  .form-content-width {
    width: 300px !important;
  }

  .content-tip {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 22px;
  }

  .upload-text {
    margin: 4px 0;
    height: 22px;
  }

  .password-cell {
    width: 100%;
    padding-left: 11px;
    padding-right: 14px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
    position: relative;
    .icon-password {
      position: absolute;
      right: 1px;
      &:hover {
        color: var(--ouryun-color-brand-base);
        cursor: pointer;
      }
    }
  }

  .auth-info-content {
    ::v-deep {
      .ouryun-table .cell {
        overflow: visible;
        padding-right: 0 !important;
        .ou-text-ellipsis {
          padding-left: 8px;
        }
      }
      .ouryun-table__cell:first-child .cell div {
        margin-left: 21px;
      }
      .ouryun-table .ouryun-table__header tr th.ouryun-table__cell:not(:last-child) .cell {
        padding-left: 16px !important;
      }
      .ouryun-table .ouryun-table__cell {
        z-index: auto;
        .cell {
          width: 100%;
          display: flex;
          align-items: center;
          height: 32px;
          // line-height: 32px;
          // padding-right: 0 !important;
          // padding-left: 8px !important;
          & > div:not(.ouryun-select):not(.ouryun-input):not(.cell-verify-content) {
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }
    ::v-deep {
      .ouryun-input__wrapper {
        box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color)) inset !important;
      }

      .ouryun-select__tags .ouryun-select__tags-text {
        max-width: none !important;
      }
      .ouryun-table__body-wrapper tbody .ouryun-table__row {
        @mixin errorMessage {
          white-space: nowrap;
          height: 36px;
          width: auto;
          font-size: 14px;
          line-height: 36px;
          padding: 0 10px;
          background-color: #ff6d6d;
          color: rgba(255, 255, 255, 0.85);
          position: absolute;
          border-radius: 3px;
          z-index: 990;
          &::before {
            content: "";
            position: absolute;
            left: 20px;
            top: calc(100% - 47px);
            width: 0px;
            height: 0px;
            border: 6px solid transparent;
          }
        }
        .cell-verify-content {
          position: relative;
          width: 100%;
          .ouryun-input__wrapper,
          .ouryun-select__wrapper {
            box-shadow: 0 0 0 1px var(--ouryun-color-danger) inset !important;
          }
        }

        &:last-child .cell-verify {
          @include errorMessage;
          top: -44px;
          &::before {
            top: 100%;
            border-top-color: #ff6d6d;
          }
        }
        .cell-verify,
        &:nth-child(-n + 2) .cell-verify {
          @include errorMessage;
          top: 40px;
          &::before {
            top: calc(100% - 47px);
            border-bottom-color: #ff6d6d;
          }
        }

        .cell-verify:empty {
          display: none;
        }
      }
    }
  }

  .ouryun-multiline-editor {
    .ouryun-select__wrapper {
      box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color)) inset !important;
      &.is-hovering:not(.is-focused) {
        box-shadow: 0 0 0 1px var(--ouryun-border-color-hover) inset !important;
      }
      &.is-focused {
        box-shadow: 0 0 0 1px var(--ouryun-color-primary) inset !important;
      }
    }
    .ouryun-input__wrapper {
      box-shadow: 0 0 0 1px var(--ouryun-input-border-color, var(--ouryun-border-color)) inset !important;

      &:hover {
        box-shadow: 0 0 0 1px var(--ouryun-input-hover-border-color) inset !important;
      }
      &.is-focus {
        box-shadow: 0 0 0 1px var(--ouryun-input-focus-border-color) inset !important;
      }
    }
    display: flex;
    flex-direction: column;
    .ouryun-multiline-editor-row {
      display: flex;
      flex-wrap: nowrap;
      column-gap: 8px;
      height: 32px;
      margin-bottom: 12px;
      &:has(.validator-error) {
        margin-bottom: 20px;
      }
      &:last-child {
        margin-bottom: 0;
      }

      .input-group {
        width: var(--width);
        display: grid;
        grid-template-columns: var(--grid-template-columns);
        column-gap: 8px;
        .validator-error {
          position: relative;
          &::after {
            content: attr(data-error-text);
            font-size: 12px;
            line-height: 1;
            color: var(--ouryun-color-danger);
            width: 100%;
            position: absolute;
            left: 1px;
            bottom: 0px;
            transform: translateY(calc(100% + 2px));
          }
          .ouryun-input .ouryun-input__wrapper {
            box-shadow: 0 0 0 1px var(--ouryun-color-danger) inset !important;
            .ouryun-input__inner {
              color: var(--ouryun-color-danger);
              caret-color: #000;
            }
          }
          .ouryun-select .ouryun-select__wrapper {
            box-shadow: 0 0 0 1px var(--ouryun-color-danger) inset !important;
            .ouryun-select__selected-item:not(.is-transparent) {
              color: var(--ouryun-color-danger);
            }
          }
        }
      }

      .button-group {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
        column-gap: 8px;
        .ouryun-button-custom {
          padding: 0;
        }
        .ouryun-button {
          margin: 0;
          border-style: dashed;
          width: 32px;
          height: 32px;
        }
      }
    }
  }
</style>
