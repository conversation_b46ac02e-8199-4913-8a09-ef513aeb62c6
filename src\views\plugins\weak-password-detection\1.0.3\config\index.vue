<template>
  <div class="add_conent_flex1">
    <ou-form labelPosition="right" ref="formRef" label-width="140px" :model="baseForm" :rules="rules">
      <ou-form-item label="检测规则：">
        <div class="list-handle">
          <ou-button type="primary" text @click="showDialog('add')">添加</ou-button>
          <ou-button type="primary" text :disabled="isCheckData" @click="batchRemove">批量删除</ou-button>
          <MoveGroup
            v-model:list="baseForm.ruleList"
            style="margin-left: 8px"
            :selections="baseForm.multipleList"
            @moved="list => routeMoved('route', list)"
            :disabled="isCheckData"></MoveGroup>
        </div>
        <ou-table
          :data="baseForm.ruleList"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          :height="baseForm.ruleList?.length >= 5 ? 245 : baseForm.ruleList?.length <= 4 ? 205 : 'auto'">
          <ou-table-column type="selection" width="32" align="center" :selectable="selectable"></ou-table-column>
          <ou-table-column label="优先级" type="index" width="58" align="center"></ou-table-column>
          <ou-table-column label="账号字段" prop="user_name">
            <template #default="scope">
              <ou-text-ellipsis :content="mapTableContent(scope.row, 'user_name')"></ou-text-ellipsis>
            </template>
          </ou-table-column>
          <ou-table-column label="密码字段">
            <template #default="scope">
              <ou-text-ellipsis :content="mapTableContent(scope.row, 'password_name')"></ou-text-ellipsis>
            </template>
          </ou-table-column>
          <ou-table-column label="请求路径">
            <template #default="scope">
              <ou-text-ellipsis :content="mapTableContent(scope.row, 'path')"></ou-text-ellipsis>
            </template>
          </ou-table-column>
          <ou-table-column label="状态码" width="100">
            <template #default="scope">
              <ou-text-ellipsis :content="mapTableContent(scope.row, 'status_code')"></ou-text-ellipsis>
            </template>
          </ou-table-column>
          <ou-table-column label="登录凭证字段">
            <template #default="scope">
              <ou-text-ellipsis :content="mapTableContent(scope.row, 'token')"></ou-text-ellipsis>
            </template>
          </ou-table-column>
          <ou-table-column label="弱密码库">
            <template #default="scope">
              <ou-text-ellipsis :content="mapTableContent(scope.row, 'rule_base')"></ou-text-ellipsis>
            </template>
          </ou-table-column>
          <ou-table-column label="状态" width="80">
            <template #default="scope">
              <ou-switch
                v-model="scope.row.status"
                size="small"
                active-text="启用"
                inactive-text="禁用"
                class="switch-offside" />
            </template>
          </ou-table-column>
          <ou-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <ou-button
                type="primary"
                :disabled="scope.row.isDefault"
                text
                @click="showDialog('edit', scope.row, scope.$index)">
                编辑
              </ou-button>
              <ou-button type="primary" text :disabled="scope.row.isDefault" @click="remove(scope.$index)">
                删除
              </ou-button>
            </template>
          </ou-table-column>
        </ou-table>
      </ou-form-item>
    </ou-form>
    <ou-opera-dialog
      :width="915"
      v-model="dialog.show"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      :title="dialog.title[dialog.type]">
      <template #content>
        <ou-form labelPosition="right" ref="formRef" label-width="140px" :model="ruleForm" :rules="formRules">
          <div class="rule-title">识别规则</div>
          <ou-form-item label="账号字段：" style="margin-bottom: 0px">
            <template #label>
              <span class="required-label">账号字段：</span>
            </template>
            <div v-for="(item, index) in ruleForm.userName" class="flex-item">
              <ou-form-item class="form-item-left" :prop="`userName.${index}.keys`" :rules="keysRules">
                <ou-select placeholder="识别位置" v-model="item.keys" multiple>
                  <ou-option
                    v-for="option in positionSelect"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    :disabled="isOptionDisabled(option.value, index, 'userName')"></ou-option>
                </ou-select>
              </ou-form-item>
              <ou-form-item class="form-item" :prop="`userName.${index}.values`" :rules="valuesRules">
                <ou-input
                  v-model.trim="item.values"
                  placeholder="请输入英文、数字、英文标点符号，多个以逗号分隔"></ou-input>
              </ou-form-item>
              <div class="handle-item-btn">
                <ou-button
                  class="button-icon-border dashed"
                  v-if="ruleForm.userName.length > 1"
                  @click="removeItem('userName', index)">
                  <ou-icon size="14" style="color: #999999"><Minus /></ou-icon>
                </ou-button>
                <ou-button
                  class="button-icon-border dashed"
                  @click="addItem('userName')"
                  :disabled="isDisabledAddItem('userName')"
                  v-if="index === ruleForm.userName.length - 1 && ruleForm.userName.length < 5">
                  <ou-icon size="14" style="color: #999999"><Plus /></ou-icon>
                </ou-button>
              </div>
            </div>
          </ou-form-item>
          <div class="form-item-ped"></div>
          <ou-form-item label="密码字段：" style="margin-bottom: 0px">
            <template #label>
              <span class="required-label">密码字段：</span>
            </template>
            <div v-for="(v, i) in ruleForm.passwordName" class="flex-item">
              <ou-form-item class="form-item-left" :prop="`passwordName.${i}.keys`" :rules="keysRules">
                <ou-select placeholder="识别位置" multiple v-model="v.keys">
                  <ou-option
                    v-for="option in positionSelect"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    :disabled="isOptionDisabled(option.value, i, 'passwordName')"></ou-option>
                </ou-select>
              </ou-form-item>
              <ou-form-item class="form-item" :prop="`passwordName.${i}.values`" :rules="valuesRules">
                <ou-input
                  v-model.trim="v.values"
                  placeholder="请输入英文、数字、英文标点符号，多个以逗号分隔"></ou-input>
              </ou-form-item>
              <div class="handle-item-btn">
                <ou-button
                  class="button-icon-border dashed"
                  v-if="ruleForm.passwordName.length > 1"
                  @click="removeItem('passwordName', i)">
                  <ou-icon size="14" style="color: #999999"><Minus /></ou-icon>
                </ou-button>
                <ou-button
                  class="button-icon-border dashed"
                  :disabled="isDisabledAddItem('passwordName')"
                  v-if="i === ruleForm.passwordName.length - 1 && ruleForm.passwordName.length < 5"
                  @click="addItem('passwordName')">
                  <ou-icon size="14" style="color: #999999"><Plus /></ou-icon>
                </ou-button>
              </div>
            </div>
          </ou-form-item>
          <div class="form-item-ped"></div>
          <ou-form-item label="请求路径：" prop="path">
            <OuTextArea
              style="width: 608px"
              :placeholder="'每行一条数据，单条数据长度限制为512字符'"
              :validateDuplicate="true"
              :validateEditValue="validatePaths"
              :allowSpaces="false"
              height="130"
              ref="pathTextAreaRef"
              emptyMessage="请求路径不能为空"
              :maxLines="100"
              :validateEmpty="true"
              :allowEmptyLines="false"
              v-model:defaultValue.sync="ruleForm.path" />

            <!-- <ou-input
              style="width: 608px"
              v-model="ruleForm.path"
              placeholder="每行一条数据，单条数据长度限制为512字符"
              :autosize="{ minRows: 4, maxRows: 6 }"
              type="textarea"
              maxlength="150"></ou-input> -->
          </ou-form-item>
          <div class="rule-title">登录成功报文特征</div>
          <ou-form-item label="状态码：" prop="status_code">
            <OuTextArea
              style="width: 260px"
              :placeholder="'请输入200-599的整数'"
              :validateDuplicate="true"
              height="130"
              :validateEditValue="validateCode"
              :allowSpaces="false"
              ref="codeTextAreaRef"
              emptyMessage="状态码不能为空"
              :maxLines="100"
              :validateEmpty="true"
              :allowEmptyLines="false"
              v-model:defaultValue.sync="ruleForm.status_code" />

            <!-- <ou-input
              style="width: 260px"
              v-model="ruleForm.status_code"
              placeholder="请输入200-599的整数"
              :autosize="{ minRows: 4, maxRows: 6 }"
              type="textarea"
              maxlength="150"></ou-input> -->
          </ou-form-item>
          <div class="form-item-ped"></div>
          <ou-form-item label="登录凭证字段：" style="margin-bottom: 0px">
            <template #label>
              <span class="required-label">登录凭证字段：</span>
            </template>
            <div v-for="(val, ind) in ruleForm.tokenList" class="flex-item">
              <ou-form-item class="form-item-left" :prop="`tokenList.${ind}.keys`" :rules="keysRules">
                <ou-select placeholder="识别位置" multiple v-model="val.keys">
                  <ou-option
                    v-for="option in tokenSelect"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                    :disabled="isOptionDisabled(option.value, ind, 'tokenList')"></ou-option>
                </ou-select>
              </ou-form-item>
              <ou-form-item class="form-item" :prop="`tokenList.${ind}.values`" :rules="valuesRules">
                <ou-input
                  v-model.trim="val.values"
                  placeholder="请输入英文、数字、英文标点符号，多个以逗号分隔"></ou-input>
              </ou-form-item>
              <div class="handle-item-btn">
                <ou-button
                  class="button-icon-border dashed"
                  v-if="ruleForm.tokenList.length > 1"
                  @click="removeItem('tokenList', ind)">
                  <ou-icon size="14" style="color: #999999"><Minus /></ou-icon>
                </ou-button>
                <ou-button
                  class="button-icon-border dashed"
                  :disabled="isDisabledAddItem('tokenList')"
                  v-if="ind === ruleForm.tokenList.length - 1 && ruleForm.tokenList.length < 5"
                  @click="addItem('tokenList')">
                  <ou-icon size="14" style="color: #999999"><Plus /></ou-icon>
                </ou-button>
              </div>
            </div>
          </ou-form-item>
          <div class="form-item-ped"></div>
          <div class="rule-title">检测规则</div>
          <ou-form-item label="弱密码库：" prop="rule_base">
            <!-- {{ baseSelect }} -->
            <ou-page-select
              style="width: 260px"
              :edit-data="editData"
              :multiple="true"
              label="name"
              v-model:value="ruleForm.rule_base"
              :load-data="getRuleBase"
              placeholder="请选择"></ou-page-select>
          </ou-form-item>
        </ou-form>
      </template>
    </ou-opera-dialog>
  </div>
</template>

<script setup>
  import MoveGroup from "@/components/MoveGroup";
  import { getWeakRuleBase } from "@/api/pgModule.js";
  import useState, { compateData } from "../hook";
  import { deepClone } from "@/utils/index.js";
  import { ref, reactive, computed, watch, nextTick, h } from "vue";
  import { weakPasswordDetection } from "../hook/history.js";

  const {
    baseForm,
    ruleForm,
    keysRules,
    valuesRules,
    dialog,
    ruleBaseList,
    formRef,
    rules,
    formRules,
    positionSelect,
    tokenSelect,
    validateCode,
    validatePaths,
    mapTableContent,
    codeTextAreaRef,
    pathTextAreaRef,
  } = useState();
  const props = defineProps({
    // 用于回显
    content: {
      type: String,
      default: "",
    },
    matedata: {
      type: Object,
      default: {},
    },
    global: {
      type: String,
      default: "",
    },
  });
  const editData = ref([]);
  const oldForm = ref(null);
  let baseSelect = ref(null);
  const handleIndex = ref(0);
  const initPositionSelect = ref([]);
  watch(
    () => dialog.value.show,
    nData => {
      if (!nData) {
        nextTick(() => {
          clearForm();
        });
      } else {
        initPositionSelect.value = positionSelect;
      }
    },
  );
  const handleSelectionChange = val => {
    baseForm.value.multipleList = val;
  };
  const isCheckData = computed(() => {
    const list = baseForm.value.multipleList?.filter(e => !e.isDefault) || [];
    return !list.length;
  });
  //禁用其他选项
  const isOptionDisabled = (value, currentIndex, fieldType) => {
    return ruleForm.value[fieldType].some((row, index) => {
      if (index === currentIndex) return false;
      return row.keys.includes(value);
    });
  };
  //是否禁用新增按钮
  const isDisabledAddItem = fieldType => {
    const isValid = ruleForm.value[fieldType].every(
      item =>
        Array.isArray(item.keys) &&
        item.keys.length > 0 &&
        item.keys.every(k => k !== undefined && k !== null && k !== "") &&
        typeof item.values === "string" &&
        item.values.trim() !== "",
    );
    return !isValid;
  };
  //禁用表格多选
  const selectable = row => {
    return !row.isDefault;
  };
  const clearForm = () => {
    ruleForm.value = {
      userName: [
        {
          keys: [],
          values: "",
        },
      ],
      passwordName: [
        {
          keys: [],
          values: "",
        },
      ],
      path: [],
      status_code: ["200"],
      tokenList: [
        {
          keys: [],
          values: "",
        },
      ],
      rule_base: [],
    };
  };
  const showDialog = (type, row, index) => {
    dialog.value.show = true;
    clearFormValidate();
    dialog.value.type = type;
    handleIndex.value = index;
    if (row) {
      ruleForm.value = deepClone({
        ...row,
        userName: row.user_name,
        passwordName: row.password_name,
        status_code: Array.isArray(row.status_code) ? row.status_code : row.status_code.split("\n"),
        path: Array.isArray(row.path) ? row.path : row.path.split("\n"),
        tokenList: row.token,
      });
    }
  };
  const clearFormValidate = () => {
    nextTick(() => {
      formRef.value.clearValidate();
      codeTextAreaRef.value.resetErrorState();
      pathTextAreaRef.value.resetErrorState();
    });
  };
  const handleConfirm = () => {
    formRef.value.validate(valid => {
      const { isValid: isCodeValid } = codeTextAreaRef.value.validateAll();
      const { isValid: isPathValid } = pathTextAreaRef.value.validateAll();
      if (!valid || !isCodeValid || !isPathValid) return;
      const { userName, passwordName, tokenList, status_code, path, rule_base } = ruleForm.value;
      const item = {
        status: true,
        isDefault: false,
        user_name: userName,
        password_name: passwordName,
        token: tokenList,
        status_code: status_code.filter(e => e),
        rule_base,
        path: path.filter(e => e),
      };
      item.rowId = `${Date.now()}`;
      item.ruleEditData = ruleForm.value.rule_base.map(e => baseSelect.value.find(v => v.value === e));
      if (dialog.value.type === "add") {
        baseForm.value.ruleList.unshift(item);
      } else {
        baseForm.value.ruleList.splice(handleIndex.value, 1, item);
      }
      dialog.value.show = false;
    });
  };
  const getRuleBase = val => {
    const query = {
      ...val,
      name: val?.searchName,
    };
    return getWeakRuleBase(query).then(res => {
      const { code, data } = res;
      if (code === 200) {
        const list = (data.list || []).map(e => ({
          value: e.id,
          is_default: e.source_type === 1,
          simple_password: e.simple_password,
          encrypt_type: e.encrypt_type || [],
          union_password: e.union_password,
          name: e.name,
        }));
        baseSelect.value = list;
        return { list, total: data.row_count };
      }
    });
  };
  const handleCancel = () => {};
  const removeItem = (fieldType, index) => {
    ruleForm.value[fieldType].splice(index, 1);
  };
  const batchRemove = () => {
    const list = baseForm.value.multipleList.filter(e => !e.isDefault);
    list.forEach(e => {
      const ind = baseForm.value.ruleList.findIndex(v => v.rowId === e.rowId);
      baseForm.value.ruleList.splice(ind, 1);
    });
  };
  const addItem = fieldType => {
    ruleForm.value[fieldType].push({
      keys: [],
      values: "",
    });
  };
  const configSubmitData = data => {
    return data.reduce((acc, cur) => {
      const { isDefault, password_name, path, status, token, user_name, ruleEditData, status_code, rule_base } = cur;
      const statusCode = (Array.isArray(status_code) ? status_code.filter(e => e) : status_code.split("\n")).map(e =>
        Number(e),
      );
      const pathList = Array.isArray(path) ? path.filter(e => e) : path.split("\n");
      const ruleItem = {
        enable: status,
        built_in_rules: false,
        user_name: configPosition(user_name),
        password_name: configPosition(password_name),
        token: configPosition(token),
        path: pathList,
        status_code: statusCode,
        built_in_password: ruleEditData.some(item => item.is_default === true),
        custom_password: configCustomPassword(ruleEditData),
      };
      const defaultRule = {
        enable: status,
        built_in_rules: isDefault,
      };
      const item = isDefault ? defaultRule : ruleItem;
      acc.push(item);
      return acc;
    }, []);
  };
  //配置自定义弱密码
  const configCustomPassword = list => {
    return list
      .filter(e => !e.is_default)
      .reduce((acc, cur) => {
        const { union_password, simple_password, encrypt_type } = cur;
        const items = (union_password || []).reduce((acc, cur) => {
          for (let key in cur) {
            if (key !== "group_type") {
              if (!acc[key]) {
                acc[key] = [];
              }
              acc[key] = Array.from(new Set(acc[key].concat(cur[key])));
            }
          }
          return acc;
        }, {});
        const type_list = encrypt_type && encrypt_type.length ? encrypt_type : [1];
        const ruleItem = {
          simple_passwords: simple_password,
          encrypt_type: type_list.reduce((acc, cur) => (acc += cur), 0),
          filter_instance_id: `weak-password-detection${Date.now()}`,
        };
        if (items.date_range)
          ruleItem.date_parts = {
            date_range: items.date_range,
            date_format: items.date_format.reduce((acc, cur) => (acc += cur), 0),
          };
        if (items.symbol) ruleItem.symbol_parts = items.symbol;
        if (items.text) ruleItem.text_parts = items.text;
        acc.push(ruleItem);
        return acc;
      }, []);
  };
  const configPosition = list => {
    return list.reduce((acc, cur) => {
      const item = cur.keys.map(e => ({
        position: e,
        fields: cur.values.split(","),
      }));
      acc.push(...item);
      return acc;
    }, []);
  };
  const submit = async () => {
    const reqData = configSubmitData(baseForm.value.ruleList);
    const params = {
      plugin_config: {
        check_config: reqData,
        buffer_limit: 1024,
      },
      meta_data: {
        form: baseForm.value,
      },
    };
    const extension = { refs: { wpd_rule_database: [] } };
    const ruleBaseList = baseForm.value.ruleList.map(e => e.rule_base).flat();
    extension.refs.wpd_rule_database = [...new Set(ruleBaseList)].map(item => {
      return {
        item_id: item,
      };
    });
    return {
      contentParams: params,
      extension,
    };
  };
  defineExpose({
    submit,
  });
  const remove = index => {
    baseForm.value.ruleList.splice(index, 1);
  };

  const initData = async () => {
    await getRuleBase({ page_num: 1, page_size: 50 });
    if (props.global) {
      const global = JSON.parse(props.global);
      let { form, highList, rule } = global.meta_data;
      // let { form, highList, rule } = weakPasswordDetection.auto.meta_data; // 测试历史数据
      if (highList) {
        console.log("highList", highList);
        form = compateData(highList, rule, baseSelect.value);
      }
      console.log("form", form);
      if (form) baseForm.value = form;
    }
  };
  initData();
</script>

<style lang="scss" scoped>
  .list-handle {
    height: 32px;
    display: flex;
    align-items: center;
  }
  .rule-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 20px;
    color: var(--ouryun-text-color-gray-1);
  }
  .flex-item {
    display: flex;
    width: 100%;
  }
  .form-item {
    width: 340px;
    margin-right: 8px;
  }
  .form-item-left {
    width: 260px;
    margin-right: 8px;
  }
  ::v-deep .ouryun-form-item {
    margin-bottom: 12px;
  }

  .form-item-ped {
    width: 100%;
    padding: 4px 0px;
  }
  .required-label::before {
    content: "*";
    color: var(--ouryun-color-red-base);
  }
  .handle-item-btn {
    display: flex;
    justify-content: flex-start; /* 垂直方向贴顶 */
  }
  :deep(.ouryun-opera-custom) {
    .ou-opear-content {
      padding-bottom: 20px !important;
    }
  }
  .textArea-form {
    margin-bottom: 0px !important;
  }
  :deep(.editor-container-wrapper):has(.is-error) {
    margin-bottom: -12px !important;
  }
</style>
