import request from "baseRequest";

// 获取编排服务端网关信息
export const getLayoutServerGatewayAPI = name => {
  return request({
    url: `/v1/gw/ENGINEID/${name}`,
    method: "get",
  });
};

// 新增编排服务端网关
export const addLayoutServerGatewayAPI = data => {
  return request({
    url: `/v1/gw/ENGINEID`,
    method: "post",
    data,
  });
};

// 编辑编排服务端网关
export const updateLayoutServerGatewayAPI = (data, name) => {
  return request({
    url: `/v1/gw/ENGINEID/${name}`,
    method: "put",
    data,
  });
};
