import { computed } from "vue";
import usePluginStore from "@/store/modules/plugin.js";
import { useState } from "./index.js";

const { timeMap, verifyMap } = useState();
const store = usePluginStore();

const levelMap = {
  L1: "宽松",
  L2: "中等",
  L3: "严格",
};

const modeMap = {
  0: "自动",
  1: "紧急",
  2: "自定义",
};

const actionNameMap = {
  0: "拒绝",
  1: "放行",
  2: "人机验证",
};

// 频次，时间等文本格式化
const formatText = (data = {}) => {
  const { dateCount, dateUnit, count } = data;
  if (count === undefined || count === null) {
    return `${dateCount}${timeMap[dateUnit]}`;
  }
  return `${count}次/${dateCount}${timeMap[dateUnit]}`;
};

// 防护等级详细内容
const getModeContent = (defendMode, form) => {
  let temp = {};
  switch (defendMode) {
    case "0":
      return { 防护等级: levelMap[form?.defendLevel] };
    case "1":
      temp = { 人机验证: verifyMap[form?.verify] };
      if (form?.verify === "auto") {
        temp = { ...temp, 触发条件: formatText(form?.trigger) };
      }
      if (form?.verifyFail) {
        temp = { ...temp, 验证失败频率: formatText(form?.verifyFail) };
      }
      if (form?.astrictTime) {
        temp = { ...temp, 封锁时长: formatText(form.astrictTime) };
      }
      return temp;
    case "2":
      if (form?.visitFrequency) {
        temp = { ...temp, 访问频率: formatText(form?.visitFrequency) };
      }
      temp = { ...temp, 动作: `${actionNameMap[form.action]}` };

      if (form.action === "2") {
        temp = { ...temp, 验证失败频率: formatText(form?.verifyFail) };
      }
      if (form.action !== "1") {
        temp = { ...temp, 封锁时长: formatText(form.astrictTime) };
      }
      return temp;
    default:
      return {};
  }
};

const baseInfo = computed(() => {
  const { globalContent } = store.pluginConfig;
  if (globalContent) {
    const { meta_data } = JSON.parse(globalContent);
    let data = getModeContent(meta_data.form.defendMode, meta_data.form);
    return {
      defendMode: modeMap[meta_data.form.defendMode] || "-",
      defendLevel: data,
      whiteData: (meta_data.form.whiteData.list || []).map(e => ({
        companyName: e.companyName,
        status: e.status,
      })),
    };
  }
});

const keyLabels = [
  {
    label: "IP白名单",
    columnspan: 3,
    key: "ipWhite",
  },
];

const baseData = computed(() => {
  const { defendLevel, defendMode, whiteData } = baseInfo.value;
  return {
    defendMode: defendMode,
    defendLevel: defendLevel["防护等级"],
    ipWhite: whiteData,
  };
});

const boundList = computed(() => {
  if (!store.pluginConfig || !store.pluginConfig.refAttachs) return [];
  return store.pluginConfig.refAttachs.map(item => {
    return {
      serviceName: item.displayName || "-",
      domainMatch: item.domains || "-",
    };
  });
});
export const useDetail = () => {
  return {
    baseInfo,
    boundList,
    keyLabels,
    baseData,
  };
};
